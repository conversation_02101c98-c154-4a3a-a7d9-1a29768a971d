import { defineConfig } from 'tsup';

export default defineConfig({
  // Entry points
  entry: {
    index: 'src/index.ts',
    env: 'env.ts',
  },

  // Build formats
  format: ['cjs', 'esm'],

  // Output file extensions
  outExtension({ format }) {
    return {
      js: format === 'cjs' ? '.js' : '.mjs',
    };
  },

  // Generate .d.ts files
  dts: true,

  // Generate sourcemaps
  sourcemap: true,

  // Clean output directory before build
  clean: true,

  // Split into chunks by default
  splitting: false,

  // Define environment variables
  env: {
    NODE_ENV: process.env.NODE_ENV || 'production',
  },

  // Don't bundle node_modules
  external: ['@willow/db', 'zod', 'react', 'react-dom'],

  // Define platform - changed to neutral to support both browser and node
  platform: 'neutral',

  // Enable JSX support
  // @ts-expect-error tsup types are wrong
  jsx: 'react',
});
