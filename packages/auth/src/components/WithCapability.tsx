import type { ReactNode } from 'react';

import type { Capability } from '../capabilities';
import type { ProfileWithAdminRole } from '../hooks/useCapabilities';
import { useCapabilities } from '../hooks/useCapabilities';

export interface WithCapabilityProps {
  /**
   * The capability or capabilities required to render the children
   */
  requiredCapabilities: Capability[] | Capability;

  /**
   * Whether all capabilities are required (AND) or any capability is sufficient (OR)
   * @default false (OR)
   */
  requireAll?: boolean;

  /**
   * Whether to hide the content when the user doesn't have the required capabilities
   * @default true (content will be hidden)
   * If false, the content will be rendered but disabled (for interactive elements)
   */
  hideWhenDisabled?: boolean;

  /**
   * The children to render if the user has the required capabilities
   */
  children: ReactNode;

  /**
   * Optional fallback component to render if the user doesn't have the required capabilities
   * and hideWhenDisabled is true
   * @default null
   */
  fallback?: ReactNode;

  /**
   * Optional className to apply to the wrapper div when content is disabled but not hidden
   * Only applies when hideWhenDisabled is false
   */
  disabledClassName?: string;

  /**
   * The user profile object that contains admin role information
   * If not provided, the component will always render the fallback
   */
  profile: ProfileWithAdminRole | null | undefined;
}

/**
 * A flexible wrapper component that conditionally renders its children based on the user's capabilities.
 * This can be used to wrap any UI element (buttons, links, forms, sections, etc.) to control visibility
 * based on the user's role and permissions.
 *
 * @example
 * // Hide a button if the user doesn't have the required capability
 * <WithCapability
 *   profile={userProfile}
 *   requiredCapabilities={Capability.CREATE_ADMINS}
 * >
 *   <Button onClick={handleCreateAdmin}>Create Admin</Button>
 * </WithCapability>
 *
 * @example
 * // Disable a form section if the user doesn't have the required capability
 * <WithCapability
 *   profile={userProfile}
 *   requiredCapabilities={[Capability.EDIT_PATIENT_INFO, Capability.EDIT_PATIENT_ADDRESSES]}
 *   requireAll={true}
 *   hideWhenDisabled={false}
 *   disabledClassName="opacity-50 pointer-events-none"
 * >
 *   <PatientInfoForm />
 * </WithCapability>
 */
export const WithCapability = ({
  profile,
  requiredCapabilities,
  requireAll = false,
  hideWhenDisabled = true,
  children,
  fallback = null,
  disabledClassName = 'opacity-50 pointer-events-none',
}: WithCapabilityProps) => {
  const { hasAnyCapability, hasAllCapabilities } = useCapabilities(profile);

  // Convert single capability to array
  const capabilities = Array.isArray(requiredCapabilities)
    ? requiredCapabilities
    : [requiredCapabilities];

  // Check if the user has the required capabilities
  const hasRequiredCapabilities = requireAll
    ? hasAllCapabilities(capabilities)
    : hasAnyCapability(capabilities);

  // If the user has the required capabilities, render the children
  if (hasRequiredCapabilities) {
    return <>{children}</>;
  }

  // If the content should be hidden when disabled, render the fallback or null
  if (hideWhenDisabled) {
    return <>{fallback}</>;
  }

  // If the content should be disabled but not hidden, render it with the disabled class
  return <div className={disabledClassName}>{children}</div>;
};
