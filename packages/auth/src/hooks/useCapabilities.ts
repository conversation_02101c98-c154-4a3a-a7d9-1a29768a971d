import { useMemo } from 'react';

import type { Capability } from '../capabilities';
import type { AdminRole } from '../roles';
import {
  hasAllCapabilities as roleHasAllCapabilities,
  hasAnyCapability as roleHasAnyCapability,
  hasCapability as roleHasCapability,
} from '../roles';

/**
 * Type for the profile object that contains admin role information
 */
export interface ProfileWithAdminRole {
  admin?: {
    role?: AdminRole;
  };
}

/**
 * Hook to check if the current admin has specific capabilities
 *
 * @param profile - The user profile object that contains admin role information
 * @returns Object with functions to check capabilities
 */
export const useCapabilities = (
  profile: ProfileWithAdminRole | null | undefined,
) => {
  const adminRole = profile?.admin?.role;

  // Memoize the hasCapability function to avoid recreating it on each render
  const hasCapability = useMemo(() => {
    return (capability: Capability): boolean => {
      if (!adminRole) return false;
      return roleHasCapability(adminRole, capability);
    };
  }, [adminRole]);

  const hasAnyCapability = useMemo(() => {
    return (requiredCapabilities: Capability[] | Capability): boolean => {
      if (!adminRole) return false;
      const capabilities = Array.isArray(requiredCapabilities)
        ? requiredCapabilities
        : [requiredCapabilities];
      return roleHasAnyCapability(adminRole, capabilities);
    };
  }, [adminRole]);

  const hasAllCapabilities = useMemo(() => {
    return (requiredCapabilities: Capability[] | Capability): boolean => {
      if (!adminRole) return false;
      const capabilities = Array.isArray(requiredCapabilities)
        ? requiredCapabilities
        : [requiredCapabilities];
      return roleHasAllCapabilities(adminRole, capabilities);
    };
  }, [adminRole]);

  return {
    hasCapability,
    hasAnyCapability,
    hasAllCapabilities,
  };
};
