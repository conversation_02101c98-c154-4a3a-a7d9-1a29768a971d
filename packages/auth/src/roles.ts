import { Capability } from './capabilities';

/**
 * Define AdminRole enum to match the one in the database schema
 */
export enum AdminRole {
  superAdmin = 'superAdmin',
  admin = 'admin',
  doctor = 'doctor',
}

/**
 * Define the capabilities assigned to each admin role
 */
export const ROLE_CAPABILITIES: Record<AdminRole, Capability[]> = {
  // Super admin has all capabilities
  [AdminRole.superAdmin]: Object.values(Capability),

  // Regular admin has most capabilities except full admin management
  [AdminRole.admin]: [
    // Admin management (limited)
    Capability.MANAGE_ADMINS,
    Capability.CREATE_ADMINS,
    Capability.DEACTIVATE_ADMINS,
    Capability.REACTIVATE_ADMINS,
    Capability.VIEW_ADMINS,
    Capability.CREATE_ADMINS,
    Capability.EDIT_ADMINS,

    //Pharmacy management
    Capability.VIEW_PHARMACIES,
    Capability.TRANSFER_PHARMACY_PATIENTS,
    Capability.TRANSFER_PATIENT_PHARMACY,

    // Patient managements
    Capability.VIEW_DASHBOARD,
    Capability.MANAGE_PATIENTS,
    Capability.VIEW_PATIENTS,
    Capability.EDIT_PATIENT_INFO,
    Capability.EDIT_PATIENT_ADDRESSES,
    Capability.RESET_PATIENT_PASSWORD,
    Capability.TRANSFER_PATIENT_DOCTOR,
    Capability.TRANSFER_PATIENT_PHARMACY,
    Capability.CREATE_PATIENT_NOTES,
    Capability.DELETE_PATIENTS,
    Capability.VIEW_MESSAGES,

    // Treatment management
    Capability.MANAGE_TREATMENTS,
    Capability.VIEW_TREATMENTS,
    Capability.CREATE_TREATMENTS,
    Capability.CANCEL_TREATMENTS,
    Capability.FIRE_NEXT_TREATMENT,
    Capability.MOVE_REFILL_DATE,

    // Billing management
    Capability.MANAGE_BILLING,
    Capability.VIEW_BILLING,
    Capability.PROCESS_REFUNDS,
    Capability.MANAGE_PAYMENT_METHODS,
  ],

  // Doctor role has patient operations similar to admin, but cannot manage other admins/doctors
  [AdminRole.doctor]: [
    // Admin management (limited) //
    Capability.MANAGE_ADMINS,
    Capability.CREATE_ADMINS,
    Capability.DEACTIVATE_ADMINS,
    Capability.REACTIVATE_ADMINS,
    Capability.VIEW_ADMINS,
    Capability.CREATE_ADMINS,
    Capability.EDIT_ADMINS,

    // Doctor managements
    Capability.MANAGE_DOCTORS,
    Capability.VIEW_DOCTORS,
    Capability.CREATE_DOCTORS,
    Capability.EDIT_DOCTORS,
    Capability.DEACTIVATE_DOCTORS,
    Capability.REACTIVATE_DOCTORS,

    // Patient managements
    Capability.VIEW_DASHBOARD,
    Capability.MANAGE_PATIENTS,
    Capability.VIEW_PATIENTS,
    Capability.EDIT_PATIENT_INFO,
    Capability.EDIT_PATIENT_ADDRESSES,
    Capability.RESET_PATIENT_PASSWORD,
    Capability.TRANSFER_PATIENT_DOCTOR,
    Capability.TRANSFER_PATIENT_PHARMACY,
    Capability.CREATE_PATIENT_NOTES,
    Capability.DELETE_PATIENTS,
    Capability.VIEW_MESSAGES,
  ],
};

/**
 * Check if a role has a specific capability
 */
export function hasCapability(
  role: AdminRole,
  capability: Capability,
): boolean {
  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
  return ROLE_CAPABILITIES[role]?.includes(capability) || false;
}

/**
 * Check if a role has any of the provided capabilities
 */
export function hasAnyCapability(
  role: AdminRole,
  capabilities: Capability[],
): boolean {
  return capabilities.some((capability) => hasCapability(role, capability));
}

/**
 * Check if a role has all of the provided capabilities
 */
export function hasAllCapabilities(
  role: AdminRole,
  capabilities: Capability[],
): boolean {
  return capabilities.every((capability) => hasCapability(role, capability));
}

/**
 * Get all capabilities for a specific role
 */
export function getCapabilitiesForRole(role: AdminRole): Capability[] {
  return ROLE_CAPABILITIES[role];
}
