import { Capability } from './capabilities';

export const AdminCapabilityGroups = {
  VIEW: [Capability.VIEW_ADMINS],
  MANAGE: [
    Capability.MANAGE_ADMINS,
    Capability.VIEW_ADMINS,
    Capability.CREATE_ADMINS,
    Capability.EDIT_ADMINS,
    Capability.DEACTIVATE_ADMINS,
    Capability.REACTIVATE_ADMINS,
    Capability.DELETE_ADMINS,
  ],
};

export const SuperAdminCapabilityGroups = {
  MANAGE: [Capability.MANAGE_SUPER_ADMINS, Capability.VIEW_INSIGHTS],
};

export const DoctorCapabilityGroups = {
  VIEW: [Capability.VIEW_DOCTORS],
  MANAGE: [
    Capability.MANAGE_DOCTORS,
    Capability.VIEW_DOCTORS,
    Capability.CREATE_DOCTORS,
    Capability.EDIT_DOCTORS,
    Capability.DEACTIVATE_DOCTORS,
    Capability.REACTIVATE_DOCTORS,
  ],
};

export const PatientCapabilityGroups = {
  VIEW: [Capability.VIEW_PATIENTS],
  MANAGE: [
    Capability.MANAGE_PATIENTS,
    Capability.VIEW_PATIENTS,
    Capability.EDIT_PATIENT_INFO,
    Capability.EDIT_PATIENT_ADDRESSES,
    Capability.RESET_PATIENT_PASSWORD,
    Capability.TRANSFER_PATIENT_DOCTOR,
    Capability.TRANSFER_PATIENT_PHARMACY,
    Capability.CREATE_PATIENT_NOTES,
    Capability.DELETE_PATIENTS,
  ],
};

export const PharmacyCapabilityGroups = {
  VIEW: [Capability.VIEW_PHARMACIES],
  MANAGE: [
    Capability.MANAGE_PHARMACIES,
    Capability.VIEW_PHARMACIES,
    Capability.CREATE_PHARMACIES,
    Capability.EDIT_PHARMACIES,
    Capability.DEACTIVATE_PHARMACIES,
    Capability.REACTIVATE_PHARMACIES,
    Capability.DELETE_PHARMACIES,
    Capability.TRANSFER_PHARMACY_PATIENTS,
  ],
};

export const ProductCapabilityGroups = {
  VIEW: [Capability.VIEW_PRODUCTS],
  MANAGE: [
    Capability.MANAGE_PRODUCTS,
    Capability.VIEW_PRODUCTS,
    Capability.CREATE_PRODUCTS,
    Capability.EDIT_PRODUCTS,
    Capability.ACTIVATE_PRODUCTS,
    Capability.DEACTIVATE_PRODUCTS,
    Capability.DELETE_PRODUCTS,
  ],
};

export const ProductPriceCapabilityGroups = {
  VIEW: [Capability.VIEW_PRODUCT_PRICES],
  MANAGE: [
    Capability.MANAGE_PRODUCT_PRICES,
    Capability.VIEW_PRODUCT_PRICES,
    Capability.CREATE_PRODUCT_PRICES,
    Capability.EDIT_PRODUCT_PRICES,
    Capability.DELETE_PRODUCT_PRICES,
  ],
};

export const ProductPriceEquivalenceCapabilityGroups = {
  VIEW: [Capability.VIEW_PRODUCT_PRICE_EQUIVALENCE],
  MANAGE: [
    Capability.MANAGE_PRODUCT_PRICE_EQUIVALENCE,
    Capability.VIEW_PRODUCT_PRICE_EQUIVALENCE,
    Capability.CREATE_PRODUCT_PRICE_EQUIVALENCE,
    Capability.EDIT_PRODUCT_PRICE_EQUIVALENCE,
    Capability.DELETE_PRODUCT_PRICE_EQUIVALENCE,
  ],
};

export const ProductPriceMappingCapabilityGroups = {
  VIEW: [Capability.VIEW_PRODUCT_PRICE_MAPPING],
  MANAGE: [
    Capability.MANAGE_PRODUCT_PRICE_MAPPING,
    Capability.VIEW_PRODUCT_PRICE_MAPPING,
    Capability.CREATE_PRODUCT_PRICE_MAPPING,
    Capability.EDIT_PRODUCT_PRICE_MAPPING,
    Capability.DELETE_PRODUCT_PRICE_MAPPING,
  ],
};

export const TreatmentCapabilityGroups = {
  VIEW: [Capability.VIEW_TREATMENTS],
  MANAGE: [
    Capability.MANAGE_TREATMENTS,
    Capability.VIEW_TREATMENTS,
    Capability.CREATE_TREATMENTS,
    Capability.CANCEL_TREATMENTS,
    Capability.FIRE_NEXT_TREATMENT,
    Capability.MOVE_REFILL_DATE,
  ],
};

export const BillingCapabilityGroups = {
  VIEW: [Capability.VIEW_BILLING],
  MANAGE: [
    Capability.MANAGE_BILLING,
    Capability.VIEW_BILLING,
    Capability.PROCESS_REFUNDS,
    Capability.MANAGE_PAYMENT_METHODS,
  ],
};

export const StateCapabilityGroups = {
  VIEW: [Capability.VIEW_STATES],
  MANAGE: [
    Capability.MANAGE_STATES,
    Capability.VIEW_STATES,
    Capability.EDIT_STATES,
  ],
};

export const DashboardCapabilityGroups = {
  VIEW: [Capability.VIEW_DASHBOARD],
};
