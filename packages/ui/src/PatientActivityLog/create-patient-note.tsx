import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@willow/ui/base/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@willow/ui/base/form';
import { Textarea } from '@willow/ui/base/textarea';

const zFormSchema = z.object({
  note: z.string().min(1, {
    message: 'Note is required',
  }),
});
type FormSchema = z.infer<typeof zFormSchema>;

export function CreatePatientNote({
  onCreateNote,
  isPending,
}: {
  onCreateNote: (
    variables: {
      note: string;
    },
    options?: {
      onSuccess?: () => void;
    },
  ) => Promise<void>;
  isPending?: boolean;
}) {
  const form = useForm<FormSchema>({
    resolver: zodResolver(zFormSchema),
    defaultValues: {
      note: '',
    },
  });

  async function onSubmitHandler(data: FormSchema) {
    await onCreateNote(data, {
      onSuccess: () => {
        form.reset({ note: '' });
      },
    });
  }

  return (
    <div className="sticky bottom-0 left-0 right-0 border-t-[1px] border-t-slate-300 bg-white">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmitHandler)}>
          <div className="flex w-full flex-row items-center gap-3 p-4">
            <div className="w-4/5">
              <FormField
                control={form.control}
                name="note"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Textarea {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <Button
              type="submit"
              variant="denimOutline"
              size="sm"
              className="w-1/5"
              loading={isPending}
              disabled={isPending}
            >
              Create Note
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
