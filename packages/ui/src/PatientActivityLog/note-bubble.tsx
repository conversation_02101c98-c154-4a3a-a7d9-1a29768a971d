import { intlFormat } from 'date-fns';

import type { AuditLogApi } from '@willow/utils/audit-log/index';
import type { ActorExtraDetails } from '@willow/utils/audit-log/types';

export const NoteBubble = ({
  note,
  date,
  actorType,
  actorDetails,
}: {
  note: string;
  date: Date;
  actorType: AuditLogApi['actorType'];
  actorDetails?: ActorExtraDetails;
}) => {
  return (
    <div className="flex flex-row gap-4">
      <div className="w-8" />
      <div className="flex flex-grow flex-col gap-2 rounded-lg border-[0.5px] border-[#1E1E1E] bg-orange-50 p-4 leading-none">
        <p className="whitespace-pre-wrap text-xs font-normal">
          {note.replace(/\\n/g, '\n')}
        </p>
        <div className="flex flex-row items-center justify-between">
          <div className="text-xs font-extrabold">
            {`${actorDetails?.firstName} ${actorDetails?.lastName} `}
            <span className="text-[10px] font-normal text-gray-500">
              {actorType.charAt(0) + actorType.slice(1).toLowerCase()}
            </span>
          </div>

          <div className="text-[10px] font-normal text-gray-500">
            {intlFormat(date, {
              dateStyle: 'short',
              timeStyle: 'short',
            })}
          </div>
        </div>
      </div>
    </div>
  );
};
