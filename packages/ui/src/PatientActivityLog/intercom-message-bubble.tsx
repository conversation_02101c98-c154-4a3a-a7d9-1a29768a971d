import { intlFormat } from 'date-fns';

import { cn } from '..';

interface IntercomMessageBubbleProps {
  title: string;
  date: Date;
  details: {
    author: {
      name: string;
      type: string;
    };
    content: string;
  };
}

const BubbleIndicator = ({
  type,
  authorName,
}: {
  type: 'admin' | 'contact';
  authorName: string | undefined;
}) => {
  return (
    <div
      className={cn(
        'flex aspect-square w-8 flex-shrink-0 items-center justify-center rounded-full bg-[#D9EBDB] pt-1 text-sm font-bold leading-none',
        type == 'admin'
          ? 'bg-denim text-white'
          : 'bg-stone-light text-black/70',
      )}
    >
      {authorName?.charAt(0).toUpperCase() ?? '?'}
    </div>
  );
};

export const AdminMessage = ({
  title,
  details,
  date,
}: IntercomMessageBubbleProps) => {
  return (
    <>
      <div className="text-bw-90 font-neue flex flex-grow flex-col gap-2 rounded-lg border-[0.5px] border-[#1E1E1E] bg-stone-light p-4 text-[13px] leading-none text-black">
        <h6 className="font-semibold">{title}</h6>

        <div className="my-2 flex flex-col gap-2">
          <div dangerouslySetInnerHTML={{ __html: details.content }} />
          <span className="font-bold">{details.author.name}</span>
        </div>

        <div className="text-bw-50 flex items-center gap-1 text-[11px] text-black">
          <span>
            {intlFormat(date, {
              dateStyle: 'short',
              timeStyle: 'short',
            })}
          </span>
        </div>
      </div>
      <BubbleIndicator type={'contact'} authorName={details.author.name} />
    </>
  );
};
export const ContactMessage = ({
  title,
  details,
  date,
}: IntercomMessageBubbleProps) => {
  return (
    <>
      <BubbleIndicator type={'admin'} authorName={details.author.name} />
      <div className="text-bw-90 font-neue flex flex-grow flex-col gap-2 rounded-lg border-[0.5px] border-[#1E1E1E] bg-denim p-4 text-[13px] leading-none text-white">
        <h6 className="font-semibold">{title}</h6>

        <div className="my-2 flex flex-col gap-2">
          <div dangerouslySetInnerHTML={{ __html: details.content }} />
        </div>

        <div className="text-bw-50 flex items-center gap-1 text-[11px] text-white">
          <span>
            {intlFormat(date, {
              dateStyle: 'short',
              timeStyle: 'short',
            })}
          </span>
        </div>
      </div>
    </>
  );
};

export const IntercomMessageBubble = ({
  title,
  date,
  details,
}: IntercomMessageBubbleProps) => {
  console.log('details');
  console.log(details);
  return (
    <div>
      <div className="flex items-start gap-4">
        {details.author.type == 'admin' && (
          <AdminMessage title={title} details={details} date={new Date(date)} />
        )}
        {details.author.type != 'admin' && (
          <ContactMessage
            title={title}
            details={details}
            date={new Date(date)}
          />
        )}
      </div>
    </div>
  );
};
