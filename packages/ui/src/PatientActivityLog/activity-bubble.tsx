import { intlFormat } from 'date-fns';

import { cn } from '@willow/ui';

interface ActivityBubbleProps {
  children?: React.ReactNode;
  title: string;
  state: 'success' | 'error';
  indicator: string;
  date: Date;
}

export const ActivityBubble = ({
  children,
  state = 'success',
  indicator = '✅',
  title,
  date,
}: ActivityBubbleProps) => {
  return (
    <div>
      <div className="flex items-start gap-4">
        <div
          className={cn(
            'flex aspect-square w-8 flex-shrink-0 items-center justify-center rounded-full pt-1 text-sm leading-none',
            state === 'success' ? 'bg-[#D9EBDB]' : 'bg-[#FFB1A933]',
          )}
        >
          {indicator}
        </div>
        <div className="text-bw-90 font-neue flex flex-grow flex-col gap-2 rounded-lg border-[0.5px] border-[#1E1E1E] p-4 text-[13px] leading-none">
          <h6 className="font-semibold">{title}</h6>
          {children ? (
            <div className="my-2 flex flex-col gap-2">{children}</div>
          ) : null}
          <div className="text-bw-50 flex items-center gap-1 text-[11px]">
            <span>
              {intlFormat(date, {
                dateStyle: 'short',
                timeStyle: 'short',
              })}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
