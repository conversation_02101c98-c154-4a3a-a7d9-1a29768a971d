import * as React from 'react';

import { cn } from '@willow/ui';

import { Textarea } from './textarea';

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  chatRef?: (node: HTMLDivElement | null) => void;
}

const ChatInput = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, chatRef, ...props }, ref) => {
    return (
      <div ref={chatRef} className="max-h-52">
        <Textarea
          className={cn(
            'w-full resize-none rounded-lg bg-transparent text-lg font-light text-denim placeholder:text-lg placeholder:text-white focus:outline-0 focus-visible:ring-0 focus-visible:ring-offset-0',
            className,
          )}
          ref={ref}
          {...props}
        />
      </div>
    );
  },
);

ChatInput.displayName = 'ChatInput';

export { ChatInput };
