#!/bin/bash
set -e

# Get credentials from .env file
if [ -f .env ]; then
  export $(grep -v '^#' .env | xargs)
  echo "Using credentials from .env file"
else
  echo "No .env file found, using default credentials"
  export POSTGRES_USER=postgres
  export POSTGRES_PASSWORD=postgres
fi

# Using default bridge network
echo "Using default bridge network"

# Check if replica is already running
if docker ps -f "name=willow-postgres-replica" --format '{{.Names}}' | grep -q "willow-postgres-replica"; then
  echo "Replica appears to be already running."
  echo "If you need to recreate it, run: docker compose down && docker compose --profile with-replica up -d"
  exit 0
fi

echo "===== Setting up PostgreSQL replication ====="

# Configure the primary (current postgres container)
echo "Configuring main PostgreSQL container for replication..."
docker exec willow-postgres psql -U $POSTGRES_USER -d willow -c "ALTER SYSTEM SET wal_level = 'replica';"
docker exec willow-postgres psql -U $POSTGRES_USER -d willow -c "ALTER SYSTEM SET max_wal_senders = '10';"
docker exec willow-postgres psql -U $POSTGRES_USER -d willow -c "ALTER SYSTEM SET max_replication_slots = '10';"
docker exec willow-postgres bash -c "echo 'host replication $POSTGRES_USER all trust' >> /var/lib/postgresql/data/pg_hba.conf"
docker exec willow-postgres psql -U $POSTGRES_USER -d willow -c "SELECT pg_reload_conf();"
docker exec willow-postgres psql -U $POSTGRES_USER -d willow -c "SELECT pg_create_physical_replication_slot('replica_slot');" || echo "Replication slot may already exist"

# Restart primary to ensure configuration is applied
echo "Restarting main PostgreSQL container..."
docker restart willow-postgres

# Wait for the primary to come back up
echo "Waiting for main PostgreSQL to be ready again..."
until docker exec willow-postgres pg_isready -U $POSTGRES_USER; do
  echo "Waiting for main PostgreSQL database to be up..."
  sleep 2
done
sleep 5

# Take a base backup using IP address instead of hostname
echo "Taking base backup from main PostgreSQL..."
# Get the IP of the postgres container
POSTGRES_IP=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' willow-postgres)
echo "Using PostgreSQL IP: $POSTGRES_IP"

rm -rf /tmp/pgbackup
mkdir -p /tmp/pgbackup
docker run --rm --network bridge -v /tmp/pgbackup:/backup postgres:16 \
  bash -c "PGPASSWORD=$POSTGRES_PASSWORD pg_basebackup -h $POSTGRES_IP -p 5432 -U $POSTGRES_USER -D /backup -Fp -Xs -P -v"

# Start the replica container
echo "Starting replica container..."
docker compose --profile with-replica up -d postgres-replica

# Wait for a moment to ensure container is created
sleep 5

# Wait for the container to be running
until [ "$(docker container inspect -f '{{.State.Running}}' willow-postgres-replica 2>/dev/null)" == "true" ]; do
  echo "Waiting for replica container to start..."
  sleep 2
done

# Stop the replica and replace its data with the backup
echo "Configuring replica with base backup..."
docker stop willow-postgres-replica

# Use the IP address for the primary connection
echo "Using PostgreSQL IP: $POSTGRES_IP"
docker run --rm -v willow_pgdata-replica:/data -v /tmp/pgbackup:/backup alpine \
  sh -c "rm -rf /data/* && cp -R /backup/* /data/ && touch /data/standby.signal && echo \"primary_conninfo = 'host=$POSTGRES_IP port=5432 user=$POSTGRES_USER password=$POSTGRES_PASSWORD application_name=replica'\" > /data/postgresql.auto.conf && echo \"primary_slot_name = 'replica_slot'\" >> /data/postgresql.auto.conf"

# Start the replica again
echo "Starting replica..."
docker start willow-postgres-replica

# Wait for replica to be ready
echo "Waiting for replica to be ready..."
sleep 10
until docker exec willow-postgres-replica pg_isready -U $POSTGRES_USER; do
  echo "Waiting for replica database to be up..."
  sleep 2
done

# Test the replication
echo -e "\n===== Testing Replication ====="
echo "Creating test table on primary..."
docker exec willow-postgres psql -U $POSTGRES_USER -d willow -c "CREATE TABLE IF NOT EXISTS replica_test (id SERIAL PRIMARY KEY, message TEXT);"
docker exec willow-postgres psql -U $POSTGRES_USER -d willow -c "INSERT INTO replica_test (message) VALUES ('Replication test');"

echo "Checking replication status..."
docker exec willow-postgres psql -U $POSTGRES_USER -d willow -c "SELECT * FROM pg_stat_replication;"

echo "Checking if data is on replica (may take a few seconds to replicate)..."
sleep 5
docker exec willow-postgres-replica psql -U $POSTGRES_USER -d willow -c "SELECT * FROM replica_test;" || echo "Data not yet replicated"

echo -e "\n===== PostgreSQL replication setup complete ====="
echo "Main database is available at localhost:5432"
echo "Replica database is available at localhost:5433"
echo ""
echo "To use the replica in your application, configure a separate connection to port 5433"
echo "To start your stack including the replica in the future, use: docker compose --profile with-replica up -d"