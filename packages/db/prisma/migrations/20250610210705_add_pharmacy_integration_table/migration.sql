-- CreateTable
CREATE TABLE "PharmacyIntegration" (
    "id" TEXT NOT NULL,
    "pharmacyId" TEXT NOT NULL,
    "prescriptionId" TEXT NOT NULL,
    "orderId" TEXT NOT NULL,
    "request" JSONB NOT NULL,
    "response" JSONB NOT NULL,
    "responseStatus" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PharmacyIntegration_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "PharmacyIntegration_pharmacyId_idx" ON "PharmacyIntegration"("pharmacyId");

-- CreateIndex
CREATE INDEX "PharmacyIntegration_prescriptionId_idx" ON "PharmacyIntegration"("prescriptionId");

-- CreateIndex
CREATE INDEX "PharmacyIntegration_orderId_idx" ON "PharmacyIntegration"("orderId");

-- CreateIndex
CREATE INDEX "PharmacyIntegration_createdAt_idx" ON "PharmacyIntegration"("createdAt");

-- CreateIndex
CREATE INDEX "PharmacyIntegration_response_idx" ON "PharmacyIntegration" USING GIN ("response" jsonb_path_ops);

-- AddForeignKey
ALTER TABLE "PharmacyIntegration" ADD CONSTRAINT "PharmacyIntegration_pharmacyId_fkey" FOREIGN KEY ("pharmacyId") REFERENCES "Pharmacy"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PharmacyIntegration" ADD CONSTRAINT "PharmacyIntegration_prescriptionId_fkey" FOREIGN KEY ("prescriptionId") REFERENCES "Prescription"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
