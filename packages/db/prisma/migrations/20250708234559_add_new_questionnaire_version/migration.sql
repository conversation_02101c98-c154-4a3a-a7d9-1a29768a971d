-- Add V2 Questionnaire with objectives step
INSERT INTO "Questionnaire" (id, version, name, type, config, "createdAt")
VALUES (
  gen_random_uuid(),
  2,
  'V2 Questionnaire',
  'onboarding',
  '{
    "meta": {
      "total": 15
    },
    "states": {
      "age": {
        "on": {
          "next": [
            {
              "guard": "isUnderAge",
              "target": "rejectedUnderAge"
            },
            {
              "guard": "isOverAge",
              "target": "rejectedOverAge"
            },
            {
              "target": "gender",
              "actions": "storeQuestionnaire"
            }
          ]
        },
        "meta": {
          "step": 1
        }
      },
      "gender": {
        "on": {
          "back": {
            "target": "age"
          },
          "male": {
            "target": "usingGLP1",
            "actions": {
              "type": "storeQuestionnaire",
              "params": {
                "gender": "male"
              }
            }
          },
          "female": {
            "target": "isPregnant",
            "actions": {
              "type": "storeQuestionnaire",
              "params": {
                "gender": "female"
              }
            }
          }
        },
        "meta": {
          "step": 2
        }
      },
      "isPregnant": {
        "on": {
          "no": {
            "target": "usingGLP1",
            "actions": {
              "type": "storeQuestionnaire",
              "params": {
                "isPregnant": "no"
              }
            }
          },
          "yes": {
            "target": "rejectedIsPregnant",
            "actions": {
              "type": "storeQuestionnaire",
              "params": {
                "isPregnant": "yes"
              }
            }
          },
          "back": {
            "target": "gender"
          }
        },
        "meta": {
          "step": 2
        }
      },
      "usingGLP1": {
        "on": {
          "no": {
            "target": "haveDiabetes",
            "actions": {
              "type": "storeQuestionnaire",
              "params": {
                "usingGLP1": "no"
              }
            }
          },
          "yes": {
            "target": "haveDiabetes",
            "actions": {
              "type": "storeQuestionnaire",
              "params": {
                "usingGLP1": "yes"
              }
            }
          },
          "back": {
            "target": "gender"
          }
        },
        "meta": {
          "step": 3
        }
      },
      "haveDiabetes": {
        "on": {
          "no": {
            "target": "eligible",
            "actions": {
              "type": "storeQuestionnaire",
              "params": {
                "haveDiabetes": "no"
              }
            }
          },
          "yes": {
            "target": "rejectedPriorConditions",
            "actions": {
              "type": "storeQuestionnaire",
              "params": {
                "haveDiabetes": "yes"
              }
            }
          },
          "back": {
            "target": "usingGLP1"
          }
        },
        "meta": {
          "step": 4
        }
      },
      "eligible": {
        "on": {
          "back": {
            "target": "haveDiabetes"
          },
          "next": {
            "target": "doctorVisits"
          }
        },
        "meta": {
          "step": 5
        }
      },
      "doctorVisits": {
        "on": {
          "no": {
            "target": "recommendSeeDoctor",
            "actions": {
              "type": "storeQuestionnaire",
              "params": {
                "doctorVisits": "no"
              }
            }
          },
          "yes": {
            "target": "qualifyingConditions",
            "actions": {
              "type": "storeQuestionnaire",
              "params": {
                "doctorVisits": "yes"
              }
            }
          },
          "back": {
            "target": "eligible"
          }
        },
        "meta": {
          "step": 6
        }
      },
      "recommendSeeDoctor": {
        "on": {
          "back": {
            "target": "doctorVisits"
          },
          "next": {
            "target": "qualifyingConditions"
          }
        },
        "meta": {
          "step": 6
        }
      },
      "qualifyingConditions": {
        "on": {
          "back": {
            "target": "doctorVisits"
          },
          "next": {
            "target": "height",
            "actions": "storeQuestionnaire"
          }
        },
        "meta": {
          "step": 7
        }
      },
      "height": {
        "on": {
          "back": {
            "target": "qualifyingConditions"
          },
          "next": {
            "target": "weight",
            "actions": "storeQuestionnaire"
          }
        },
        "meta": {
          "step": 8
        }
      },
      "weight": {
        "on": {
          "back": {
            "target": "height"
          },
          "next": {
            "target": "desiredWeight",
            "actions": "storeQuestionnaire"
          }
        },
        "meta": {
          "step": 9
        }
      },
      "desiredWeight": {
        "on": {
          "back": {
            "target": "weight"
          },
          "next": {
            "target": "objectives",
            "actions": "storeQuestionnaire"
          }
        },
        "meta": {
          "step": 10
        }
      },
      "objectives": {
        "on": {
          "back": {
            "target": "desiredWeight"
          },
          "next": {
            "target": "haveAllergies",
            "actions": "storeQuestionnaire"
          }
        },
        "meta": {
          "step": 11
        }
      },
      "haveAllergies": {
        "on": {
          "no": {
            "target": "medications",
            "actions": {
              "type": "storeQuestionnaire",
              "params": {
                "allergies": [],
                "hasAllergies": "no"
              }
            }
          },
          "yes": {
            "target": "selectAllergies",
            "actions": {
              "type": "storeQuestionnaire",
              "params": {
                "hasAllergies": "yes"
              }
            }
          },
          "back": {
            "target": "objectives"
          }
        },
        "meta": {
          "step": 12
        }
      },
      "selectAllergies": {
        "on": {
          "back": {
            "target": "haveAllergies"
          },
          "next": {
            "target": "medications",
            "actions": "storeQuestionnaire"
          }
        },
        "meta": {
          "step": 12
        }
      },
      "medications": {
        "on": {
          "back": {
            "target": "haveAllergies"
          },
          "next": {
            "target": "medicalConditions",
            "actions": "storeQuestionnaire"
          }
        },
        "meta": {
          "step": 13
        }
      },
      "medicalConditions": {
        "on": {
          "back": {
            "target": "medications"
          },
          "next": {
            "target": "additionalInformation",
            "actions": "storeQuestionnaire"
          }
        },
        "meta": {
          "step": 14
        }
      },
      "additionalInformation": {
        "on": {
          "back": {
            "target": "medicalConditions"
          },
          "next": {
            "target": "finished",
            "actions": "storeQuestionnaire"
          }
        },
        "meta": {
          "step": 15
        }
      },
      "finished": {
        "entry": {
          "type": "complete",
          "params": {
            "completed": true
          }
        },
        "always": "#onboarding.selectTreatmentType"
      },
      "rejectedUnderAge": {
        "on": {
          "back": {
            "target": "age",
            "actions": "clearRejected"
          }
        },
        "meta": {
          "step": 1
        },
        "entry": {
          "type": "reject",
          "params": {
            "reason": "under age"
          }
        }
      },
      "rejectedOverAge": {
        "on": {
          "back": {
            "target": "age",
            "actions": "clearRejected"
          }
        },
        "meta": {
          "step": 1
        },
        "entry": {
          "type": "reject",
          "params": {
            "reason": "over age"
          }
        }
      },
      "rejectedIsPregnant": {
        "on": {
          "back": {
            "target": "isPregnant",
            "actions": "clearRejected"
          }
        },
        "meta": {
          "step": 2
        },
        "entry": {
          "type": "reject",
          "params": {
            "reason": "is pregnant"
          }
        }
      },
      "rejectedPriorConditions": {
        "on": {
          "back": {
            "target": "haveDiabetes",
            "actions": "clearRejected"
          }
        },
        "meta": {
          "step": 4
        },
        "entry": {
          "type": "reject",
          "params": {
            "reason": "has prior conditions"
          }
        }
      }
    },
    "initial": "age"
  }'::jsonb,
  NOW()
);