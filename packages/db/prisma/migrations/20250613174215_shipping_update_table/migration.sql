-- CreateTable
CREATE TABLE "ShipmentUpdate" (
    "id" TEXT NOT NULL,
    "patientId" TEXT NOT NULL,
    "pharmacyId" TEXT,
    "status" TEXT NOT NULL,
    "trackingNumber" TEXT NOT NULL,
    "trackingLink" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ShipmentUpdate_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ShipmentUpdate_trackingNumber_idx" ON "ShipmentUpdate"("trackingNumber");

-- CreateIndex
CREATE INDEX "ShipmentUpdate_status_idx" ON "ShipmentUpdate"("status");

-- CreateIndex
CREATE INDEX "ShipmentUpdate_updatedAt_idx" ON "ShipmentUpdate"("updatedAt");

-- AddForeignKey
ALTER TABLE "ShipmentUpdate" ADD CONSTRAINT "ShipmentUpdate_patientId_fkey" FOREIGN KEY ("patientId") REFERENCES "Patient"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ShipmentUpdate" ADD CONSTRAINT "ShipmentUpdate_pharmacyId_fkey" FOREIGN KEY ("pharmacyId") REFERENCES "Pharmacy"("id") ON DELETE SET NULL ON UPDATE CASCADE;
