-- CreateTable
CREATE TABLE "ProductCategory"
(
    "id"               TEXT         NOT NULL,
    "name"             TEXT         NOT NULL,
    "form"             TEXT,
    "label"            TEXT,
    "tags"             TEXT,
    "shortDescription" TEXT,
    "description"      TEXT,
    "image"            TEXT,
    "customCard"       TEXT,
    "enabled"          BOOLEAN      NOT NULL DEFAULT true,
    "order"            INTEGER      NOT NULL DEFAULT 0,
    "createdAt"        TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt"        TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ProductCategory_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ProductCategory_form_idx" ON "ProductCategory" ("form");

-- Insert initial ProductCategory records based on existing ProductForms enum values
INSERT INTO "ProductCategory" ("id", "name", "form", "label", "tags", "shortDescription", "description", "image",
                               "customCard", "enabled", "order", "createdAt", "updatedAt")
VALUES ('2f06c413-bc14-474a-a366-3f4b892a3d35', 'tablet', 'tablet', 'Dissolvable Tablet', 'Most Popular',
        'Our dissolvable tablets provide a full dose of GLP-1 weight loss medication in a convenient, flavored tablet that dissolves under your tongue.',
        'Our dissolvable tablets provide a full dose of GLP-1 weight loss medication in a convenient, flavored tablet that dissolves under your tongue. Taken daily, these tablets are easy to store and travel with—no injections needed!',
        'https://local-products.startwillow.com/product-categories/2f06c413-bc14-474a-a366-3f4b892a3d35.jpg', 'tablet',
        true, 2, '2025-07-07 22:10:03.68', '2025-07-07 23:09:20.533'),
       ('3604a497-7ce1-4a53-ae98-9fc0bcab3c62', 'oral', 'oral', 'Oral', NULL,
        'Our oral GLP-1 treatment offers a convenient, easy-to-take option that fits seamlessly into your daily routine.',
        'Our oral GLP-1 treatment offers a convenient, easy-to-take option that fits seamlessly into your daily routine. Simply take your prescribed dose daily with a dropper under your tongue, and let the medication help you manage your weight effectively. Ideal for those who prefer a non-invasive, oral-based approach to weight management.',
        'https://local-products.startwillow.com/product-categories/3604a497-7ce1-4a53-ae98-9fc0bcab3c62.jpg', NULL,
        false, 4, '2025-07-07 22:10:03.68', '2025-07-07 23:11:49.929'),
       ('0816170d-c687-460d-b57a-2cb7da79ec4b', 'injectable', 'injectable', 'Injectable', NULL,
        'The injectable GLP-1 treatment offers a powerful and efficient way to manage your weight with the added benefit of weekly dosing.',
        'The injectable GLP-1 treatment offers a powerful and efficient way to manage your weight with the added benefit of weekly dosing. This option is perfect for those looking for a consistent release of medication over time. The injections are easy to administer, and you can do them in the comfort of your own home using standard syringes.',
        'https://local-products.startwillow.com/product-categories/0816170d-c687-460d-b57a-2cb7da79ec4b.jpg', NULL,
        true, 3, '2025-07-07 22:10:03.68', '2025-07-07 23:12:42.345'),
       ('ccf289f8-a32e-4c86-9caa-1ad819bf5ad2', '90 day program', 'tablet', '90 Day Program', 'New!', '',
        'Our signature 90-Day Program combines daily, fast-dissolving semaglutide tablets with a structured, 12-week habit-coaching curriculum and unlimited access to your board-certified doctor.',
        'https://local-products.startwillow.com/product-categories/ccf289f8-a32e-4c86-9caa-1ad819bf5ad2.png', '90-day',
        false, 1, '2025-07-07 23:33:29.385', '2025-07-07 23:33:44.037');

-- CreateTable: ProductToProductCategory pivot table
CREATE TABLE "ProductToProductCategory"
(
    "productId"         TEXT         NOT NULL,
    "productCategoryId" TEXT         NOT NULL,
    "assignedAt"        TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ProductToProductCategory_pkey" PRIMARY KEY ("productId", "productCategoryId")
);

-- CreateIndex
CREATE INDEX "ProductToProductCategory_productId_idx" ON "ProductToProductCategory" ("productId");

-- CreateIndex
CREATE INDEX "ProductToProductCategory_productCategoryId_idx" ON "ProductToProductCategory" ("productCategoryId");

-- AddForeignKey
ALTER TABLE "ProductToProductCategory"
    ADD CONSTRAINT "ProductToProductCategory_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductToProductCategory"
    ADD CONSTRAINT "ProductToProductCategory_productCategoryId_fkey" FOREIGN KEY ("productCategoryId") REFERENCES "ProductCategory" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- Populate pivot table: Link existing products to categories based on their form
INSERT INTO "ProductToProductCategory" ("productId", "productCategoryId")
SELECT p."id", pc."id"
FROM "Product" p
         JOIN "ProductCategory" pc ON p."form"::text = pc."form";

-- AlterTable
ALTER TABLE "Product"
    ADD COLUMN "customCard" TEXT;

UPDATE "Product"
SET "isCore" = false
WHERE "type" = 'additional';

UPDATE "Product"
SET "isCore" = true
WHERE "type" = 'core'
