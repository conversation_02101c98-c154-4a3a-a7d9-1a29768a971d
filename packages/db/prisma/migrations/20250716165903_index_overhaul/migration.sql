-- DropIndex
DROP INDEX "PharmacyIntegration_pharmacyId_idx";

-- DropIndex
DROP INDEX "ProductCategory_form_idx";

-- DropIndex
DROP INDEX "ProductToProductCategory_productCategoryId_idx";

-- DropIndex
DROP INDEX "ProductToProductCategory_productId_idx";

-- CreateIndex
CREATE INDEX "AuditLog_patientId_idx" ON "AuditLog" ("patientId");

-- CreateIndex
CREATE INDEX "Conversation_lastMessageFrom_idx" ON "Conversation" ("lastMessageFrom");

-- CreateIndex
CREATE INDEX "ConversationMessage_conversationId_idx" ON "ConversationMessage" ("conversationId");

-- CreateIndex
CREATE INDEX "ConversationMessage_treatmentId_idx" ON "ConversationMessage" ("treatmentId");

-- CreateIndex
CREATE INDEX "ConversationMessage_conversationRouterId_idx" ON "ConversationMessage" ("conversationRouterId");

-- CreateIndex
CREATE INDEX "ConversationMessage_userId_idx" ON "ConversationMessage" ("userId");

-- CreateIndex
CREATE INDEX "ConversationRouter_conversationId_idx" ON "ConversationRouter" ("conversationId");

-- CreateIndex
CREATE INDEX "DoctorAssignment_doctorId_idx" ON "DoctorAssignment" ("doctorId");

-- CreateIndex
CREATE INDEX "DoctorAssignment_patientId_idx" ON "DoctorAssignment" ("patientId");

-- CreateIndex
CREATE INDEX "DoctorAssignment_bulkTransferId_idx" ON "DoctorAssignment" ("bulkTransferId");

-- CreateIndex
CREATE INDEX "Outbox_patientId_idx" ON "Outbox" ("patientId");

-- CreateIndex
CREATE INDEX "Patient_status_createdAt_idx" ON "Patient" ("status", "createdAt");

-- CreateIndex
CREATE INDEX "Patient_canceledByUserId_idx" ON "Patient" ("canceledByUserId");

-- CreateIndex
CREATE INDEX "PatientFollowUp_treatmentId_idx" ON "PatientFollowUp" ("treatmentId");

-- CreateIndex
CREATE INDEX "PatientShippingAddress_patientId_idx" ON "PatientShippingAddress" ("patientId");

-- CreateIndex
CREATE INDEX "Prescription_patientId_idx" ON "Prescription" ("patientId");

-- CreateIndex
CREATE INDEX "Prescription_treatmentId_idx" ON "Prescription" ("treatmentId");

-- CreateIndex
CREATE INDEX "Referral_referrerUserId_idx" ON "Referral" ("referrerUserId");

-- CreateIndex
CREATE INDEX "ShipmentUpdate_patientId_idx" ON "ShipmentUpdate" ("patientId");
