-- AlterTable
ALTER TABLE "Pharmacy"
    RENAME COLUMN "priority" TO "regularPriority";

ALTER TABLE "Pharmacy"
    ADD COLUMN "color"             TEXT,
    ADD COLUMN "slug"              TEXT,
    ADD COLUMN "usingGLP1Priority" INTEGER NOT NULL DEFAULT 0;

-- Update slug from metadata if it exists
UPDATE "Pharmacy"
SET "slug" = metadata ->> 'slug'
WHERE metadata ->> 'slug' IS NOT NULL;

-- Set GLP-1 priority to the same value as regular priority
UPDATE "Pharmacy"
SET "usingGLP1Priority" = "regularPriority";

-- Update color based on slug using brand color mapping
UPDATE "Pharmacy"
SET "color" = CASE
                  WHEN "slug" = 'partell' THEN '#1B458E'
                  WHEN "slug" = 'redRock' THEN '#BA4646'
                  WHEN "slug" = 'empower' THEN '#914EB5'
                  WHEN "slug" = 'boothwyn' THEN '#62B469'
                  WHEN "slug" = 'epiqScripts' THEN '#21513E'
                  WHEN "slug" = 'strive' THEN '#635BFF'
                  ELSE '#505050'
    END
WHERE "slug" IS NOT NULL;

-- Create<PERSON>ndex
CREATE UNIQUE INDEX "Pharmacy_slug_key" ON "Pharmacy" ("slug");
