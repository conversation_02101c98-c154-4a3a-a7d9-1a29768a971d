-- CreateEnum
CREATE TYPE "conversationRouterStatus" AS ENUM ('pending', 'processing', 'processed', 'closed');

-- AlterTable
ALTER TABLE "ConversationMessage" ADD COLUMN     "conversationRouterId" TEXT;

-- CreateTable
CREATE TABLE "ConversationRouter" (
    "id" TEXT NOT NULL,
    "conversationId" TEXT NOT NULL,
    "intercomId" TEXT,
    "delayedUntil" TIMESTAMP(3) NOT NULL,
    "messages" JSONB,
    "reason" TEXT,
    "inquiryTypes" JSONB,
    "relevantForPatientServices" BOOLEAN,
    "relevantForDoctor" BOOLEAN,
    "status" "conversationRouterStatus" NOT NULL DEFAULT 'pending',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ConversationRouter_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ConversationRouter_status_delayedUntil_idx" ON "ConversationRouter"("status", "delayedUntil");

-- CreateIndex
CREATE INDEX "ConversationRouter_intercomId_idx" ON "ConversationRouter"("intercomId");

-- CreateIndex
CREATE INDEX "ConversationRouter_reason_idx" ON "ConversationRouter"("reason");

-- AddForeignKey
ALTER TABLE "ConversationMessage" ADD CONSTRAINT "ConversationMessage_conversationRouterId_fkey" FOREIGN KEY ("conversationRouterId") REFERENCES "ConversationRouter"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ConversationRouter" ADD CONSTRAINT "ConversationRouter_conversationId_fkey" FOREIGN KEY ("conversationId") REFERENCES "Conversation"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
