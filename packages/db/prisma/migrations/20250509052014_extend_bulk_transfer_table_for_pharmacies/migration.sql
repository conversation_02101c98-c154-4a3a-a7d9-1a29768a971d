-- CreateEnum
CREATE TYPE "bulkTransferType" AS ENUM ('doctor', 'pharmacy');

-- DropForeignKey
ALTER TABLE "BulkTransfer"
    DROP CONSTRAINT "BulkTransfer_doctorId_fkey";

-- AlterTable
ALTER TABLE "BulkTransfer"
    ADD COLUMN "pharmacyId" TEXT,
    ADD COLUMN "type"       "bulkTransferType" DEFAULT 'doctor',
    ALTER COLUMN "doctorId" DROP NOT NULL;

-- CreateIndex
CREATE INDEX "BulkTransfer_type_idx" ON "BulkTransfer" ("type");

-- AddForeignKey
ALTER TABLE "BulkTransfer"
    ADD CONSTRAINT "BulkTransfer_doctorId_fkey" FOREIGN KEY ("doctorId") REFERENCES "Doctor" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BulkTransfer"
    ADD CONSTRAINT "BulkTransfer_pharmacyId_fkey" FOREIGN KEY ("pharmacyId") REFERENCES "Pharmacy" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AlterTable
ALTER TABLE "BulkTransfer"
    ALTER COLUMN "type" DROP NOT NULL,
    ALTER COLUMN "type" DROP DEFAULT;

-- AlterEnum
ALTER TYPE "ConversationMessageType" ADD VALUE 'doctorNote';

-- Drop Tables not in use
-- DropForeignKey
ALTER TABLE "DoctorInviteCode" DROP CONSTRAINT "DoctorInviteCode_doctorId_fkey";

-- DropForeignKey
ALTER TABLE "DoctorInviteCode" DROP CONSTRAINT "DoctorInviteCode_generatedByAdmin_fkey";

-- DropTable
DROP TABLE "DoctorInviteCode";

-- DropTable
DROP TABLE "Lead";