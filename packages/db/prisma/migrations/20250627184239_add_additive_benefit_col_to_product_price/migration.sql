-- AlterTable
ALTER TABLE "ProductPrice"
    ADD COLUMN "additiveBenefit" TEXT;

-- Update additive benefits based on pharmacy name and product label
-- Empower
UPDATE "ProductPrice" pp
SET "additiveBenefit" = 'It''s compounded with cobalamin, also known as vitamin B12. Vitamin B12 is an important vitamin that helps create DNA and red blood cells. It helps with increased energy and also helps improve the health of skin, hair, and nails. Vitamin B12 also can help with cognitive functioning. Please note, this will make the injection appear pinkish/red in hue.'
FROM "Product" p
         JOIN "Pharmacy" ph ON p."pharmacyId" = ph.id
WHERE pp."productId" = p.id
  AND ph.name = 'Empower'
  AND p.form = 'injectable'
  AND p.label = 'Semaglutide';

UPDATE "ProductPrice" pp
SET "additiveBenefit" = 'It''s compounded with niacinamide, also known as vitamin B3 which can help improve energy, keep your nervous system healthy, improve skin, and lower cholesterol.'
FROM "Product" p
         JOIN "Pharmacy" ph ON p."pharmacyId" = ph.id
WHERE pp."productId" = p.id
  AND ph.name = 'Empower'
  AND p.form = 'injectable'
  AND p.label = 'Tirzepatide';

-- Red Rock
UPDATE "ProductPrice" pp
SET "additiveBenefit" = 'It''s compounded with glycine, an amino acid that helps preserve muscle mass during weight loss.'
FROM "Product" p
         JOIN "Pharmacy" ph ON p."pharmacyId" = ph.id
WHERE pp."productId" = p.id
  AND ph.name = 'Red Rock'
  AND p.form = 'injectable'
  AND p.label = 'Semaglutide';