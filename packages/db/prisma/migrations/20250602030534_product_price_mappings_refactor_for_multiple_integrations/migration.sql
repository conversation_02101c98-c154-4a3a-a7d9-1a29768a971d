-- DropIndex
DROP INDEX "ProductPriceMapping_pharmacyId_idx";
DROP INDEX "ProductPriceMapping_productPriceId_source_externalId_key";

-- Step 1: Add the new metadata column
ALTER TABLE "ProductPriceMapping"
    ADD COLUMN "metadata" JSONB;

-- Step 2: Populate metadata with existing data from quantity, directions, and source fields
UPDATE "ProductPriceMapping"
SET "metadata" = jsonb_build_object(
        'quantity', COALESCE("quantity", 1),
        'directions', COALESCE("directions", ''),
        'source', COALESCE("source"::text, '')
                 )
WHERE "metadata" IS NULL;

-- Step 3: Drop the old columns and pharmacyId column
ALTER TABLE "ProductPriceMapping"
    DROP COLUMN "directions",
    DROP COLUMN "quantity",
    DROP COLUMN "source",
    DROP COLUMN "pharmacyId";

-- DropEnum
DROP TYPE "MappingSource";

-- CreateIndex - Final unique constraint without pharmacyId
CREATE UNIQUE INDEX "ProductPriceMapping_externalId_productPriceId_key" ON "ProductPriceMapping" ("externalId", "productPriceId");