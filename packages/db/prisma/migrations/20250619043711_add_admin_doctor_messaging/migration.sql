ALTER TYPE "ConversationStatus" ADD VALUE 'open';
ALTER TYPE "ConversationStatus" ADD VALUE 'closed';

-- DropIndex
DROP INDEX "Conversation_patientId_key";

-- DropIndex
DROP INDEX "Conversation_userId_key";

-- AlterTable
ALTER TABLE "Conversation"
    ADD COLUMN "assignedAdminId" TEXT,
    ADD COLUMN "closedAt"        TIMESTAMP(3);

-- AlterTable
ALTER TABLE "User"
    ADD COLUMN "conversationId" TEXT;

-- CreateIndex
CREATE INDEX "Conversation_assignedAdminId_idx" ON "Conversation" ("assignedAdminId");

-- CreateIndex
CREATE UNIQUE INDEX "Conversation_patientId_type_key" ON "Conversation" ("patientId", "type");

-- AddForeignKey
ALTER TABLE "User"
    ADD CONSTRAINT "User_conversationId_fkey" FOREIGN KEY ("conversationId") REFERENCES "Conversation" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Conversation"
    ADD CONSTRAINT "Conversation_assignedAdminId_fkey" FOREIGN KEY ("assignedAdminId") REFERENCES "User" ("id") ON DELETE SET NULL ON UPDATE CASCADE;
