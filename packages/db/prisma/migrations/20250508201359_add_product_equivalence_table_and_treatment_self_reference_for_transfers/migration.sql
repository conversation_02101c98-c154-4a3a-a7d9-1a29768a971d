/*
  Warnings:

  - A unique constraint covering the columns `[transferredTo]` on the table `Treatment` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "ProductPrice" ADD COLUMN     "equivalenceGroupId" TEXT;

-- AlterTable
ALTER TABLE "Treatment" ADD COLUMN     "transferredTo" TEXT;

-- CreateTable
CREATE TABLE "ProductPriceEquivalenceGroup" (
    "id" TEXT NOT NULL,
    "name" TEXT,

    CONSTRAINT "ProductPriceEquivalenceGroup_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ProductPrice_productId_equivalenceGroupId_idx" ON "ProductPrice"("productId", "equivalenceGroupId");

-- CreateIndex
CREATE UNIQUE INDEX "Treatment_transferredTo_key" ON "Treatment"("transferredTo");

-- AddForeignKey
ALTER TABLE "ProductPrice" ADD CONSTRAINT "ProductPrice_equivalenceGroupId_fkey" FOREIGN KEY ("equivalenceGroupId") REFERENCES "ProductPriceEquivalenceGroup"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Treatment" ADD CONSTRAINT "Treatment_transferredTo_fkey" FOREIGN KEY ("transferredTo") REFERENCES "Treatment"("id") ON DELETE SET NULL ON UPDATE CASCADE;
