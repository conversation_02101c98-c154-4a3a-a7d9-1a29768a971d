-- CreateEnum
CREATE TYPE "ProductGenericName" AS ENUM ('semaglutide', 'tirzepatide', 'ondansetron');

-- AlterTable
ALTER TABLE "Product"
    ADD COLUMN "genericName" "ProductGenericName";

-- Populate genericName based on label values
UPDATE "Product"
SET "genericName" = CASE
                        WHEN label IN ('Semaglutide', 'Semaglutide (Acc)', 'Semaglutide High Dose', 'ODT 90 Day')
                            THEN 'semaglutide'::"ProductGenericName"
                        WHEN label IN ('Tirzepatide', 'Tirzepatide High Dose') THEN 'tirzepatide'::"ProductGenericName"
                        WHEN label = 'Ondansetron' THEN 'ondansetron'::"ProductGenericName"
                        ELSE 'semaglutide'::"ProductGenericName" -- default to semaglutide for any unmapped values
    END
WHERE "genericName" IS NULL;

-- Set NOT NULL constraint after populating the data
ALTER TABLE "Product"
    ALTER COLUMN "genericName" SET NOT NULL,
    ALTER COLUMN "genericName" SET DEFAULT 'semaglutide'::"ProductGenericName";
