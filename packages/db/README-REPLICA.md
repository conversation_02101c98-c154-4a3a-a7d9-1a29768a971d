# PostgreSQL Read Replica Setup

This document explains how to set up a PostgreSQL read replica alongside your existing Willow development environment.

## Overview

The setup consists of:

- Your existing primary PostgreSQL server (port 5432)
- A new read-only replica PostgreSQL server (port 5433)
- Streaming replication to keep them synchronized

## Quick Start

### First-time Setup

1. Make sure your regular Willow environment is running (`docker-compose up -d`)
2. Run the replication setup script:
   ```bash
   ./scripts/setup-replica.sh
   ```
3. The script will:
   - Configure your existing PostgreSQL server for replication
   - Take a base backup of your database
   - Set up a read-only replica on port 5433
   - Establish streaming replication between them

### Regular Usage

After the initial setup, you can start your environment with or without the replica:

#### Regular startup (without replica)

```bash
docker-compose up -d
```

#### Startup with replica

```bash
docker-compose --profile with-replica up -d
```

## Credentials

The replica uses the same credentials as your main PostgreSQL server:

- The script automatically reads your PostgreSQL username and password from the `.env` file
- If no `.env` file is found, it defaults to `postgres` / `postgres`

## Using the Replica

In your application:

1. Configure a separate database connection for read-only operations:
   - Host: localhost
   - Port: 5433
   - Username: (same as main database)
   - Password: (same as main database)
   - Database: willow
2. Use the main connection (port 5432) for all write operations
3. Use the replica connection (port 5433) for read-heavy queries

## Cleaning Up

To stop and remove the replica:

```bash
# Stop replica only
docker stop willow-postgres-replica
```

```bash
# Or, stop everything but don't include replica on restart
docker-compose down
```

## Verifying Replication

1. Create a test user on the primary:

   ```bash
   docker exec willow-postgres psql -U user -d willow -c "INSERT INTO \"User\" (id, type, \"firstName\", \"lastName\", email, \"createdAt\") VALUES ('test-replication-id', 'patient', 'Replication', 'Test', '<EMAIL>', now()) RETURNING id;"
   ```

2. Verify it appears on the replica (may take a moment):

   ```bash
   docker exec willow-postgres-replica psql -U user -d willow -c "SELECT id, \"firstName\", \"lastName\", email FROM \"User\" WHERE id = 'test-replication-id';"
   ```

3. Delete the test user after verification:

   ```bash
   docker exec willow-postgres psql -U user -d willow -c "DELETE FROM \"User\" WHERE id = 'test-replication-id';"
   ```

4. Check replication status (optional):
   ```bash
   docker exec willow-postgres psql -U user -d willow -c "SELECT * FROM pg_stat_replication;"
   ```

## Troubleshooting

If the replica stops working, the simplest approach is to reset it:

1. Stop everything:

   ```bash
   docker-compose down
   ```

2. Start everything without the replica:

   ```bash
   docker-compose up -d
   ```

3. Run the setup script again:
   ```bash
   ./scripts/setup-replica.sh
   ```
