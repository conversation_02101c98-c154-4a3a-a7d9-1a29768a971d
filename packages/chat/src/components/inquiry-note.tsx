import type { PartialConversationRouter } from '../types';

export function InquiryNote({
  showPatientServicesNotification,
  forUserType,
  conversationRouter,
}: {
  showPatientServicesNotification: boolean;
  forUserType?: string;
  conversationRouter?: PartialConversationRouter;
}) {
  if (!showPatientServicesNotification || !conversationRouter) return;
  if (forUserType == 'doctor') {
    return <InquiryNoteForDoctor conversationRouter={conversationRouter} />;
  } else if (forUserType == 'patient') {
    return <InquiryNoteForPatient conversationRouter={conversationRouter} />;
  } else {
    return;
  }
}

function InquiryNoteForDoctor({
  conversationRouter,
}: {
  conversationRouter?: PartialConversationRouter;
}) {
  const relevantForDoctor = conversationRouter?.relevantForDoctor;
  const relevantForPatientServices =
    conversationRouter?.relevantForPatientServices;

  if (relevantForDoctor && !relevantForPatientServices) {
    // Only relevant for doctor, show nothing
    return null;
  }
  if (!relevantForDoctor && relevantForPatientServices) {
    // Only relevant for patient services
    return (
      <div className="pl-2 text-xs italic text-amber-600">
        This inquiry has been forwarded to patient services and doctor action
        not needed
      </div>
    );
  }
  if (relevantForDoctor && relevantForPatientServices) {
    // Relevant for both
    return (
      <div className="pl-2 text-xs italic text-amber-600">
        This inquiry has been forwarded to patient services and{' '}
        <span className="font-bold"> doctor action is needed</span>
      </div>
    );
  }
  if (!relevantForDoctor && !relevantForPatientServices) {
    // Not relevant for either
    return (
      <div className="pl-2 text-xs italic text-amber-600">
        No doctor action needed for this inquiry
      </div>
    );
  }
}

function InquiryNoteForPatient({
  conversationRouter,
}: {
  conversationRouter?: PartialConversationRouter;
}) {
  const relevantForDoctor = conversationRouter?.relevantForDoctor;
  const relevantForPatientServices =
    conversationRouter?.relevantForPatientServices;

  if (!relevantForDoctor && relevantForPatientServices) {
    // Only relevant for patient services
    return (
      <div className="pl-2 text-xs italic text-amber-600">
        Forwarded to Patient Services
      </div>
    );
  }
  if (relevantForDoctor && relevantForPatientServices) {
    // Relevant for both
    return (
      <div className="pl-2 text-xs italic text-amber-600">
        Partially forwarded to Patient Services
      </div>
    );
  }
  return null;
}
