'use client';

import { useRef, useState } from 'react';
import Image from 'next/image';
import { zodResolver } from '@hookform/resolvers/zod';
import imageCompression from 'browser-image-compression';
import { PaperclipIcon } from 'lucide-react';
import { useForm } from 'react-hook-form';
import useMeasure from 'react-use-measure';
import { z } from 'zod';

import type { Conversation } from '@willow/chat';
import { useSendMessage, useUploadFile } from '@willow/chat';
import { Button } from '@willow/ui/base/button';
import { ChatInput } from '@willow/ui/base/chat-input';
import { Dialog, DialogContent } from '@willow/ui/base/dialog';
import { Form, FormControl, FormField, FormItem } from '@willow/ui/base/form';
import { formatFilename } from '@willow/utils/format';
import { useInvalidatedQuery } from '@willow/utils/react-query';

const schema = z.object({
  message: z.string().min(1),
});

export const MessageInput = ({
  conversation,
  onConversationAssigned,
}: {
  conversation: Conversation;
  onConversationAssigned?: () => void;
}) => {
  const apiUrl = process.env.NEXT_PUBLIC_API_URL ?? '';
  const { mutateAsync: sendMessage, isPending: isSubmittingMessage } =
    useSendMessage(apiUrl);
  const { mutateAsync: uploadFile, isPending: isUploadingFile } =
    useUploadFile(apiUrl);
  const invalidatedQuery = useInvalidatedQuery();

  const [chatRef, bounds] = useMeasure();
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [isSendingImage, setIsSendingImage] = useState(false);

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      message: '',
    },
  });

  const onSubmit = async (data: z.infer<typeof schema>) => {
    const { message } = data;

    await sendMessage(
      {
        conversationId: conversation.id,
        content: message,
        contentType: 'text',
        needsReply: false,
        type: 'doctorAdmin',
      },
      {
        onSuccess: () => {
          void invalidatedQuery(['admin-doctor-conversations']);
          void invalidatedQuery(['admin-doctor-conversation', conversation.id]);
          void invalidatedQuery(['chat', conversation.id]);
          onConversationAssigned?.();
        },
      },
    );

    form.setValue('message', '');
    inputRef.current?.focus();
  };

  const submitImage = async (image: File) => {
    try {
      setIsSendingImage(true);
      const processedImage = await imageCompression(image, {
        maxWidthOrHeight: 1200,
        useWebWorker: true,
        fileType: 'image/jpeg',
        initialQuality: 0.8,
      });

      await uploadFile({
        conversationId: conversation.id,
        filename: image.name,
        file: processedImage,
      });
      await sendMessage(
        {
          conversationId: conversation.id,
          content: image.name,
          contentType: 'image',
          needsReply: false,
          type: 'doctorAdmin',
        },
        {
          onSuccess: () => {
            void invalidatedQuery(['admin-doctor-conversations']);
            void invalidatedQuery([
              'admin-doctor-conversation',
              conversation.id,
            ]);
            void invalidatedQuery(['chat', conversation.id]);
            onConversationAssigned?.();
          },
        },
      );
      setIsSendingImage(false);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (e) {
      setIsSendingImage(false);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.key === 'Enter' && (event.metaKey || event.ctrlKey)) {
      event.preventDefault();
      void form.handleSubmit(onSubmit)();
    }
  };

  return (
    <div>
      <div className="h-[1px] bg-[#E0E0E0] px-2" />
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="relative flex h-fit w-full bg-white py-6 md:border-0"
          style={{ height: bounds.height + 20 }}
        >
          <div className="flex shrink grow basis-0 pr-4">
            <FormField
              control={form.control}
              name="message"
              render={({ field }) => (
                <FormItem className="relative w-full text-xl font-medium">
                  <FormControl>
                    <ChatInput
                      autoFocus={true}
                      chatRef={chatRef}
                      {...field}
                      ref={inputRef}
                      placeholder="Write a message.."
                      className="text-sm placeholder:text-sm placeholder:text-gray-400 focus-visible:outline-none"
                      onKeyDown={handleKeyDown}
                    />
                  </FormControl>

                  <div className="absolute right-2 top-0 hover:opacity-80">
                    <input
                      className="hidden"
                      id="upload-image"
                      name="image"
                      type="file"
                      accept="image/*"
                      onChange={(event) => {
                        const file = event.target.files?.[0];
                        if (file) {
                          setSelectedImage(formatFilename(file));
                          event.target.value = '';
                        }
                      }}
                    />

                    <label htmlFor="upload-image" className="cursor-pointer">
                      <PaperclipIcon className="h-4 w-4 text-gray-500" />
                    </label>
                  </div>
                </FormItem>
              )}
            />
          </div>

          <div className="flex gap-2">
            <Button
              size="sm"
              variant="denim"
              className="text-xs text-white"
              type="submit"
              loading={isSubmittingMessage || isUploadingFile}
            >
              SEND MESSAGE
            </Button>
          </div>
        </form>
      </Form>

      {selectedImage && (
        <Dialog open={true} onOpenChange={() => setSelectedImage(null)}>
          <DialogContent className="w-full max-w-[699px]">
            <div className="flex flex-col items-center justify-center gap-10">
              <div className="font-['Neue Haas Grotesk Display Pro'] text-center text-3xl font-medium text-slate-600">
                <Image
                  alt="Upload image"
                  src={URL.createObjectURL(selectedImage)}
                  width={100}
                  height={100}
                  className="h-auto max-h-60 w-full"
                />
              </div>

              <div className="flex gap-5">
                <Button
                  size="sm"
                  className="text-xs"
                  variant="denimOutline"
                  onClick={() => setSelectedImage(null)}
                  disabled={isSendingImage}
                >
                  Cancel
                </Button>
                <Button
                  size="sm"
                  variant="denim"
                  className="text-xs text-white"
                  onClick={async () => {
                    await submitImage(selectedImage);
                    setSelectedImage(null);
                  }}
                  disabled={isSendingImage}
                >
                  Send Image
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};
