import { useState } from 'react';
import Image from 'next/image';

import type { Message } from '@willow/chat';
import { useChat } from '@willow/chat';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@willow/ui/base/dialog';

export function ImgMessage({ message }: { message: Message }) {
  const [imageModalOpen, setImageModalOpen] = useState(false);
  const { s3Url } = useChat();

  const filename = message.content.split('/').pop() ?? '';
  const imageUrl = `${s3Url}/${message.content}`;
  return (
    <>
      <div onClick={() => setImageModalOpen(true)} className="cursor-pointer">
        <Image
          alt={filename}
          width={200}
          height={200}
          src={imageUrl}
          className="w-60 md:w-72"
          objectFit="contain"
        />
      </div>

      <Dialog open={imageModalOpen} onOpenChange={setImageModalOpen}>
        <DialogContent className="flex h-[95vh] max-h-[95vh] w-[95vw] max-w-[95vw] flex-col p-0">
          <DialogHeader className="border-b bg-white p-4">
            <DialogTitle>{filename}</DialogTitle>
          </DialogHeader>
          <div className="relative w-full flex-1 overflow-hidden">
            <Image
              src={imageUrl}
              alt={filename}
              layout="fill"
              objectFit="contain"
            />
          </div>
          <DialogFooter className="bg-white p-1" />
        </DialogContent>
      </Dialog>
    </>
  );
}
