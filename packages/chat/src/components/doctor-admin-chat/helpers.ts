import { format, isToday, isYesterday } from 'date-fns';

import type { Message } from '@willow/chat';

function formatDateLabel(date: Date): string {
  if (isToday(date)) {
    return `Today, ${format(date, 'MMMM d')}`;
  } else if (isYesterday(date)) {
    return `Yesterday, ${format(date, 'MMMM d')}`;
  } else {
    return format(date, 'EEEE, MMMM d');
  }
}

export function groupMessagesByDay(
  messages: Message[],
): Record<string, Message[]> {
  return messages.reduce(
    (groups, message) => {
      const dayKey = formatDateLabel(new Date(message.createdAt));

      if (!groups[dayKey]) {
        groups[dayKey] = [];
      }

      groups[dayKey].push(message);

      return groups;
    },
    {} as Record<string, Message[]>,
  );
}
