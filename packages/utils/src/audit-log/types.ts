import { z } from 'zod';

export const zAuditLogEntity = z.enum([
  'COMMAND',
  'SYSTEM',
  'PATIENT',
  'DOCTOR',
  'ADMIN',
  'TREATMENT',
  'FOLLOW_UP',
  'CONVERSATION',
]);

export const zActivityAction = z.enum([
  // Patient
  'PATIENT_ACCOUNT_CREATED',
  'PATIENT_VISIT_STARTED',
  'PATIENT_REJECTED',
  'PATIENT_ID_PHOTO_UPDATED',
  'PATIENT_CANCELLED',
  'PATIENT_UNCANCELLED',
  'PATIENT_IDENTITY_ACCEPTED',
  'PATIENT_ACCEPTED',
  'PATIENT_DELETED',
  'PATIENT_REASSIGNED_DOCTOR',
  'PATIENT_INVOICE_PAID',
  'PATIENT_INVOICE_PAYMENT_FAILED',

  'PATIENT_PROFILE_INFO_UPDATED',
  'PATIENT_SHIPPING_ADDRESS_UPDATED',
  'PATIENT_BILLING_ADDRESS_UPDATED',
  'PATIENT_PAYMENT_INFO_UPDATED',
  'PATIENT_INFO_UPDATED',
  'PATIENT_REFUND_ON_CANCEL',
  'PATIENT_REFUND_ISSUED',
  'PRESCRIPTION_TRANSFERRED',

  'USER_PASSWORD_RESET_REQUESTED',
  'USER_PASSWORD_RESET',
  'PATIENT_NOTE_CREATED',

  // Onboarding
  'ONBOARDING_UPLOAD_ID_PHOTO_SKIPPED',
  'ONBOARDING_TREATMENT_FORM_SELECTED',
  'ONBOARDING_TREATMENT_SELECTED',
  'ONBOARDING_SHIPPING_ADDRESS_UPDATED',
  'ONBOARDING_CHECKOUT_STARTED',
  'ONBOARDING_CHECKOUT_COMPLETED',
  'ONBOARDING_QUESTIONNAIRE_COMPLETED',

  // Follow-up
  'FOLLOW_UP_CREATED',
  'FOLLOW_UP_RESCHEDULED',
  'FOLLOW_UP_SENT',
  'FOLLOW_UP_STARTED',
  'FOLLOW_UP_COMPLETED',
  'FOLLOW_UP_REVIEWED_BY_DOCTOR',
  'FOLLOW_UP_CANCELLED',

  // Treatment
  'TREATMENT_CREATED',
  'TREATMENT_INVOICE_PAYMENT_FAILED', // Deprecated: use PATIENT_INVOICE_PAYMENT_FAILED
  'TREATMENT_INVOICE_PAID', // Deprecated: use PATIENT_INVOICE_PAID
  'TREATMENT_ORDER_SENT',
  'TREATMENT_ORDER_FAILED',
  'TREATMENT_ORDER_SENT_MANUALLY',
  'TREATMENT_RESUMED',
  'TREATMENT_PAUSED',
  'TREATMENT_CANCELLED',
  'TREATMENT_FIRED_NEXT',
  'TREATMENT_REFILL_DATE_MOVED',
  'TREATMENT_RETRY_FAILED_PAYMENT',

  // Chat
  'CONVERSATION_CHAT_MESSAGE_CREATED',

  //Intercom
  'INTERCOM_CONVERSATION_MESSAGE_CREATED',
]);

export const zActorExtraDetails = z.object({
  computer_username: z.string().optional(),
  firstName: z.string(),
  lastName: z.string(),
  userId: z.string(),
  ipAddress: z.string().optional(),
  userAgent: z.string().optional(),
});
export type ActorExtraDetails = z.infer<typeof zActorExtraDetails>;

export const zUserAuditLogActor = z.object({
  actorType: z.enum([
    zAuditLogEntity.Values.DOCTOR,
    zAuditLogEntity.Values.PATIENT,
    zAuditLogEntity.Values.ADMIN,
  ]),
  actorId: z.string(),
  actorExtraDetails: zActorExtraDetails,
});

export const zSystemAuditLogActor = z.object({
  actorType: z.literal(zAuditLogEntity.Values.SYSTEM),
  actorId: z.string(),
});
