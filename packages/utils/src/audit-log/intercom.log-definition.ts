import { z } from 'zod';

import { zActivityAction, zAuditLogEntity } from './types';

export const zIntercomConversationChatMesssageCreatedAuditLog = z.object({
  patientId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.CONVERSATION),
  resourceId: z.string(),
  action: z.literal(
    zActivityAction.Values.INTERCOM_CONVERSATION_MESSAGE_CREATED,
  ),
  details: z.object({
    content: z.string(),
    author: z.object({
      id: z.string(),
      type: z.string(),
      name: z.string(),
      email: z.string(),
    }),
  }),
});

export const intercomAuditLogDefinitions = [
  zIntercomConversationChatMesssageCreatedAuditLog,
] as const;
