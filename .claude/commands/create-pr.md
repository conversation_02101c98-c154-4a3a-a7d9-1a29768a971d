# Create a PR following the requested PR title and description format.

## PR Template

Title: `{ticket-id} - {meaningful-title}`, for example: `PROD-1104 - Cancel patient button`

```markdown
# Context 📝

https://app.clickup.com/t/**********/{ticket-id}

# What Changed 🔧

- {Short bullet list of changes}

# Verification ✅

1. {Steps to verify changes}
```

Don't add any indication of <PERSON> in the PR

## Follow these steps:

1. Get the ClickUp URL from $ARGUMENTS, if not present construct it from the branch name
   using https://app.clickup.com/t/**********/{custom-id}, with branch names for example `PROD-7132-fix-next-dose` where
   `PROD-7132` is the custom id, if not, prompt for it. Sometimes the branch name is _exactly_ the custom id. Try not to prompt unless necessary.
2. Get the ClickUp task title and description from the ClickUp API
3. Compare the current branch with `origin/staging`
4. Using the ClickUp task title and description, the diff from the branch comparison, and the PR template, create a PR
   with the correct title and description
5. Prompt for confirmation before creating the PR

Important note on the PR description:
It's not rare that the original ClickUp description is not 100% followed. The PR description should reflect the actual
changes in the code, not just the ClickUp description.