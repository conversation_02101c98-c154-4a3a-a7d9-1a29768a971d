# Mark a ticket as ready for testing, add QA instructions

https://app.clickup.com/t/9013445150/{ticket-id}

Don't add any indication of <PERSON> in the PR

## Follow these steps:

1. Get the custom-id from the branch name, for example for PROD-7132-fix-next-dose, PROD-7132 is the custom id
2. Check if there's a PR for the current branch, and if so, get the PR description
3. If there's a PR, get the PR diff with origin/staging, if not, get the diff from the current branch with
   origin/staging
4. Construct QA instructions from the PR description and the diff.
   a. QA instructions should be a list of steps to verify the changes, starting with ### QA Steps
   b. Keep steps concise - use only main numbered steps, avoid sub-bullets unless absolutely necessary
   c. Each step should be a single line describing one action or verification
   d. Only split complex verifications (like Segment events) into 2-3 specific items to check
   e. If there's new data to be set, instruct to set it in the admin if possible
   f. QA only works with the frontend apps, Stripe and Segment. These are non-technical instructions
   g. Aim for 5-10 steps total if possible, including desktop/mobile testing as the final step. Try to be concise, QA
   can always choose to test more if needed
5. Prompt for confirmation of the generated QA instructions
6. If confirmed, add the QA instructions to ClickUp ticket as a new comment
7. Mark the ticket as ready for testing in ClickUp

Important note on the description:
When building the description, the actual diff is more important than the PR description.