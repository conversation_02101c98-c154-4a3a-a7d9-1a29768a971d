# TBD

having all this info in ALL contexts is a bit of an overkill, new packages are not created often. This are the
instructions to configure them correctly

## Shared Packages

The monorepo contains several shared packages with different configurations based on their usage:

### Package Types

1. **Built packages (dual format)** - `@willow/auth`: Uses tsup to build both ESM and CommonJS for cross-environment
   compatibility
2. **Source-only packages** - `@willow/utils`, `@willow/db`: Export TypeScript source directly, transpiled by consuming
   apps
3. **UI packages** - `@willow/ui`: Frontend-only components using transpilePackages

### Creating Built Packages (Frontend + Backend)

For packages used by both Next.js frontends and NestJS backend (like `@willow/auth`):

#### 1. Package Configuration

```json
{
  "name": "@willow/package-name",
  "version": "0.1.0",
  "private": true,
  "license": "MIT",
  "main": "./dist/index.js",
  "module": "./dist/index.mjs",
  "types": "./dist/index.d.ts",
  "files": [
    "dist/**",
    "src/**"
  ],
  "exports": {
    ".": {
      "types": "./dist/index.d.ts",
      "import": "./dist/index.mjs",
      "require": "./dist/index.js"
    }
  },
  "scripts": {
    "build": "tsup",
    "prepare": "pnpm build",
    "dev": "tsup --watch"
  }
}
```

#### 2. Build Configuration (tsup.config.ts)

```typescript
import { defineConfig } from 'tsup';

export default defineConfig({
  entry: { index: 'src/index.ts' },
  format: ['cjs', 'esm'],
  outExtension({ format }) {
    return { js: format === 'cjs' ? '.js' : '.mjs' };
  },
  dts: true,
  sourcemap: true,
  clean: true,
  external: ['@willow/db'], // External dependencies
  platform: 'neutral'
});
```

#### 3. Frontend Integration

Add to Next.js `transpilePackages` for hot reloading:

```javascript
// next.config.mjs
{
  transpilePackages: ['@willow/package-name']
}
```

#### 4. Backend Integration (Development)

For source-level debugging in development, update API's tsconfig.json:

```json
{
  "paths": {
    "@willow/package-name": [
      "../../packages/package-name/src/index.ts"
    ]
  }
}
```

### Creating Source-Only Packages (Frontend-Only or Direct Export)

For packages like `@willow/utils` that export TypeScript source directly:

#### 1. Package Configuration

```json
{
  "name": "@willow/package-name",
  "type": "module",
  "exports": {
    "./*": "./src/*.ts"
  },
  "scripts": {
    "dev": "tsc",
    "typecheck": "tsc --noEmit"
  }
}
```

#### 2. Frontend Integration

Add to Next.js `transpilePackages`:

```javascript
// next.config.mjs
{
  transpilePackages: ['@willow/package-name']
}
```

### Development Workflow

#### For Built Packages with Backend Usage:

1. **Start package in watch mode**: `pnpm -F package-name dev`
2. **Start API**: `pnpm -F api dev` (will use source files for debugging)
3. **Start frontend**: `pnpm -F app-name dev` (will use transpilePackages)

#### For Source-Only Packages:

1. **Start frontend**: `pnpm -F app-name dev` (transpiles automatically)
2. No build step needed - changes are live

### Key Principles

1. **Frontends**: Use `transpilePackages` for all shared packages
2. **Backend development**: Use TypeScript path mapping to source files for debugging
3. **Backend production**: Uses built CommonJS files from dist/
4. **Build packages**: Only when backend compatibility is required
5. **Source packages**: For frontend-only or when build complexity isn't needed

This setup ensures packages can be used seamlessly in both the NestJS API (CommonJS) and the Next.js frontends (ESM)
without maintaining separate code for each environment.
