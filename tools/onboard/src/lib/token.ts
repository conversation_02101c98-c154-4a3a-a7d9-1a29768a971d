import { existsSync, readFileSync, unlinkSync, writeFileSync } from 'fs';
import { join } from 'path';

interface TokenCache {
  accessToken: string;
  doctorId: string;
  expiresAt: string; // ISO string date
}

const TOKEN_FILE_PATH = join(process.cwd(), '.token');

export function saveToken(accessToken: string, doctorId: string): void {
  // Token expires in 1 hour
  const expiresAt = new Date(Date.now() + 60 * 60 * 1000).toISOString();

  const tokenCache: TokenCache = {
    accessToken,
    doctorId,
    expiresAt,
  };

  try {
    writeFileSync(TOKEN_FILE_PATH, JSON.stringify(tokenCache, null, 2));
  } catch (error: unknown) {
    // Ignore errors when saving token cache
    console.warn('Failed to save token cache:', (error as Error).message);
  }
}

export function loadToken(): { accessToken: string; doctorId: string } | null {
  try {
    const tokenData = readFileSync(TOKEN_FILE_PATH, 'utf-8');
    const tokenCache = JSON.parse(tokenData) as TokenCache;

    // Check if token is still valid
    const now = new Date();
    const expiresAt = new Date(tokenCache.expiresAt);

    if (now < expiresAt) {
      return {
        accessToken: tokenCache.accessToken,
        doctorId: tokenCache.doctorId,
      };
    } else {
      // Token expired, return null
      return null;
    }
  } catch {
    // File doesn't exist or is corrupted, return null
    return null;
  }
}

export function clearToken(): void {
  try {
    if (existsSync(TOKEN_FILE_PATH)) {
      unlinkSync(TOKEN_FILE_PATH);
    }
  } catch {
    // Ignore errors when clearing token cache
  }
}
