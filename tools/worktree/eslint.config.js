import baseConfig from '@willow/eslint-config/base';

/** @type {import("typescript-eslint").Config} */
export default [
  {
    ignores: [],
  },
  ...baseConfig,
  {
    languageOptions: {
      parserOptions: {
        project: ['./tsconfig.json'],
        tsconfigRootDir: import.meta.dirname,
      },
    },
    rules: {
      '@typescript-eslint/no-unused-vars': 'off',
      'turbo/no-undeclared-env-vars': 'off',
      '@typescript-eslint/require-await': 'off',
    },
  },
];
