{"name": "@willow/worktree", "private": true, "version": "0.0.1", "type": "module", "scripts": {"start:setup": "pnpm --silent with-env tsx src/setup.ts", "start:setup:debug": "pnpm --silent with-env tsx --inspect-brk=9231 src/setup.ts", "start:list": "pnpm --silent with-env tsx src/list.ts", "start:list:debug": "pnpm --silent with-env tsx --inspect-brk=9231 src/list.ts", "start:teardown": "pnpm --silent with-env tsx src/teardown.ts", "start:teardown:debug": "pnpm --silent with-env tsx --inspect-brk=9231 src/teardown.ts", "purge": "git clean -xdf .cache .turbo node_modules", "clean": "git clean -xdf .cache .turbo", "format": "prettier --check . --ignore-path ../../../.gitignore", "format:fix": "prettier --check --write . --ignore-path ../../../.gitignore", "lint": "eslint", "lint:fix": "eslint --fix", "typecheck": "tsc --noEmit", "with-env": "dotenv -e ../../${ENV_FILE:-.env} --"}, "dependencies": {"@inquirer/prompts": "^7.0.1", "@willow/db": "workspace:*", "aws-sdk": "^2.1547.0", "axios": "^1.7.9", "chalk": "^4.1.2", "dayjs": "^1.11.13", "ora": "^5.4.1", "stripe": "^14.12.0", "tsx": "^4.7.1"}, "devDependencies": {"@willow/eslint-config": "workspace:*", "@willow/prettier-config": "workspace:*", "@willow/tsconfig": "workspace:*", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}}