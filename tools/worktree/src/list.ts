import { exec } from 'child_process';
import fs from 'fs/promises';
import net from 'net';
import path from 'path';
import { promisify } from 'util';
import { search } from '@inquirer/prompts';
import chalk from 'chalk';
import { Command } from 'commander';

const execAsync = promisify(exec);

// Configuration
const WORKTREE_BASE_DIR = '/Users/<USER>/Willow-worktrees';
const MAIN_REPO_DIR = '/Users/<USER>/Willow';

const styles = {
  header: chalk.bold.cyan,
  success: chalk.green,
  warning: chalk.yellow,
  error: chalk.red,
  info: chalk.cyan,
  muted: chalk.gray,
  command: chalk.magenta,
};

process.on('SIGINT', () => {
  process.exit(0);
});

interface EnvironmentData {
  number: number;
  name: string;
  isMain: boolean;
  path: string;
  patientsPort: string;
  doctorsPort: string;
  adminPort: string;
  apiPort: string;
}

/**
 * Open URL in default browser
 */
async function openInBrowser(url: string): Promise<void> {
  try {
    await execAsync(`open "${url}"`);
  } catch (error) {
    console.error(styles.error(`Failed to open ${url}:`, error));
  }
}

/**
 * Open WebStorm in specified directory
 */
async function openWebStorm(dirPath: string): Promise<void> {
  try {
    await execAsync(`webstorm "${dirPath}"`);
  } catch (error) {
    console.error(styles.error(`Failed to open WebStorm:`, error));
  }
}

/**
 * Open VS Code in specified directory
 */
async function openVSCode(dirPath: string): Promise<void> {
  try {
    await execAsync(`code "${dirPath}"`);
  } catch (error) {
    console.error(styles.error(`Failed to open VS Code:`, error));
  }
}

/**
 * Check if an application is available
 */
async function isAppAvailable(appName: string): Promise<boolean> {
  try {
    await execAsync(`osascript -e 'tell application "${appName}" to get name'`);
    return true;
  } catch (_error) {
    return false;
  }
}

/**
 * Get the best available terminal application
 */
async function getBestTerminalApp(): Promise<string> {
  // Check in order of preference: Ghostty -> iTerm2 -> Terminal
  if (await isAppAvailable('Ghostty')) {
    return 'Ghostty';
  }
  if (await isAppAvailable('iTerm')) {
    return 'iTerm';
  }
  return 'Terminal';
}

/**
 * Open terminal and attach/start tmux session
 */
async function openTerminalWithTmux(env: EnvironmentData): Promise<void> {
  try {
    // Determine session name
    let sessionName: string;
    if (env.isMain) {
      sessionName = 'willow';
    } else {
      // Extract worktree name from environment name
      const match = /\(([^)]+)\)$/.exec(env.name);
      const worktreeName = match ? match[1] : env.name;
      sessionName = `willow-${worktreeName}`;
    }

    // Check if session is already running
    const sessionRunning = await tmuxSessionIsRunning(
      env.isMain ? 'willow' : sessionName.replace('willow-', ''),
    );

    // Get the best available terminal app
    const terminalApp = await getBestTerminalApp();

    const tmuxCommand = sessionRunning
      ? `tmux attach -d -t ${sessionName}`
      : `tmuxinator start ${sessionName}`;

    if (terminalApp === 'iTerm') {
      // iTerm2 specific script
      await execAsync(
        `osascript -e 'tell application "iTerm" to activate' -e 'tell application "iTerm" to create window with default profile' -e 'tell current session of current window of application "iTerm" to write text "${tmuxCommand}"'`,
      );
    } else {
      // Ghostty and Terminal use the same approach with System Events
      await execAsync(
        `osascript -e 'tell application "${terminalApp}" to activate' -e 'tell application "System Events" to keystroke "t" using command down' -e 'delay 0.5' -e 'tell application "System Events" to keystroke "${tmuxCommand}" & return'`,
      );
    }
  } catch (error) {
    console.error(styles.error(`Failed to open terminal with tmux:`, error));
  }
}

/**
 * Check if a directory exists
 */
async function directoryExists(dirPath: string): Promise<boolean> {
  try {
    const stats = await fs.stat(dirPath);
    return stats.isDirectory();
  } catch (_error) {
    return false;
  }
}

/**
 * Check if a port is responding
 */
async function isPortResponding(port: string): Promise<boolean> {
  if (port === 'unknown') return false;

  const portNum = parseInt(port, 10);
  if (isNaN(portNum)) return false;

  return new Promise((resolve) => {
    const socket = new net.Socket();
    const timeout = 2000; // 2 second timeout

    socket.setTimeout(timeout);

    socket.connect(portNum, 'localhost', () => {
      socket.destroy();
      resolve(true);
    });

    socket.on('error', () => {
      resolve(false);
    });

    socket.on('timeout', () => {
      socket.destroy();
      resolve(false);
    });
  });
}

/**
 * Get all worktree directories using git worktree list
 */
async function getWorktreeDirs(): Promise<string[]> {
  try {
    const { stdout } = await execAsync(
      'cd /Users/<USER>/Willow && git worktree list',
    );
    const lines = stdout.trim().split('\n');
    const dirs: string[] = [];

    for (const line of lines) {
      // Parse git worktree list output: path commit [branch]
      const [path] = line.split(/\s+/);
      if (path && path !== MAIN_REPO_DIR) {
        dirs.push(path);
      }
    }

    return dirs;
  } catch (_error) {
    return [];
  }
}

/**
 * Get branch information for a worktree
 */
async function getBranchInfo(worktreeDir: string): Promise<string> {
  try {
    // Check if .git file exists
    const gitFilePath = path.join(worktreeDir, '.git');
    const gitFileContent = await fs.readFile(gitFilePath, 'utf-8');

    // Extract gitdir path
    const gitdirMatch = /gitdir: (.*)/.exec(gitFileContent);
    if (!gitdirMatch?.[1]) {
      return 'unknown';
    }

    const gitdir = gitdirMatch[1];
    const headPath = path.join(gitdir, 'HEAD');

    try {
      const headContent = await fs.readFile(headPath, 'utf-8');
      const branchMatch = /ref: refs\/heads\/(.*)/.exec(headContent);
      return branchMatch?.[1] ?? 'unknown';
    } catch (_error) {
      return 'unknown';
    }
  } catch (_error) {
    return 'unknown';
  }
}

/**
 * Get main repository information
 */
async function getMainRepoInfo(): Promise<{
  branch: string;
  apiPort: string;
  postgresPort: string;
  patientsPort: string;
  doctorsPort: string;
  adminPort: string;
}> {
  try {
    // Get current branch
    const { stdout } = await execAsync(
      'cd /Users/<USER>/Willow && git branch --show-current',
    );
    const branch = stdout.trim();

    // Get environment information
    const envInfo = await getEnvironmentInfo(MAIN_REPO_DIR, true);

    return {
      branch,
      ...envInfo,
    };
  } catch (_error) {
    return {
      branch: 'unknown',
      apiPort: 'unknown',
      postgresPort: 'unknown',
      patientsPort: 'unknown',
      doctorsPort: 'unknown',
      adminPort: 'unknown',
    };
  }
}

/**
 * Check if a Tmux session is currently running
 */
async function tmuxSessionIsRunning(worktreeName: string): Promise<boolean> {
  try {
    // For main repo, check both possible session names
    if (worktreeName === 'main') {
      const sessionNames = ['willow-main', 'willow'];

      for (const sessionName of sessionNames) {
        try {
          const { stdout } = await execAsync(
            `tmux list-sessions | grep "^${sessionName}:"`,
          );
          if (stdout.trim().length > 0) {
            return true;
          }
        } catch (_error) {
          // Continue checking other session names
        }
      }
      return false;
    }

    // For worktrees, check the specific session name
    const sessionName = `willow-${worktreeName}`;
    const { stdout } = await execAsync(
      `tmux list-sessions | grep "^${sessionName}:"`,
    );
    return stdout.trim().length > 0;
  } catch (_error) {
    return false;
  }
}

/**
 * Check if Docker is running globally (any willow-postgres container)
 */
async function dockerIsRunningGlobally(): Promise<boolean> {
  try {
    const { stdout } = await execAsync('docker ps | grep "willow-postgres"');
    return stdout.trim().length > 0;
  } catch (_error) {
    return false;
  }
}

/**
 * Get environment information for a worktree
 */
async function getEnvironmentInfo(
  worktreeDir: string,
  isMainRepo = false,
): Promise<{
  apiPort: string;
  postgresPort: string;
  patientsPort: string;
  doctorsPort: string;
  adminPort: string;
}> {
  const envFilePath = path.join(worktreeDir, '.env');

  try {
    const envContent = await fs.readFile(envFilePath, 'utf-8');

    // Extract API port
    const apiPortMatch = /^API_PORT=(.*)$/m.exec(envContent);
    const apiPort = apiPortMatch?.[1] ?? 'unknown';

    // Extract Postgres port
    const postgresPortMatch = /^POSTGRES_PORT=(.*)$/m.exec(envContent);
    const postgresPort = postgresPortMatch?.[1] ?? 'unknown';

    // Extract frontend ports - these might not be explicitly set in .env
    // so we need to calculate them based on the worktree index
    const patientsPortMatch = /^PATIENTS_PORT=(.*)$/m.exec(envContent);
    const doctorsPortMatch = /^DOCTORS_PORT=(.*)$/m.exec(envContent);
    const adminPortMatch = /^ADMIN_PORT=(.*)$/m.exec(envContent);

    // If ports aren't found in .env, calculate them based on API port offset
    let patientsPort = 'unknown';
    let doctorsPort = 'unknown';
    let adminPort = 'unknown';

    if (patientsPortMatch?.[1]) {
      patientsPort = patientsPortMatch[1];
    } else if (apiPort !== 'unknown') {
      // Calculate based on whether this is main repo or worktree
      if (isMainRepo) {
        // Main repo always uses standard ports
        patientsPort = '3000';
      } else {
        // Worktrees use offset from their API port
        const apiPortNum = parseInt(apiPort, 10);
        const portOffset = apiPortNum - 8081;
        patientsPort = (3001 + portOffset).toString();
      }
    }

    if (doctorsPortMatch?.[1]) {
      doctorsPort = doctorsPortMatch[1];
    } else if (apiPort !== 'unknown') {
      if (isMainRepo) {
        doctorsPort = '3001';
      } else {
        const apiPortNum = parseInt(apiPort, 10);
        const portOffset = apiPortNum - 8081;
        doctorsPort = (3002 + portOffset).toString();
      }
    }

    if (adminPortMatch?.[1]) {
      adminPort = adminPortMatch[1];
    } else if (apiPort !== 'unknown') {
      if (isMainRepo) {
        adminPort = '3002';
      } else {
        const apiPortNum = parseInt(apiPort, 10);
        const portOffset = apiPortNum - 8081;
        adminPort = (3003 + portOffset).toString();
      }
    }

    return {
      apiPort,
      postgresPort,
      patientsPort,
      doctorsPort,
      adminPort,
    };
  } catch (_error) {
    return {
      apiPort: 'unknown',
      postgresPort: 'unknown',
      patientsPort: 'unknown',
      doctorsPort: 'unknown',
      adminPort: 'unknown',
    };
  }
}

/**
 * Select environment with search functionality
 */
async function selectEnvironmentWithSearch(
  environments: EnvironmentData[],
  actionName: string,
  actionFunction: (env: EnvironmentData, number: number) => Promise<void>,
  cleanup: (shouldReload?: boolean) => void,
): Promise<void> {
  try {
    // Temporarily clean up the raw mode and listeners to allow search prompt to work
    process.stdin.setRawMode(false);
    process.stdin.removeAllListeners('data');
    process.stdin.pause();

    // Create options array with environment names
    const environmentOptions = environments.map((env) => env.name);

    const selectedOption = await search({
      message: `Select environment for ${actionName}:`,
      source: (term: string | undefined) => {
        if (!term) return environmentOptions;
        return environmentOptions.filter((option) =>
          option.toLowerCase().includes(term.toLowerCase()),
        );
      },
    });

    // Find the selected environment
    const selectedEnv = environments.find((env) => env.name === selectedOption);
    if (selectedEnv) {
      await actionFunction(selectedEnv, selectedEnv.number);
    }

    // Clean up and exit interactive mode
    cleanup();
  } catch (error) {
    // User cancelled or error occurred
    console.log(styles.muted('Operation cancelled'));
    // Clean up and exit interactive mode even if cancelled
    cleanup();
  }
}

/**
 * Handle keyboard shortcuts
 */
async function handleKeyboardShortcuts(
  environments: EnvironmentData[],
): Promise<boolean> {
  console.log();
  console.log(styles.header('Interactive Mode'));
  console.log(
    'Shortcuts: Q-Quit, R-Reload, W-WebStorm, V-VS Code, T-Terminal/Tmux, B-Browser, A-All',
  );
  console.log(styles.muted('Press Q to quit...'));
  console.log();

  // Check if stdin supports raw mode (only available in interactive terminals)
  if (!process.stdin.isTTY || typeof process.stdin.setRawMode !== 'function') {
    console.log(styles.error('Interactive mode requires a TTY terminal'));
    console.log(styles.info('In a real terminal, you would be able to:'));
    console.log(
      styles.info('- Press W to search and select environment for WebStorm'),
    );
    console.log(
      styles.info('- Press V to search and select environment for VS Code'),
    );
    console.log(
      styles.info('- Press T to search and select environment for Terminal'),
    );
    console.log(
      styles.info('- Press B to search and select environment for Browser'),
    );
    console.log(
      styles.info(
        '- Press A to search and select environment for All (WebStorm + Terminal + Browser)',
      ),
    );
    console.log(
      styles.info(
        '- Press Ctrl+C during search to cancel (will exit the tool)',
      ),
    );
    console.log(styles.info('- Press Q to quit'));
    return false;
  }

  // Set up raw mode for immediate key detection
  process.stdin.setRawMode(true);
  process.stdin.resume();
  process.stdin.setEncoding('utf8');

  return new Promise<boolean>((resolve) => {
    const handleKeypress = async (chunk: string) => {
      const key = chunk.toString();

      // Handle Ctrl+C for graceful exit
      if (key === '\u0003') {
        cleanup();
        return;
      }

      // Handle Q for quit
      if (key.toLowerCase() === 'q') {
        cleanup();
        return;
      }

      // Handle R for reload
      if (key.toLowerCase() === 'r') {
        console.log(styles.info('Reloading environment information...'));
        cleanup(true); // Pass true to indicate reload
        return;
      }

      // Handle T for terminal/tmux
      if (key.toLowerCase() === 't') {
        await selectEnvironmentWithSearch(
          environments,
          'Terminal',
          openTerminalForEnvironment,
          cleanup,
        );
        return;
      }

      // Handle WebStorm command
      if (key.toLowerCase() === 'w') {
        await selectEnvironmentWithSearch(
          environments,
          'WebStorm',
          openWebStormForEnvironment,
          cleanup,
        );
        return;
      }

      // Handle VS Code command
      if (key.toLowerCase() === 'v') {
        await selectEnvironmentWithSearch(
          environments,
          'VS Code',
          openVSCodeForEnvironment,
          cleanup,
        );
        return;
      }

      // Handle Browser command
      if (key.toLowerCase() === 'b') {
        await selectEnvironmentWithSearch(
          environments,
          'Browser',
          openBrowserForEnvironment,
          cleanup,
        );
        return;
      }

      // Handle All command (WebStorm + Terminal + Browser)
      if (key.toLowerCase() === 'a') {
        await selectEnvironmentWithSearch(
          environments,
          'All (WebStorm + Terminal + Browser)',
          openAllForEnvironment,
          cleanup,
        );
        return;
      }
    };

    const cleanup = (shouldReload = false) => {
      process.stdin.setRawMode(false);
      process.stdin.removeAllListeners('data');
      process.stdin.pause();
      if (!shouldReload) {
        console.log(styles.info('\nExiting interactive mode...'));
      }
      resolve(shouldReload);
    };

    const openEnvironmentApps = async (
      env: EnvironmentData,
      number: number,
    ) => {
      console.log(styles.info(`Opening ${env.name} apps in browser...`));

      // Open frontend apps only
      const promises: Promise<void>[] = [];
      if (env.patientsPort !== 'unknown') {
        promises.push(openInBrowser(`http://localhost:${env.patientsPort}`));
      }
      if (env.doctorsPort !== 'unknown') {
        promises.push(openInBrowser(`http://localhost:${env.doctorsPort}`));
      }
      if (env.adminPort !== 'unknown') {
        promises.push(openInBrowser(`http://localhost:${env.adminPort}`));
      }

      await Promise.all(promises);
      console.log(styles.success(`✅ Environment ${number} apps opened`));
    };

    const openWebStormForEnvironment = async (
      env: EnvironmentData,
      number: number,
    ) => {
      console.log(styles.info(`Opening ${env.name} in WebStorm...`));
      await openWebStorm(env.path);
      console.log(
        styles.success(`✅ WebStorm opened for environment ${number}`),
      );
    };

    const openVSCodeForEnvironment = async (
      env: EnvironmentData,
      number: number,
    ) => {
      console.log(styles.info(`Opening ${env.name} in VS Code...`));
      await openVSCode(env.path);
      console.log(
        styles.success(`✅ VS Code opened for environment ${number}`),
      );
    };

    const openTerminalForEnvironment = async (
      env: EnvironmentData,
      number: number,
    ) => {
      console.log(styles.info(`Opening terminal with tmux for ${env.name}...`));
      await openTerminalWithTmux(env);
      console.log(
        styles.success(`✅ Terminal opened for environment ${number}`),
      );
    };

    const openBrowserForEnvironment = async (
      env: EnvironmentData,
      number: number,
    ) => {
      console.log(styles.info(`Opening ${env.name} apps in browser...`));
      await openEnvironmentApps(env, number);
    };

    const openAllForEnvironment = async (
      env: EnvironmentData,
      number: number,
    ) => {
      console.log(
        styles.info(
          `Opening ${env.name} in WebStorm, Terminal, and Browser...`,
        ),
      );

      // Prioritize WebStorm over VS Code
      await openWebStorm(env.path);
      console.log(
        styles.success(`✅ WebStorm opened for environment ${number}`),
      );

      // Open Terminal with Tmux
      await openTerminalWithTmux(env);
      console.log(
        styles.success(`✅ Terminal opened for environment ${number}`),
      );

      // Open Browser apps
      await openEnvironmentApps(env, number);

      console.log(
        styles.success(`✅ All apps opened for environment ${number}`),
      );
    };

    process.stdin.on('data', (chunk: Buffer) => {
      void handleKeypress(chunk.toString());
    });

    // Handle process termination
    process.on('SIGINT', cleanup);
    process.on('SIGTERM', cleanup);
  });
}

/**
 * Display environment information (main function logic)
 */
async function displayEnvironments(): Promise<EnvironmentData[]> {
  // First, prune any stale worktree references
  try {
    await execAsync('cd /Users/<USER>/Willow && git worktree prune');
  } catch (error) {
    // Silently continue if prune fails
  }

  // Print header
  console.log(styles.header('Willow Worktrees'));
  console.log(styles.header('================'));

  // Check if worktree directory exists
  const worktreeDirExists = await directoryExists(WORKTREE_BASE_DIR);
  if (!worktreeDirExists) {
    console.log(
      `No worktrees found. (Directory ${WORKTREE_BASE_DIR} doesn't exist)`,
    );
    return [];
  }

  // Get main repository info
  const mainRepoInfo = await getMainRepoInfo();

  // Get all worktree directories
  const worktreeDirs = await getWorktreeDirs();

  // Store environment data for interactive mode
  const environments: EnvironmentData[] = [];

  // Check global Docker status
  const globalDockerRunning = await dockerIsRunningGlobally();
  const globalDockerStatus = globalDockerRunning
    ? styles.success('✅')
    : styles.error('❌');

  // Print total count (main repo + worktrees)
  const totalCount = worktreeDirs.length + 1;
  console.log(`Docker: ${globalDockerStatus} (shared across all environments)`);
  console.log();

  // Add main repository to environments
  environments.push({
    number: 1,
    name: `${mainRepoInfo.branch} (current branch)`,
    isMain: true,
    path: MAIN_REPO_DIR,
    patientsPort: mainRepoInfo.patientsPort,
    doctorsPort: mainRepoInfo.doctorsPort,
    adminPort: mainRepoInfo.adminPort,
    apiPort: mainRepoInfo.apiPort,
  });

  // Display main repository first
  console.log(
    `1. ${styles.info(mainRepoInfo.branch)} ${styles.warning('(current branch)')}`,
  );
  console.log(`   Branch: ${mainRepoInfo.branch}`);

  // Check if main tmux session is running
  const mainTmuxRunning = await tmuxSessionIsRunning('main');
  const mainTmuxStatus = mainTmuxRunning
    ? styles.success('✅')
    : styles.error('❌');

  console.log(`   Tmux: ${mainTmuxStatus}`);
  console.log(
    `   Ports: API: ${mainRepoInfo.apiPort}, DB: ${mainRepoInfo.postgresPort}`,
  );

  // Check port status for main repo services
  const [
    mainPatientsRunning,
    mainDoctorsRunning,
    mainAdminRunning,
    mainApiRunning,
  ] = await Promise.all([
    isPortResponding(mainRepoInfo.patientsPort),
    isPortResponding(mainRepoInfo.doctorsPort),
    isPortResponding(mainRepoInfo.adminPort),
    isPortResponding(mainRepoInfo.apiPort),
  ]);

  // Print URLs for main repo
  console.log(`   URLs:`);
  if (mainRepoInfo.patientsPort !== 'unknown') {
    const statusIcon = mainPatientsRunning
      ? styles.success('●')
      : styles.error('●');
    console.log(
      `     Patients: ${statusIcon} ${styles.command(`http://localhost:${mainRepoInfo.patientsPort}`)}`,
    );
  } else {
    console.log(`     Patients: ${styles.muted('● unknown')}`);
  }

  if (mainRepoInfo.doctorsPort !== 'unknown') {
    const statusIcon = mainDoctorsRunning
      ? styles.success('●')
      : styles.error('●');
    console.log(
      `     Doctors:  ${statusIcon} ${styles.command(`http://localhost:${mainRepoInfo.doctorsPort}`)}`,
    );
  } else {
    console.log(`     Doctors:  ${styles.muted('● unknown')}`);
  }

  if (mainRepoInfo.adminPort !== 'unknown') {
    const statusIcon = mainAdminRunning
      ? styles.success('●')
      : styles.error('●');
    console.log(
      `     Admin:    ${statusIcon} ${styles.command(`http://localhost:${mainRepoInfo.adminPort}`)}`,
    );
  } else {
    console.log(`     Admin:    ${styles.muted('● unknown')}`);
  }

  if (mainRepoInfo.apiPort !== 'unknown') {
    const statusIcon = mainApiRunning ? styles.success('●') : styles.error('●');
    console.log(
      `     API:      ${statusIcon} ${styles.command(`http://localhost:${mainRepoInfo.apiPort}`)}`,
    );
  } else {
    console.log(`     API:      ${styles.muted('● unknown')}`);
  }

  // Only show path in verbose mode
  if (process.env.WILLOW_WORKTREE_VERBOSE === 'true') {
    console.log(`   Path: ${MAIN_REPO_DIR}`);
  }

  console.log();

  // Loop through each worktree
  let count = 2;
  for (const worktreeDir of worktreeDirs) {
    // Get worktree name from directory
    const worktreeName = path.basename(worktreeDir);

    // Get branch information
    const branch = await getBranchInfo(worktreeDir);

    // Check if tmux session is running
    const tmuxRunning = await tmuxSessionIsRunning(worktreeName);
    const tmuxStatus = tmuxRunning ? styles.success('✅') : styles.error('❌');

    // Get environment information
    const envInfo = await getEnvironmentInfo(worktreeDir);
    const envStatus = `API: ${envInfo.apiPort}, DB: ${envInfo.postgresPort}`;

    // Add worktree to environments
    environments.push({
      number: count,
      name: `${branch} (${worktreeName})`,
      isMain: false,
      path: worktreeDir,
      patientsPort: envInfo.patientsPort,
      doctorsPort: envInfo.doctorsPort,
      adminPort: envInfo.adminPort,
      apiPort: envInfo.apiPort,
    });

    // Print worktree information
    console.log(
      `${count}. ${styles.info(branch)} ${styles.warning(`(${worktreeName})`)}`,
    );
    console.log(`   Branch: ${branch}`);
    console.log(`   Tmux: ${tmuxStatus}`);
    console.log(`   Ports: ${envStatus}`);

    // Check port status for each service
    const [patientsRunning, doctorsRunning, adminRunning, apiRunning] =
      await Promise.all([
        isPortResponding(envInfo.patientsPort),
        isPortResponding(envInfo.doctorsPort),
        isPortResponding(envInfo.adminPort),
        isPortResponding(envInfo.apiPort),
      ]);

    // Print URLs for each section with status indicators
    console.log(`   URLs:`);
    if (envInfo.patientsPort !== 'unknown') {
      const statusIcon = patientsRunning
        ? styles.success('●')
        : styles.error('●');
      console.log(
        `     Patients: ${statusIcon} ${styles.command(`http://localhost:${envInfo.patientsPort}`)}`,
      );
    } else {
      console.log(`     Patients: ${styles.muted('● unknown')}`);
    }

    if (envInfo.doctorsPort !== 'unknown') {
      const statusIcon = doctorsRunning
        ? styles.success('●')
        : styles.error('●');
      console.log(
        `     Doctors:  ${statusIcon} ${styles.command(`http://localhost:${envInfo.doctorsPort}`)}`,
      );
    } else {
      console.log(`     Doctors:  ${styles.muted('● unknown')}`);
    }

    if (envInfo.adminPort !== 'unknown') {
      const statusIcon = adminRunning ? styles.success('●') : styles.error('●');
      console.log(
        `     Admin:    ${statusIcon} ${styles.command(`http://localhost:${envInfo.adminPort}`)}`,
      );
    } else {
      console.log(`     Admin:    ${styles.muted('● unknown')}`);
    }

    if (envInfo.apiPort !== 'unknown') {
      const statusIcon = apiRunning ? styles.success('●') : styles.error('●');
      console.log(
        `     API:      ${statusIcon} ${styles.command(`http://localhost:${envInfo.apiPort}`)}`,
      );
    } else {
      console.log(`     API:      ${styles.muted('● unknown')}`);
    }

    // Only show path in verbose mode
    if (process.env.WILLOW_WORKTREE_VERBOSE === 'true') {
      console.log(`   Path: ${worktreeDir}`);
    }

    count++;
  }
  return environments;
}

/**
 * Print help footer
 */
function printHelpFooter(): void {
  console.log();
  console.log('To start an environment:');
  console.log(
    styles.command('  tmuxinator start willow-main          # for main repo'),
  );
  console.log(
    styles.command('  tmuxinator start willow-<worktree>    # for worktrees'),
  );
  console.log();
  console.log('To stop and remove a worktree:');
  console.log(styles.command('  ./teardown-worktree.sh <worktree-name>'));
}

/**
 * List all worktrees with interactive mode
 */
async function listWorktrees(): Promise<void> {
  let shouldReload = true;

  while (shouldReload) {
    const environments = await displayEnvironments();
    printHelpFooter();
    shouldReload = await handleKeyboardShortcuts(environments);
  }
}

interface ListOptions {
  verbose?: boolean;
}

async function main() {
  try {
    const program = new Command();

    program
      .name('worktree-list')
      .description(
        'Lists all git worktrees created with the worktree setup command',
      )
      .option(
        '-v, --verbose',
        'Show more detailed information including full paths',
      );

    program.parse();

    const options = program.opts<ListOptions>();

    // Set global verbose flag if specified
    if (options.verbose) {
      process.env.WILLOW_WORKTREE_VERBOSE = 'true';
    }

    await listWorktrees();

    process.exit(0);
  } catch (error) {
    if (error instanceof Error) {
      console.error(styles.error('\nError:'), error.message);
    }
    process.exit(1);
  }
}

void main();
