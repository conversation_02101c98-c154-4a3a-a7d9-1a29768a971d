import { exec } from 'child_process';
import fs from 'fs/promises';
import path from 'path';
import { promisify } from 'util';
import { confirm, input, search } from '@inquirer/prompts';
import chalk from 'chalk';
import { Command } from 'commander';
import ora from 'ora';

import { cleanNameForFilesystem } from './utils';

const execAsync = promisify(exec);

interface CommandOptions {
  name?: string;
  force?: boolean;
}

export const styles = {
  header: chalk.bold.cyan,
  success: chalk.green,
  warning: chalk.yellow,
  error: chalk.red,
  info: chalk.cyan,
  muted: chalk.gray,
  command: chalk.magenta,
};

// Configuration
const MAIN_REPO = '/Users/<USER>/Willow';
const WORKTREE_BASE_DIR = '/Users/<USER>/Willow-worktrees';
const TMUXINATOR_CONFIG_DIR = '/Users/<USER>/.config/tmuxinator';

process.on('SIGINT', () => {
  process.exit(0);
});

/**
 * Check if a directory exists
 */
async function directoryExists(dirPath: string): Promise<boolean> {
  try {
    const stats = await fs.stat(dirPath);
    return stats.isDirectory();
  } catch (_error) {
    return false;
  }
}

/**
 * Check if a file exists
 */
async function fileExists(filePath: string): Promise<boolean> {
  try {
    const stats = await fs.stat(filePath);
    return stats.isFile();
  } catch (_error) {
    return false;
  }
}

/**
 * Get all worktree directories
 */
async function getWorktreeDirs(): Promise<string[]> {
  try {
    const entries = await fs.readdir(WORKTREE_BASE_DIR);
    const dirs: string[] = [];

    for (const entry of entries) {
      const fullPath = path.join(WORKTREE_BASE_DIR, entry);
      const stats = await fs.stat(fullPath);
      if (stats.isDirectory()) {
        dirs.push(entry);
      }
    }

    return dirs;
  } catch (_error) {
    console.error(`Error reading worktree directory: ${String(_error)}`);
    return [];
  }
}

/**
 * Check if Docker is running
 */
async function isDockerRunning(): Promise<boolean> {
  try {
    await execAsync('docker info');
    return true;
  } catch (_error) {
    return false;
  }
}

/**
 * Check if tmuxinator is installed
 */
async function isTmuxinatorInstalled(): Promise<boolean> {
  try {
    await execAsync('which tmuxinator');
    return true;
  } catch (_error) {
    return false;
  }
}

/**
 * Check if tmuxinator is running for a specific worktree
 */
async function isTmuxinatorRunning(worktreeName: string): Promise<boolean> {
  // First check if tmuxinator is even installed
  if (!(await isTmuxinatorInstalled())) {
    return false;
  }

  try {
    const { stdout } = await execAsync('tmuxinator list');
    return stdout.includes(`willow-${worktreeName}`);
  } catch (_error) {
    return false;
  }
}

/**
 * Stop tmuxinator project if tmuxinator is installed
 */
async function stopTmuxinator(worktreeName: string): Promise<void> {
  // Skip if tmuxinator is not installed
  if (!(await isTmuxinatorInstalled())) {
    return;
  }

  const spinner = ora('Stopping tmuxinator project...').start();
  try {
    await execAsync(`tmuxinator stop willow-${worktreeName}`);
    spinner.succeed('Tmuxinator project stopped');
  } catch (_error) {
    spinner.warn('Could not stop tmuxinator project, it may not be running');
  }
}

/**
 * Remove tmuxinator config if it exists
 */
async function removeTmuxinatorConfig(worktreeName: string): Promise<void> {
  // Skip if tmuxinator is not installed or directory doesn't exist
  if (!(await isTmuxinatorInstalled())) {
    return;
  }

  // Check if tmuxinator config directory exists
  try {
    await fs.access(TMUXINATOR_CONFIG_DIR);
  } catch (_error) {
    // Config directory doesn't exist, so no config to remove
    return;
  }

  const configPath = path.join(
    TMUXINATOR_CONFIG_DIR,
    `willow-${worktreeName}.yml`,
  );

  const spinner = ora(`Checking for tmuxinator config...`).start();

  if (await fileExists(configPath)) {
    try {
      await fs.unlink(configPath);
      spinner.succeed(`Tmuxinator config removed: ${configPath}`);
    } catch (_error) {
      spinner.warn(`Could not remove tmuxinator config: ${String(_error)}`);
    }
  } else {
    spinner.info('No tmuxinator config found');
  }
}

/**
 * Drop database for worktree
 */
async function dropDatabase(worktreeName: string): Promise<void> {
  const spinner = ora(`Dropping database willow_${worktreeName}...`).start();

  try {
    // First, terminate all connections to the database
    spinner.text = `Terminating connections to database willow_${worktreeName}...`;
    await execAsync(
      `docker exec willow-postgres psql -U user -d willow -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = 'willow_${worktreeName}' AND pid <> pg_backend_pid();"`,
    );

    // Now drop the database
    spinner.text = `Dropping database willow_${worktreeName}...`;
    await execAsync(
      `docker exec willow-postgres psql -U user -d willow -c "DROP DATABASE IF EXISTS willow_${worktreeName};"`,
    );
    spinner.succeed(`Database willow_${worktreeName} dropped successfully`);
  } catch (error) {
    if (error instanceof Error && process.env.DEBUG) {
      console.error(styles.error(`Database drop error: ${error.message}`));
    }
    spinner.warn(
      'Could not drop database. It may not exist or there was an error connecting to PostgreSQL',
    );
  }
}

/**
 * Remove git worktree
 */
async function removeGitWorktree(worktreeDir: string): Promise<void> {
  const spinner = ora('Removing git worktree...').start();

  try {
    // First, try to remove the worktree normally
    await execAsync(
      `cd "${MAIN_REPO}" && git worktree remove "${worktreeDir}" --force`,
    );
    spinner.succeed('Git worktree removed');
  } catch (error) {
    // If that fails, try to prune worktrees and clean up manually
    spinner.text = 'Git worktree remove failed, attempting cleanup...';

    try {
      // Prune any stale worktree references
      await execAsync(`cd "${MAIN_REPO}" && git worktree prune`);

      // Try to remove the directory manually if it still exists
      if (await directoryExists(worktreeDir)) {
        await fs.rm(worktreeDir, { recursive: true, force: true });
      }

      spinner.succeed('Git worktree cleaned up (manual cleanup required)');
    } catch (cleanupError) {
      spinner.warn(
        `Could not fully clean up worktree: ${String(cleanupError)}`,
      );
      // Don't throw here - we want to continue with the teardown process
    }
  }
}

/**
 * Main teardown function
 */
async function teardownWorktree(
  worktreeName: string,
  force = false,
): Promise<void> {
  // Clean worktree name for filesystem use
  const finalWorktreeName = cleanNameForFilesystem(worktreeName);

  const worktreeDir = path.join(WORKTREE_BASE_DIR, finalWorktreeName);

  console.log(
    styles.header('\nTearing down environment for worktree:'),
    worktreeName,
  );
  console.log(styles.info('Working directory:'), worktreeDir);

  // Check if worktree exists
  if (!(await directoryExists(worktreeDir))) {
    console.error(
      styles.error(`Error: Worktree directory doesn't exist at ${worktreeDir}`),
    );
    process.exit(1);
  }

  // Confirm teardown if not forced
  if (!force) {
    const confirmTeardown = await confirm({
      message: `Are you sure you want to tear down the worktree "${worktreeName}"? This will remove all associated resources.`,
      default: false,
    });

    if (!confirmTeardown) {
      console.log(styles.warning('Teardown cancelled'));
      process.exit(0);
    }
  }

  // Check if tmuxinator is available and running this project
  const tmuxinatorAvailable = await isTmuxinatorInstalled();
  if (tmuxinatorAvailable) {
    console.log(styles.info('\nChecking tmuxinator projects...'));
    if (await isTmuxinatorRunning(finalWorktreeName)) {
      await stopTmuxinator(finalWorktreeName);
    }

    // Remove tmuxinator config file if it exists
    await removeTmuxinatorConfig(finalWorktreeName);
  } else {
    console.log(
      styles.info('\nTmuxinator not found, skipping tmuxinator cleanup...'),
    );
  }

  // Check if Docker is running
  const dockerRunning = await isDockerRunning();
  if (!dockerRunning) {
    console.log(
      styles.warning('Docker is not running. Skipping database cleanup.'),
    );
  } else {
    // Drop the database
    await dropDatabase(finalWorktreeName);
  }

  // First, prune any stale worktree references
  const pruneSpinner = ora('Pruning stale worktree references...').start();
  try {
    await execAsync(`cd "${MAIN_REPO}" && git worktree prune`);
    pruneSpinner.succeed('Stale worktree references pruned');
  } catch (error) {
    pruneSpinner.warn(`Could not prune worktree references: ${String(error)}`);
  }

  // Remove git worktree
  await removeGitWorktree(worktreeDir);

  // Ensure directory is completely removed
  if (await directoryExists(worktreeDir)) {
    const spinner = ora(
      'Ensuring worktree directory is completely removed...',
    ).start();
    try {
      await fs.rm(worktreeDir, { recursive: true, force: true });
      spinner.succeed('Worktree directory completely removed');
    } catch (error) {
      spinner.warn(`Could not remove directory: ${String(error)}`);
    }
  }

  // Final message
  console.log(
    styles.success('\n✅ Teardown complete for worktree:'),
    worktreeName,
  );
  console.log(styles.success('All resources have been removed.'));
}

function main(): void {
  const program = new Command();

  program
    .name('teardown')
    .description('Tear down a git worktree with custom environment')
    .option('-n, --name <name>', 'Name of the worktree to tear down')
    .option('-f, --force', 'Force teardown without confirmation', false)
    .action(async (options: CommandOptions) => {
      try {
        let worktreeName = options.name;

        // If no name provided, show available worktrees and prompt with search
        if (!worktreeName) {
          const worktrees = await getWorktreeDirs();

          if (worktrees.length === 0) {
            console.log(styles.warning('No worktrees found to tear down'));
            process.exit(1);
          }

          worktreeName = await search({
            message: 'Select a worktree to tear down:',
            source: (term: string | undefined) => {
              if (!term) return worktrees;
              return worktrees.filter((name) =>
                name.toLowerCase().includes(term.toLowerCase()),
              );
            },
          });
        }

        if (worktreeName) {
          await teardownWorktree(worktreeName, options.force ?? false);
        }
      } catch (error) {
        if (error instanceof Error) {
          console.error(styles.error(error.message));
        } else {
          console.error(styles.error(String(error)));
        }
        process.exit(1);
      }
    });

  program.parse();
}

// In ESM, we can use import.meta.url to check if this is the main module
if (import.meta.url === `file://${process.argv[1]}`) {
  void main();
}
