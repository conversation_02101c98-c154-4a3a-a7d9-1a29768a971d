/**
 * Clean a string for filesystem and database use
 *
 * Ensures the name is compatible with:
 * 1. Filesystem naming restrictions (no special chars, spaces)
 * 2. PostgreSQL database naming conventions
 * 3. Redis key prefix requirements
 */
export function cleanNameForFilesystem(name: string): string {
  // Convert to lowercase, replace non-alphanumeric chars with underscores
  const sanitized = name.toLowerCase().replace(/[^a-z0-9]/g, '_');

  // Ensure it doesn't start with a number (prefixes with 'w_' if needed)
  return /^[0-9]/.test(sanitized) ? `w_${sanitized}` : sanitized;
}
