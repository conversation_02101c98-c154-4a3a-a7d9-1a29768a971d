#!/bin/zsh

emails=(
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
)

for email in "${emails[@]}"; do
  echo "Ejecutando para $email en paralelo (full y 30d)"
  pnpm -F api cli test-conversation-simulation --interactive false --html ./convo -p "$email" --convo-context full &
  pnpm -F api cli test-conversation-simulation --interactive false --html ./convo -p "$email" --convo-context 30d &
  wait
  echo "Finalizado $email"
done
