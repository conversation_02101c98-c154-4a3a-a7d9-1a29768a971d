# Role-Based Authorization in Willow Admin Dashboard

> **Note**: This documentation has been updated to reflect the refactored auth package structure that supports both ESM and CommonJS module formats.

This document describes the role-based authorization system implemented in the Willow Admin Dashboard.

## Overview

The Willow Admin Dashboard uses a capability-based role system that allows for fine-grained control over what actions different admin users can perform. This system is designed to be flexible and extensible, allowing for the addition of new roles and capabilities as needed.

## Roles

The system currently supports the following roles:

| Role | Description |
|------|-------------|
| `superAdmin` | Has full access to all features and capabilities |
| `admin` | Has access to most features except admin management |
| `products` | Has access only to product and pharmacy management features |

## Capabilities

Each role is assigned a set of capabilities that determine what actions the user can perform. The system uses both section-level capabilities for broader permissions and granular capabilities to allow for fine-grained control over specific actions.

### Section-Level Capabilities

These capabilities provide broader permissions for entire sections of the application:

| Capability | Description |
|------------|-------------|
| `MANAGE_ADMINS` | Can manage all aspects of admin users |
| `MANAGE_DOCTORS` | Can manage all aspects of doctor accounts |
| `MANAGE_PATIENTS` | Can manage all aspects of patient accounts |
| `MANAGE_PHARMACIES` | Can manage all aspects of pharmacies |
| `MANAGE_PRODUCTS` | Can manage all aspects of products |
| `MANAGE_PRODUCT_PRICES` | Can manage all aspects of product prices |
| `MANAGE_PRODUCT_PRICE_EQUIVALENCE` | Can manage all aspects of product price equivalence groups |
| `MANAGE_PRODUCT_PRICE_MAPPING` | Can manage all aspects of product price mappings |
| `MANAGE_TREATMENTS` | Can manage all aspects of treatments |
| `MANAGE_BILLING` | Can manage all aspects of billing |
| `MANAGE_STATES` | Can manage all aspects of states |

### Granular Capabilities

These capabilities provide fine-grained control over specific actions:

### Admin Management

| Capability | Description |
|------------|-------------|
| `VIEW_ADMINS` | Can view admin users |
| `CREATE_ADMINS` | Can create new admin users |
| `EDIT_ADMINS` | Can edit admin users |
| `DEACTIVATE_ADMINS` | Can deactivate admin users |
| `REACTIVATE_ADMINS` | Can reactivate admin users |
| `DELETE_ADMINS` | Can delete admin users |

### Doctor Management

| Capability | Description |
|------------|-------------|
| `VIEW_DOCTORS` | Can view doctor accounts |
| `CREATE_DOCTORS` | Can create doctor accounts |
| `EDIT_DOCTORS` | Can edit doctor accounts |
| `DEACTIVATE_DOCTORS` | Can deactivate doctor accounts |
| `REACTIVATE_DOCTORS` | Can reactivate doctor accounts |

### Patient Management

| Capability | Description |
|------------|-------------|
| `VIEW_PATIENTS` | Can view patient accounts |
| `EDIT_PATIENT_INFO` | Can edit patient information |
| `EDIT_PATIENT_ADDRESSES` | Can edit patient addresses |
| `RESET_PATIENT_PASSWORD` | Can reset patient passwords |
| `TRANSFER_PATIENT_DOCTOR` | Can transfer patients between doctors |
| `TRANSFER_PATIENT_PHARMACY` | Can transfer patients between pharmacies |
| `CREATE_PATIENT_NOTES` | Can create notes on patient accounts |
| `DELETE_PATIENTS` | Can delete patient accounts |

### Pharmacy Management

| Capability | Description |
|------------|-------------|
| `VIEW_PHARMACIES` | Can view pharmacies |
| `CREATE_PHARMACIES` | Can create pharmacies |
| `EDIT_PHARMACIES` | Can edit pharmacies |
| `DEACTIVATE_PHARMACIES` | Can deactivate pharmacies |
| `REACTIVATE_PHARMACIES` | Can reactivate pharmacies |
| `DELETE_PHARMACIES` | Can delete pharmacies |
| `TRANSFER_PHARMACY_PATIENTS` | Can transfer all patients from one pharmacy to another |

### Product Management

| Capability | Description |
|------------|-------------|
| `VIEW_PRODUCTS` | Can view products |
| `CREATE_PRODUCTS` | Can create products |
| `EDIT_PRODUCTS` | Can edit products |
| `ACTIVATE_PRODUCTS` | Can activate products |
| `DEACTIVATE_PRODUCTS` | Can deactivate products |
| `DELETE_PRODUCTS` | Can delete products |

### Product Price Management

| Capability | Description |
|------------|-------------|
| `VIEW_PRODUCT_PRICES` | Can view product prices |
| `CREATE_PRODUCT_PRICES` | Can create product prices |
| `EDIT_PRODUCT_PRICES` | Can edit product prices |
| `DELETE_PRODUCT_PRICES` | Can delete product prices |

### Product Price Equivalence Management

| Capability | Description |
|------------|-------------|
| `VIEW_PRODUCT_PRICE_EQUIVALENCE` | Can view product price equivalence groups |
| `CREATE_PRODUCT_PRICE_EQUIVALENCE` | Can create product price equivalence groups |
| `EDIT_PRODUCT_PRICE_EQUIVALENCE` | Can edit product price equivalence groups |
| `DELETE_PRODUCT_PRICE_EQUIVALENCE` | Can delete product price equivalence groups |

### Product Price Mapping Management

| Capability | Description |
|------------|-------------|
| `VIEW_PRODUCT_PRICE_MAPPING` | Can view product price mappings |
| `CREATE_PRODUCT_PRICE_MAPPING` | Can create product price mappings |
| `EDIT_PRODUCT_PRICE_MAPPING` | Can edit product price mappings |
| `DELETE_PRODUCT_PRICE_MAPPING` | Can delete product price mappings |

### Treatment Management

| Capability | Description |
|------------|-------------|
| `VIEW_TREATMENTS` | Can view treatments |
| `CREATE_TREATMENTS` | Can create treatments |
| `CANCEL_TREATMENTS` | Can cancel treatments |
| `FIRE_NEXT_TREATMENT` | Can manually trigger the next treatment |
| `MOVE_REFILL_DATE` | Can adjust treatment refill dates |

### Billing Management

| Capability | Description |
|------------|-------------|
| `VIEW_BILLING` | Can view billing information |
| `PROCESS_REFUNDS` | Can process refunds |
| `MANAGE_PAYMENT_METHODS` | Can manage payment methods |

### State Management

| Capability | Description |
|------------|-------------|
| `VIEW_STATES` | Can view states |
| `EDIT_STATES` | Can edit states |

### General Capabilities

| Capability | Description |
|------------|-------------|
| `VIEW_DASHBOARD` | Can view the dashboard |

## Role Capability Mapping

The following table shows a summary of which capability groups are assigned to each role:

| Capability Group | superAdmin | admin | products |
|------------------|------------|-------|----------|
| Admin Management | Full Access | Limited (View, Create non-superAdmins) | No Access |
| Doctor Management | Full Access | Full Access | No Access |
| Patient Management | Full Access | Full Access | No Access |
| Pharmacy Management | Full Access | Full Access | View Only |
| Product Management | Full Access | Full Access | Full Access |
| Product Price Management | Full Access | Full Access | Full Access |
| Product Price Equivalence | Full Access | Full Access | Full Access |
| Product Price Mapping | Full Access | Full Access | Full Access |
| Treatment Management | Full Access | Full Access | No Access |
| Billing Management | Full Access | Full Access | No Access |
| State Management | Full Access | Full Access | No Access |
| General Capabilities | Full Access | Full Access | Limited |

For a detailed breakdown of exactly which capabilities are assigned to each role, refer to the `ROLE_CAPABILITIES` mapping in the `packages/auth/src/roles.ts` file.

## Role Restrictions

In addition to capability-based restrictions, there are some role-specific restrictions:

1. **SuperAdmin Restrictions**: Only existing superAdmins can manage the superAdmin role:
   - Only superAdmins can create new superAdmin users
   - Only superAdmins can upgrade existing admins to superAdmin
   - Regular admins with the `CREATE_ADMINS` capability can only create admins with roles <> `superAdmin`

2. **Last SuperAdmin Protection**: The system prevents actions that would leave the application without any superAdmin users:
   - Cannot downgrade the last superAdmin to a regular admin
   - Cannot deactivate the last superAdmin
   - Cannot downgrade the currently logged-in superAdmin

## Implementation Details

### Backend

The role-based authorization system is implemented using the following components:

1. **Capabilities**: Defined in `packages/auth/src/capabilities.ts`
2. **Roles and Role Capabilities**: Defined in `packages/auth/src/roles.ts`
3. **Capability Groups**: Defined in `packages/auth/src/capability-groups.ts`
4. **Capability Guard**: Implemented in `apps/api/src/modules/auth/guards/capability.guard.ts`
5. **Capability Decorator**: Implemented in `apps/api/src/modules/auth/decorators/require-capabilities.decorator.ts`

### Frontend

In the frontend, the role-based authorization system is implemented using capability groups that make it easier to check for related capabilities:

1. **Capability Groups**: Defined in `packages/auth/src/capability-groups.ts`
2. **Authorization Components**: Components that conditionally render based on user capabilities

Each capability group has VIEW and MANAGE arrays that contain the relevant capabilities:

```typescript
export const AdminCapabilityGroups = {
  VIEW: [Capability.VIEW_ADMINS],
  MANAGE: [
    Capability.MANAGE_ADMINS,
    Capability.VIEW_ADMINS,
    Capability.CREATE_ADMINS,
    Capability.EDIT_ADMINS,
    Capability.DEACTIVATE_ADMINS,
    Capability.REACTIVATE_ADMINS,
    Capability.DELETE_ADMINS,
  ],
};
```

These groups can be used in frontend components to check if a user has the necessary capabilities to view or manage a particular resource.

## Adding New Roles or Capabilities

To add a new role:

1. Add the role to the `AdminRole` enum in `packages/db/prisma/schema.prisma`
2. Add the role to the `AdminRole` enum in `packages/auth/src/roles.ts`
3. Create a database migration using `pnpm -F db migrate --name add_new_role`
4. Add the role to the `ROLE_CAPABILITIES` mapping in `packages/auth/src/roles.ts`
5. Update the frontend components to display the new role

To add a new capability:

1. Add the capability to the `Capability` enum in `packages/auth/src/capabilities.ts`
2. Assign the capability to the appropriate roles in the `ROLE_CAPABILITIES` mapping in `packages/auth/src/roles.ts`
3. If needed, add the capability to relevant capability groups in `packages/auth/src/capability-groups.ts`
4. Use the `@RequireCapabilities` decorator on the relevant controller methods
