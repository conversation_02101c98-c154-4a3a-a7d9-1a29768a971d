import { Button } from '@willow/ui/base/button';

import { useCreateDoctorAdminConversation } from '~/hooks/chat';

export function CreateConversation({ patientId }: { patientId: string }) {
  const { mutateAsync: createDoctorAdminConversation, isPending } =
    useCreateDoctorAdminConversation();

  async function createConversation() {
    const conversationId = await createDoctorAdminConversation({
      patientId,
    });
    return conversationId;
  }

  return (
    <div className="flex h-full flex-col items-center justify-center gap-6">
      <div className="text-center text-sm text-gray-500">
        No active conversation exists with this patient.
      </div>
      <Button
        size="sm"
        variant="denimOutline"
        onClick={() => createConversation()}
        className="w-1/2"
        loading={isPending}
      >
        {' '}
        Initiate Discussion
      </Button>
    </div>
  );
}
