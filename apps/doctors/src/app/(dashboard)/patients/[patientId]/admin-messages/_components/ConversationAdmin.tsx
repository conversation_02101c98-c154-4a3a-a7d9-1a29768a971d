'use client';

import { useCurrentPatient } from '~/hooks/patient';
import { DoctorAdminConversation } from './Chat';
import { CreateConversation } from './CreateConversation';

export function ConversationAdmin() {
  const patient = useCurrentPatient();

  if (!patient) return null;

  return (
    <>
      {' '}
      {patient.doctorAdminConversation?.id ? (
        <DoctorAdminConversation
          conversationId={patient.doctorAdminConversation?.id}
        />
      ) : (
        <CreateConversation patientId={patient.id} />
      )}
    </>
  );
}
