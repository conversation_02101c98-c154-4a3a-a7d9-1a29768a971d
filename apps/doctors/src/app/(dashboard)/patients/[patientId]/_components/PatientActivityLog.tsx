import React from 'react';
import { XIcon } from 'lucide-react';

import { Button } from '@willow/ui/base/button';
import {
  Drawer,
  DrawerContent,
  DrawerOverlay,
  DrawerPortal,
  DrawerT<PERSON>le,
  DrawerTrigger,
} from '@willow/ui/base/vaul';
import { Loader } from '@willow/ui/loader';
import { PatientActivityLog as ActivityLog } from '@willow/ui/PatientActivityLog/index';

import { useGetPatientActivityLog } from '~/hooks/patient';

export function PatientActivityLog({ patientId }: { patientId: string }) {
  const [isOpenDrawer, setIsOpenDrawer] = React.useState(false);
  const { data, isLoading } = useGetPatientActivityLog(patientId, {
    includes: 'PATIENT_NOTE_CREATED',
  });

  if (isLoading)
    return (
      <Button variant="denimOutline" size="sm" disabled>
        Loading...
      </Button>
    );

  if (!data)
    return (
      <Button variant="denimOutline" size="sm" disabled>
        Notes unavailable
      </Button>
    );

  return (
    <div className="relative flex-1 overflow-auto">
      <Drawer
        direction="right"
        open={isOpenDrawer}
        onOpenChange={(value) => {
          if (!value) setIsOpenDrawer(false);
        }}
      >
        <DrawerTrigger
          asChild
          onClick={() => {
            setIsOpenDrawer(true);
          }}
        >
          <Button variant="denimOutline" size="sm" loading={isLoading}>
            Notes {`(${data.length})`}
          </Button>
        </DrawerTrigger>
        <DrawerPortal>
          <DrawerOverlay className="fixed inset-0 z-20 bg-dark/40" />
          <DrawerContent className="fixed bottom-0 right-0 top-0 z-20 flex w-[600px] !touch-none !select-text">
            <DrawerTitle className="hidden">Patient Activity</DrawerTitle>
            <div className="w-full bg-white">
              <div className="flex items-center justify-between p-4">
                <h1 className="text-xl font-bold">Patient Activity</h1>
                <XIcon
                  size={24}
                  className="cursor-pointer text-denim"
                  onClick={() => setIsOpenDrawer(false)}
                />
              </div>
              <div className="border-b border-gray-300" />
              <LogList patientId={patientId} />
            </div>
          </DrawerContent>
        </DrawerPortal>
      </Drawer>
    </div>
  );
}

function LogList({ patientId }: { patientId: string }) {
  const [isHideEvents, setIsHideEvents] = React.useState(false);
  const handleHideEvents = () => {
    setIsHideEvents((prev) => !prev);
  };

  const onboardingEvents = [
    'ONBOARDING_UPLOAD_ID_PHOTO_SKIPPED',
    'ONBOARDING_TREATMENT_FORM_SELECTED',
    'ONBOARDING_TREATMENT_SELECTED',
    'ONBOARDING_SHIPPING_ADDRESS_UPDATED',
    'ONBOARDING_CHECKOUT_STARTED',
    'ONBOARDING_CHECKOUT_COMPLETED',
    'ONBOARDING_QUESTIONNAIRE_COMPLETED',
    'PATIENT_ID_PHOTO_UPDATED',
    'PATIENT_VISIT_STARTED',
    'PATIENT_ACCOUNT_CREATED',
  ];
  const chatEvents = ['CONVERSATION_CHAT_MESSAGE_CREATED'];

  const { data, isLoading, error } = useGetPatientActivityLog(patientId, {
    includes: isHideEvents ? 'PATIENT_NOTE_CREATED' : undefined,
    excludes: chatEvents.concat(onboardingEvents).join(','),
  });

  if (isLoading) return <Loader className="h-screen" size="xl" />;

  if (!data) return <div className="p-6">No data available</div>;

  return (
    <ActivityLog
      data={data}
      isLoading={isLoading}
      error={error}
      isHideEvents={isHideEvents}
      handleHideEvents={handleHideEvents}
      className="max-h-[95vh]"
    />
  );
}
