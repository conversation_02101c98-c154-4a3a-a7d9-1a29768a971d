import type { OnboardingProps } from '@/app/(connected)/onboarding/(onboarding)/questionnaire/[slug]/page';
import React, { useState } from 'react';
import Image from 'next/image';
import arrow from '@/assets/svg/arrow-circle.svg';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormLoader,
  FormMessage,
} from '@/components/ui/form';
import { useOnboardingQuestionnaire } from '@/hooks/onboarding';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

const schema = z.object({
  objectives: z.array(z.string()).min(1, 'Please select at least one option'),
});

type FormType = z.infer<typeof schema>;

const Objectives = ({ data, callback }: OnboardingProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const objectives = [
    {
      id: 'loseFatWithoutLosingMuscle',
      label: 'I want to lose fat without losing muscle',
    },
    {
      id: 'decreaseFatigueIncreaseEnergy',
      label: 'I want to decrease fatigue and increase my energy',
    },
    {
      id: 'supportHeartHealth',
      label: "I'm interested in supporting my heart health",
    },
    {
      id: 'improveSkinLookAndFeel',
      label: "I'd like to improve the look and feel of my skin",
    },
    { id: 'dosingConcerns', label: "I'm concerned about dosing correctly" },
    {
      id: 'noRefrigerationNeeded',
      label: "I need medication that doesn't require refrigeration",
    },
    { id: 'travelFriendly', label: "I'd like something travel-friendly" },
    { id: 'none', label: 'No, none of these apply to me' },
  ];

  // Initialize form with existing data or default to [] if empty
  const existingObjectives = data.context.questionnaire.objectives || [];

  const form = useForm<FormType>({
    resolver: zodResolver(schema),
    values: {
      objectives: existingObjectives.length > 0 ? existingObjectives : [],
    },
  });

  const { mutateAsync: next, isPending } = useOnboardingQuestionnaire();
  const onSubmit = async ({ objectives }: FormType) => {
    try {
      setIsLoading(true);

      // If NONE is selected, clear any other selections
      let objectivesList = objectives;
      if (objectivesList.includes('none')) {
        objectivesList = [];
      }

      const response = await next({
        value: { objectives: objectivesList },
      });
      callback(response.data);
    } catch (e: any) {
      setIsLoading(false);
      form.setError('objectives', {
        message: e.response.data?.message,
      });
    }
  };

  // Check if any option is selected
  const hasSelection = form.watch('objectives')?.length > 0;

  return (
    <OnboardingTitle title="Do any of the following apply to you?">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex flex-col gap-10"
        >
          <FormLoader isLoading={isLoading || isPending}>
            <div className="gap-5">
              <FormField
                control={form.control}
                name={'objectives'}
                render={() => (
                  <FormItem>
                    {objectives.map((item, index) => (
                      <React.Fragment key={item.id}>
                        {/* Add a spacer div before the last item */}
                        {index === objectives.length - 1 && (
                          <div className="h-12" />
                        )}
                        <FormField
                          control={form.control}
                          name="objectives"
                          render={({ field }) => (
                            <FormItem
                              className={cn(
                                'medical',
                                field.value?.includes(item.id) &&
                                  'bg-[#445f85]',
                              )}
                            >
                              <FormControl>
                                <div
                                  className={cn(
                                    'rounded border border-[#63799A] p-5',
                                  )}
                                >
                                  <FormLabel
                                    htmlFor={`objectives-${item.id}`}
                                    className="flex items-center text-lg text-white"
                                  >
                                    <Checkbox
                                      id={`objectives-${item.id}`}
                                      checked={field.value?.includes(item.id)}
                                      onCheckedChange={(checked) => {
                                        if (checked) {
                                          // If NONE is selected, clear all other selections
                                          if (item.id === 'none') {
                                            return field.onChange(['none']);
                                          }
                                          // If any other option is selected, remove NONE if it's selected
                                          const newValue = [
                                            ...field.value,
                                            item.id,
                                          ].filter((value) => value !== 'none');
                                          return field.onChange(newValue);
                                        } else {
                                          return field.onChange(
                                            field.value?.filter(
                                              (value) => value !== item.id,
                                            ),
                                          );
                                        }
                                      }}
                                      className="mr-4 data-[state=checked]:bg-electric"
                                    />
                                    {item.label}
                                  </FormLabel>
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </React.Fragment>
                    ))}
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Button
              size={'lg'}
              variant={'electric'}
              className="flex w-full max-w-none justify-between"
              type={'submit'}
              disabled={isPending || !hasSelection}
            >
              <span>CONTINUE</span>
              <Image alt="arrow" src={arrow} style={{ objectFit: 'contain' }} />
            </Button>
          </FormLoader>
        </form>
      </Form>
    </OnboardingTitle>
  );
};

export default Objectives;
