'use client';

import MaxWidthWrapper from '@/components/MaxWidthWrapper';
import DashboardTitle from '@/components/patient-dashboard/DashboardTitle';

import BackButton from '~/components/patient-dashboard/BackButton';
import {
  CardDownloadHsaDoc,
  CardDownloadLetterOfMedicalNecessity,
} from '~/components/ui/card-download-insurance';

const InsuranceDocumentsList = () => {
  return (
    <MaxWidthWrapper className="flex flex-1 flex-col md:max-w-[2000px] md:px-0">
      <div className="flex flex-1 flex-col md:my-16 md:mb-20 md:gap-4 lg:mx-28 lg:gap-20">
        <div className="mb-8 mt-6 flex px-6 md:mt-0">
          <BackButton label="Return to Patient Area" link="/dashboard" />
        </div>

        <div className="flex flex-1 flex-col gap-10 md:px-6 lg:flex-row lg:gap-20">
          <div className="w-full flex-col justify-between px-6 md:flex md:w-3/4 md:px-0">
            <DashboardTitle title="Insurance Documents" />
          </div>

          <div className="flex h-auto w-full flex-1 flex-col gap-4 px-6 md:h-[500px] md:flex-auto md:gap-0 md:px-0">
            <div className="mt-6 md:mt-0">
              <CardDownloadHsaDoc />
              <CardDownloadLetterOfMedicalNecessity />
            </div>
            <p className="mt-4 text-sm leading-relaxed text-gray-600 md:mt-0 md:text-base">
              We understand that some of our patients may be looking to submit
              their medical expenses to their insurance provider for
              reimbursement. To assist with this process, we have made it easy
              for you to access the necessary documents, such as the Health
              Savings Account (HSA). By accessing these documents and submitting
              them to your insurance provider, you may be able to recoup some of
              the costs of your medical treatment. Please note that the
              submission and management of these claims is the patient's
              responsibility.
            </p>
          </div>
        </div>
      </div>
    </MaxWidthWrapper>
  );
};

export default InsuranceDocumentsList;
