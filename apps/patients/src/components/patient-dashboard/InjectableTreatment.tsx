'use client';

import React, { useEffect, useRef, useState } from 'react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { InfoIcon } from 'lucide-react';

import { Triangle } from '~/assets/svg/Triangle';
import { cn } from '~/lib/utils';

interface InjectableTreatmentProps {
  maxUnits: number;
  unitStep: number;
  currentPatientUnit: number;
  className?: string;
}

const InjectableTreatment: React.FC<InjectableTreatmentProps> = ({
  maxUnits,
  unitStep,
  currentPatientUnit,
  className = '',
}) => {
  const numSteps = maxUnits / unitStep;
  const steps = Array.from({ length: numSteps }, (_, i) => (i + 1) * unitStep);
  const componentRef = useRef<HTMLDivElement>(null);
  const [triangleContainerWidth, setTriangleContainerWidth] = useState(0);

  const getPercentageValue = (unit: number) => (unit / maxUnits) * 100;

  useEffect(() => {
    if (componentRef.current) {
      setTriangleContainerWidth(componentRef.current.offsetWidth);
    }
  }, [currentPatientUnit]);

  return (
    <TooltipProvider>
      <div className={cn('flex w-full flex-col', className)}>
        <span className="flex items-center gap-[6px] pb-1 text-sm font-semibold text-white">
          Your Syringe{' '}
          <Tooltip>
            <TooltipTrigger asChild>
              <InfoIcon size={16} />
            </TooltipTrigger>
            <TooltipContent>
              <p>Total syringe size may be different than what is shown</p>
            </TooltipContent>
          </Tooltip>
        </span>

        <div className="relative flex w-full justify-between">
          <span className="text-[13px] text-white">0</span>

          <span className="text-[13px] text-white">{maxUnits}</span>
        </div>

        <div className="relative h-[25px] w-full overflow-hidden rounded-full bg-electric">
          <div
            className="h-full bg-white"
            style={{ width: `${getPercentageValue(currentPatientUnit)}%` }}
          />

          {steps.map((step) => (
            <div
              className="absolute top-0 h-full w-[1px] bg-denim-darker"
              style={{ left: `${getPercentageValue(step)}%` }}
            />
          ))}
        </div>

        <div
          className="relative"
          style={{ width: `${getPercentageValue(currentPatientUnit)}%` }}
        >
          <div
            ref={componentRef}
            className="absolute bottom-[-25px] top-[-5px] flex flex-col items-center"
            style={{ right: `-${triangleContainerWidth / 2}px` }}
          >
            <Triangle />

            <span className="text-[13px] font-medium text-electric">
              {currentPatientUnit}
            </span>
          </div>
        </div>
      </div>
    </TooltipProvider>
  );
};

export default InjectableTreatment;
