import { useState } from 'react';
import Image from 'next/image';
import program90DaysImage from '@/assets/png/90-days-program.png';
import { FormLabel } from '@/components/ui/form';
import { RadioGroupItem } from '@/components/ui/radio-group';
import { TreatmentDialog } from '@/components/ui/TreatmentDialog';
import classNames from 'classnames';

import { MobileTreatmentCard } from './MobileTreatmentCard';

interface Treatment90DayProgramCardProps {
  id: string;
  name: string;
  image: string;
  price: number;
  isSelected: boolean;
  description?: string;
  type?: 'core' | 'additional';
  warning?: string | null;
  weightLoss?: number;
  onLearnMore?: () => void;
}

export const Treatment90DayProgramCard = ({
  id,
  name,
  image,
  price,
  isSelected,
  description = 'Our 90-Day Program pairs daily fast-dissolving semaglutide tablets with 12 weeks of habit coaching and unlimited access to your doctor.',
  type = 'core',
  warning,
  weightLoss = 0,
  onLearnMore,
}: Treatment90DayProgramCardProps) => {
  const [showInfoModal, setShowInfoModal] = useState(false);

  const renderPrice = () => (
    <div>
      <span>${price}</span>
      {type === 'core' && <span className="text-sm">/mo</span>}
    </div>
  );

  const learnMoreDescription = (
    <div className="flex flex-col gap-4 text-left text-sm font-normal text-white">
      <p>
        Our 90-Day Program combines daily, fast-dissolving semaglutide tablets
        with a comprehensive 12-week habit-coaching curriculum designed to
        create lasting lifestyle changes.
      </p>
      <p>
        You'll receive unlimited access to your board-certified doctor
        throughout your journey, ensuring personalized support and medical
        guidance every step of the way.
      </p>
      <p>
        The program includes structured modules covering nutrition, movement,
        mindset, and sleep - all scientifically designed to work synergistically
        with your GLP-1 medication for optimal results.
      </p>
    </div>
  );

  return (
    <div>
      <FormLabel
        htmlFor={id}
        className={classNames(
          'flex flex-col items-start justify-center gap-2.5 rounded-lg border border-glass p-7 backdrop-blur-xl',
          // Desktop specific styles
          'md:relative md:block md:p-5',
          {
            ['border-electric bg-white bg-opacity-10']: isSelected,
            ['md:bg-[#445e85]']: !isSelected,
          },
        )}
      >
        {/* Mobile Layout (default) */}
        <MobileTreatmentCard
          id={id}
          name={name}
          image={image}
          price={renderPrice()}
          isSelected={isSelected}
          description={description}
          type={type}
          warning={warning}
          weightLoss={weightLoss}
          tags={['New!']}
          onClickLearnMore={() => setShowInfoModal(true)}
          learnMore={onLearnMore ? 'Learn more about ' + name : undefined}
          customTitle={
            <h3 className="text-xl leading-tight text-white">
              <span className="font-medium">90 Day</span> Program
            </h3>
          }
          radioButton={
            <RadioGroupItem
              className="border-white"
              value={id}
              id={id}
              checked={isSelected}
            />
          }
        />

        {/* Desktop Layout */}
        <div className="relative hidden h-full items-center px-7 md:flex">
          {/* Radio button */}
          <div className="mr-6 flex-shrink-0">
            <RadioGroupItem
              className="border-white"
              value={id}
              id={id}
              checked={isSelected}
            />
          </div>

          {/* Content container with proper width constraint */}
          <div className="flex flex-1 flex-col gap-2 pr-[300px]">
            {/* Badge */}
            <div
              className={classNames(
                'inline-flex self-start rounded-xl px-2 py-1',
                {
                  ['bg-electric']: isSelected,
                  ['bg-[#e6fe74]']: !isSelected,
                },
              )}
            >
              <span className="text-xs font-medium italic text-[#2f4c78]">
                New!
              </span>
            </div>

            {/* Title */}
            <h3 className="text-[32px] font-light leading-tight text-white">
              <span className="font-medium">90 Day</span> Program
            </h3>

            {/* Description */}
            <p className="min-h-[4lh] text-sm font-normal leading-relaxed text-white">
              {description}
            </p>

            {/* Learn more link */}
            {onLearnMore && (
              <button
                type="button"
                onClick={() => setShowInfoModal(true)}
                className="self-start text-sm text-white underline hover:no-underline"
              >
                Learn more
              </button>
            )}
          </div>

          {/* Price - positioned above the image */}
          <div className="absolute right-2 top-7 text-white">
            <span className="text-base">Starting at </span>
            <span className="text-2xl font-normal">${price}</span>
            <span className="text-base">/mo</span>
          </div>

          {/* Image - positioned absolutely on the right */}
          <div className="absolute -bottom-2 -right-5 h-[170px] w-[280px]">
            <Image
              src={program90DaysImage}
              width={280}
              height={170}
              alt="90 Day Program"
              className="h-full w-full object-contain object-right-bottom"
            />
          </div>
        </div>
      </FormLabel>

      {showInfoModal && onLearnMore && (
        <TreatmentDialog
          title={name}
          image={image}
          description={learnMoreDescription}
          confirmBtnText="CLOSE"
          confirmBtnClick={() => setShowInfoModal(false)}
          closeBtnClick={() => {
            setShowInfoModal(false);
          }}
        />
      )}
    </div>
  );
};
