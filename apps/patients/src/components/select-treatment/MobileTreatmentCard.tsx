import type { ReactNode } from 'react';
import Image from 'next/image';
import classNames from 'classnames';

interface MobileTreatmentCardProps {
  id: string;
  name: string;
  image: string;
  price: ReactNode;
  isSelected: boolean;
  description?: string;
  type?: 'core' | 'additional';
  warning?: string | null;
  weightLoss?: number;
  tags?: string[];
  onClickLearnMore?: () => void;
  learnMore?: string;
  customTitle?: ReactNode;
  radioButton?: ReactNode;
}

export const MobileTreatmentCard = ({
  name,
  image,
  price,
  isSelected,
  description,
  type = 'core',
  warning,
  weightLoss = 0,
  tags = [],
  onClickLearnMore,
  learnMore,
  customTitle,
  radioButton,
}: MobileTreatmentCardProps) => {
  return (
    <div className="flex items-start gap-3 md:hidden">
      {/* Radio Button */}
      {radioButton && <div className="flex-shrink-0 pt-1">{radioButton}</div>}

      {/* Content wrapper */}
      <div className="flex flex-1 flex-col gap-3">
        {/* Image and Title Row */}
        <div className="flex gap-3">
          {/* Image */}
          <div className="flex-shrink-0">
            <div className="h-20 w-20 rounded-lg bg-gray-200">
              <Image
                src={image}
                width={80}
                height={80}
                alt="treatment"
                className="h-full w-full rounded-lg object-cover"
              />
            </div>
          </div>

          {/* Title and Tag */}
          <div className="flex flex-1 flex-col justify-center gap-1">
            {/* Tags */}
            {tags.length > 0 && (
              <div className="flex gap-2">
                {tags.map((tag, index) => (
                  <div
                    key={index}
                    className={classNames(
                      'inline-flex items-center rounded-xl px-2.5 py-1',
                      {
                        ['bg-electric text-denim']: isSelected,
                        ['bg-[#e6fe74] text-denim']: !isSelected,
                      },
                    )}
                  >
                    <span className="text-xs font-medium italic">{tag}</span>
                  </div>
                ))}
              </div>
            )}

            {/* Title */}
            {customTitle || (
              <h3 className="text-xl font-medium leading-tight text-white">
                {name}
              </h3>
            )}
          </div>
        </div>

        {/* Rest of content aligned with image */}
        <div className="flex flex-col gap-2">
          {/* Description */}
          {description && (
            <p className="text-sm font-normal leading-relaxed text-white">
              {description}
            </p>
          )}

          {/* Warning */}
          {warning && (
            <p className="text-sm font-normal text-white">{warning}</p>
          )}

          {/* Learn More */}
          {learnMore && onClickLearnMore && (
            <button
              type="button"
              onClick={onClickLearnMore}
              className="self-start text-sm text-white underline hover:no-underline"
            >
              Learn more
            </button>
          )}

          {/* Weight Loss Estimate */}
          {weightLoss > 0 && (
            <p className="text-sm font-normal text-white">
              Estimated weight loss based on your current weight: {weightLoss}{' '}
              lbs.
            </p>
          )}

          {/* In Stock Badge */}
          {type === 'core' && (
            <div className="self-start">
              <div className="inline-flex items-center gap-1 rounded-md bg-gray-200 px-2 py-1">
                <div className="h-2 w-2 rounded-full bg-[#21d849]" />
                <span className="text-xs font-medium text-denim">
                  IN STOCK NOW
                </span>
              </div>
            </div>
          )}

          {/* Price */}
          <div className="text-xl font-normal text-white">{price}</div>
        </div>
      </div>
    </div>
  );
};
