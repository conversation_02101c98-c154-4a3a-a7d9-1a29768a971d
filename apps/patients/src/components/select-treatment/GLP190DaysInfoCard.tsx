import { Circle<PERSON>heck } from 'lucide-react';

const GLP190DaysInfoCard = () => (
  <div className="inline-flex flex-col items-start justify-start gap-3 rounded-2xl border border-white p-6">
    <span className="text-lg font-medium leading-snug text-white">
      Personalized GLP-1 medication treatment
    </span>

    <ul className="flex flex-col gap-2">
      <li className="flex gap-1 text-sm text-white">
        <CircleCheck /> Custom Dosage Schedule Doctor vetted dosages are
        adjusted by your <PERSON> physician to maximize weight-loss benefits while
        minimizing side effects.
      </li>
      <li className="flex gap-1 text-sm text-white">
        <CircleCheck /> Daily, Needle-Free Semaglutide Mint-flavored,
        fast-dissolving tablets deliver a full GLP-1 dose every morning—no
        injections or refrigeration required.
      </li>
      <li className="flex gap-1 text-sm text-white">
        <CircleCheck />
        Synergistic Habit Integration Our 12-week behavioral framework anchors
        each dose in tiny, science-backed habits (nutrition, movement, mindset,
        sleep) for lasting change.
      </li>
    </ul>
  </div>
);

export default GLP190DaysInfoCard;
