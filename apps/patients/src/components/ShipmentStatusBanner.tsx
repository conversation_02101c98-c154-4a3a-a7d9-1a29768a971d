import { useQuery } from '@tanstack/react-query';
import { useAtomValue } from 'jotai';

import { Banner } from '@willow/ui/base/banner';
import { apiClient } from '@willow/utils/api/client';

import { useProfile } from '~/hooks/profile';
import { interceptorInjected } from '~/store/store';

export const ShipmentStatusBanner = () => {
  const profile = useProfile();

  const { data: shipmentStatus } = useGetPatientShipmentStatus(profile.id);

  if (!shipmentStatus || shipmentStatus.length === 0) return null;

  if (shipmentStatus[0]?.status === 'delivered')
    return (
      <Banner variant={'success'}>
        Package Status: Delivered.{' '}
        <a
          href={shipmentStatus[0]?.trackingLink}
          target="_blank"
          rel="noopener noreferrer"
          className="font-semibold"
        >
          Click here
        </a>{' '}
        to track your package.
      </Banner>
    );

  // Map non-delivered statuses to appropriate variants and messages
  const getVariantAndMessage = (
    status: string,
  ): { variant: 'info' | 'warning' | 'error'; message: string } => {
    switch (status) {
      case 'awaitingPickup':
        return { variant: 'info', message: 'Package Status: Awaiting Pickup.' };
      case 'shipped':
        return { variant: 'info', message: 'Package Status: Shipped.' };
      case 'outfordelivery':
        return {
          variant: 'info',
          message: 'Package Status: Out for Delivery.',
        };
      case 'delayeddelivery':
        return {
          variant: 'warning',
          message: 'Package Status: Delayed Delivery.',
        };
      case 'faileddelivery':
        return {
          variant: 'error',
          message: 'Package Status: Failed Delivery.',
        };
      case 'lostdamaged':
        return {
          variant: 'error',
          message: 'Package Status: Lost or Damaged.',
        };
      default:
        return { variant: 'info', message: 'Package Status: In Transit.' };
    }
  };

  const { variant, message } = getVariantAndMessage(
    shipmentStatus[0]?.status || '',
  );

  return (
    <Banner variant={variant}>
      {message}{' '}
      <a
        href={shipmentStatus[0]?.trackingLink}
        target="_blank"
        rel="noopener noreferrer"
        className="font-semibold"
      >
        Click here
      </a>{' '}
      to track your package.
    </Banner>
  );
};

const useGetPatientShipmentStatus = (patientId: string) => {
  const injected = useAtomValue(interceptorInjected);
  return useQuery({
    queryKey: ['shipment-status', patientId],
    enabled: injected,
    queryFn: () =>
      apiClient
        .get(`/shipment/${patientId}/status`)
        .then((res) => res.data as ShipmentStatus),
  });
};

type ShipmentStatus = {
  userId: string;
  type: string;
  status: string;
  trackingNumber: string;
  trackingLink: string;
  createdAt: string;
  updatedAt: string;
}[];
