import * as React from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { cva } from 'class-variance-authority';
import { ChevronRight } from 'lucide-react';

interface CardLink2Props {
  label: string;
  link: string;
  mailto?: boolean;
  icon: React.ReactNode;
  variant?: any;
  onclick?: any;
  target?: string;
  className?: string;
  description?: string;
}

const labelVariants = cva('text-xl font-normal md:text-lg md:font-medium', {
  variants: {
    variant: {
      outline: 'text-denim',
      link: 'text-white',
    },
  },
  defaultVariants: {
    variant: 'outline',
  },
});

const buttonVariants = cva(
  'flex h-auto w-full justify-between rounded-2xl bg-white px-4 py-6 md:py-4',
  {
    variants: {
      variant: {
        link: 'px-0 py-2',
      },
    },
  },
);

const CardLink2 = ({
  label,
  link,
  icon,
  onclick,
  target,
  variant = 'outline',
  className,
  description,
}: CardLink2Props) => {
  const Component = link.includes('mailto') ? 'a' : Link;
  return (
    <Component href={link} className="w-full flex-1" target={target}>
      <button
        type="button"
        onClick={onclick}
        className={cn(buttonVariants({ variant }), className)}
      >
        <div className="flex w-full flex-row items-center gap-4">
          {icon && <div>{icon}</div>}
          <div className="flex w-full flex-col gap-2">
            <div className="flex w-full items-center justify-between">
              <div className="flex items-center gap-2">
                <span
                  className={cn(
                    labelVariants({ variant }),
                    'w-full text-wrap text-base font-medium leading-normal md:text-xl',
                  )}
                >
                  {label}
                </span>
              </div>
              <ChevronRight className="text-denim" />
            </div>
            <div className="w-full text-left">
              {description && (
                <span className="text-wrap text-xs text-denim md:text-sm">
                  {description}
                </span>
              )}
            </div>
          </div>
        </div>
      </button>
    </Component>
  );
};

CardLink2.displayName = 'CardLink2';

export { CardLink2 };
