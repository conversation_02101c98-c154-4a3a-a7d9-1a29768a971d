import * as React from 'react';
import { useState } from 'react';
import { FileIcon } from 'lucide-react';

import { Button } from '@willow/ui/base/button';
import { Dialog, DialogContent, DialogTitle } from '@willow/ui/base/dialog';
import { apiClient } from '@willow/utils/api/client';

import { useProfile } from '~/hooks/profile';

const downloadDocument = (documentName: string, content: Blob) => {
  const url = window.URL.createObjectURL(content);
  const a = document.createElement('a');
  a.href = url;
  a.download = documentName;
  a.click();
  window.URL.revokeObjectURL(url);
};

const CardDownloadHsaDoc = () => {
  const [open, setOpenDownloadDialog] = useState(false); // Cambiar a useState para manejar el estado reactivo del modal
  return (
    <div className="w-full pb-2">
      <CardDownloadHsaDocDialog open={open} setOpen={setOpenDownloadDialog} />
      <button
        type="button"
        onClick={() => setOpenDownloadDialog(true)}
        className="flex h-auto w-full justify-between rounded-2xl border-none bg-white px-4 py-6 transition hover:scale-[1.01] hover:bg-white hover:shadow-lg md:py-4"
      >
        <div className="flex w-full flex-col gap-2">
          <div className="flex w-full items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="flex aspect-square w-[42px] items-center justify-center rounded-md bg-[#2F4C781A] !bg-opacity-0 md:w-[42px]">
                <FileIcon color="#2F4C78" size={20} />
              </div>

              <span className="w-full text-wrap text-base font-medium leading-normal text-denim md:text-xl">
                HSA Receipt
              </span>
            </div>
            <div className="rounded-full bg-denim p-3 font-bold text-white">
              DOWNLOAD
            </div>
          </div>
        </div>
      </button>
    </div>
  );
};

// Letter of Medical Necessity download card
const CardDownloadLetterOfMedicalNecessity = () => {
  const profile = useProfile();
  const [errorMessage, setErrorMessage] = useState('');
  const handleDownloadLetterOfMedicalNecessity = async () => {
    try {
      const response = await apiClient.get(
        `/patient/insurance/letter-of-medical-necessity`,
        {
          responseType: 'blob',
        },
      );

      const document: Blob = response.data as Blob;
      downloadDocument(
        `willow-letter_of_medical_necessity-${profile.user.email}.pdf`,
        document,
      );
    } catch {
      setErrorMessage('Failed to download letter, contact patient support.');
    }
  };

  return (
    <div className="w-full pb-2">
      <button
        type="button"
        className="flex h-auto w-full justify-between rounded-2xl border-none bg-white px-4 py-6 transition hover:scale-[1.01] hover:bg-white hover:shadow-lg md:py-4"
        onClick={handleDownloadLetterOfMedicalNecessity}
      >
        <div className="flex w-full flex-col gap-2">
          <div className="flex w-full items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="flex aspect-square w-[42px] items-center justify-center rounded-md bg-[#2F4C781A] !bg-opacity-0 md:w-[42px]">
                <FileIcon color="#2F4C78" size={20} />
              </div>

              <span className="w-full text-wrap text-base font-medium leading-normal text-denim md:text-xl">
                Letter of Medical Necessity
              </span>
            </div>
            <div className="rounded-full bg-denim p-3 font-bold text-white">
              DOWNLOAD
            </div>
          </div>
          {errorMessage && (
            <div className="w-full text-left">
              <span className="text-wrap text-xs text-denim text-red-500 md:text-sm">
                {errorMessage}
              </span>
            </div>
          )}
        </div>
      </button>
    </div>
  );
};

interface CardDownloadLinkDialogProps {
  open: boolean;
  setOpen: (value: boolean) => void;
}

const CardDownloadHsaDocDialog = ({
  setOpen,
  open,
}: CardDownloadLinkDialogProps) => {
  const profile = useProfile();
  const firstYear = new Date(profile.createdAt).getFullYear() || 2020;
  const [year, setYear] = useState(firstYear);
  const [errorMessage, setErrorMessage] = useState('');
  const handleDownloadReceipt = async () => {
    try {
      const response = await apiClient.get(
        `/patient/insurance/hsa-receipt/${year}`,
        { responseType: 'blob' },
      );

      setErrorMessage('');

      const document: Blob = response.data as Blob;
      downloadDocument(
        `willow-hsa_receipt-${profile.user.email}-${year}.pdf`,
        document,
      );
    } catch {
      setErrorMessage('Failed to download receipt, contact patient support.');
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent>
        <div className="flex flex-col gap-4 sm:flex-row">
          <DialogTitle asChild>
            <p className="text-base font-medium text-denim">
              Download HSA Document
            </p>
          </DialogTitle>
          <form
            onSubmit={(e) => {
              e.preventDefault();
              void handleDownloadReceipt();
            }}
            className="flex flex-col gap-4"
          >
            <label className="text-base font-medium text-denim">
              for{' '}
              <span className="font-bold text-denim">
                {' '}
                {profile.user.firstName} {profile.user.lastName}
              </span>
            </label>
            <select
              name="year"
              value={year}
              onChange={(e) => setYear(Number(e.target.value))}
              className="w-full rounded-md border border-gray-300 p-2 text-base"
            >
              {(() => {
                const options = [];
                for (let i = firstYear; i <= new Date().getFullYear(); i++) {
                  options.push(
                    <option key={i} value={i}>
                      {i}
                    </option>,
                  );
                }
                return options;
              })()}
            </select>
            <div className="flex justify-end gap-4">
              <Button
                onClick={() => setOpen(false)}
                variant="denimOutline"
                className="w-full md:w-auto"
                type="button"
                size="sm"
              >
                CANCEL
              </Button>
              <Button
                variant="denim"
                className="w-full text-white md:w-auto"
                type="submit"
                size="sm"
              >
                DOWNLOAD RECEIPT
              </Button>
            </div>
          </form>
        </div>
        {errorMessage && (
          <div className="w-full text-left">
            <span className="text-wrap text-xs text-denim text-red-500 md:text-sm">
              {errorMessage}
            </span>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
CardDownloadHsaDocDialog.displayName = 'CardDownloadLinkDialog';
CardDownloadHsaDoc.displayName = 'CardDownloadHsaDoc';
CardDownloadLetterOfMedicalNecessity.displayName =
  'CardDownloadLetterOfMedicalNecessity';
export { CardDownloadHsaDoc, CardDownloadLetterOfMedicalNecessity };
