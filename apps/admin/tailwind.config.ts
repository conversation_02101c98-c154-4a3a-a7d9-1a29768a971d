import type { Config } from 'tailwindcss';

import baseConfig from '@willow/tailwind-config/web';

export default {
  // We need to append the path to the UI package to the content array so that
  // those classes are included correctly.
  content: [
    ...baseConfig.content,
    '../../packages/ui/src/**/*.{ts,tsx}',
    '../../packages/chat/src/**/*.{ts,tsx}',
  ],
  presets: [baseConfig],
  theme: {
    extend: {
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic':
          'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
      },
      colors: {
        bw: {
          50: '#787878',
          90: '#1E1E1E',
        },
        yellow: '#FFECCA',
        error: '#EB5757',
      },
      fontSize: {
        sm2: '13px',
      },
      fontFamily: {
        neue: ['--font-neue', 'sans-serif'],
        objectSans: ['--font-pp', 'sans-serif'],
      },
    },
  },
} satisfies Config;
