import type { AxiosError } from 'axios';
import { useEffect, useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import axios, { isAxiosError } from 'axios';

import type {
  AdminPatientById,
  EligibleDoctor,
  EligibleStateWithDoctors,
  PaginatedPatients,
  PatientGroup,
  PatientIntake,
} from '@willow/db';
import type { AuditLogApi } from '@willow/utils/audit-log/index';
import type { CancelPatientSchema } from '@willow/utils/patient/cancel';
import { PatientGroupsList } from '@willow/db/client';
import { useToast } from '@willow/ui/base/use-toast';
import { apiClient } from '@willow/utils/api/client';
import { useInvalidatedQuery } from '@willow/utils/react-query';

import { useRefreshToken } from '~/hooks/login';
import { useAccessToken } from './useHasAccessToken';

// Regular hook for backward compatibility
export const useGetPatientsGroupCounts = () => {
  const accessToken = useAccessToken();

  return useQuery({
    queryKey: ['patients', 'group-counts'],
    staleTime: 1000 * 10, // 10 seconds to match backend cache TTL
    refetchOnWindowFocus: false,
    // This will deduplicate requests that happen at the same time
    // Only one request will be made even if multiple components call this hook
    queryFn: async () => {
      if (!accessToken?.accessToken) {
        throw new Error('No access token available');
      }

      // Use axios directly to get the raw response for streaming
      const response = await axios.get('/admin/patients/group-counts', {
        baseURL: apiClient.defaults.baseURL,
        headers: {
          Accept: 'application/json',
          Authorization: `Bearer ${accessToken.accessToken}`,
        },
        responseType: 'text', // Important for streaming text response
      });

      // Parse the JSON array response
      const data = JSON.parse(response.data);

      // Convert the array of {group, count} objects to a Record<PatientGroup, number>
      const result: Record<PatientGroup, number> = {} as Record<
        PatientGroup,
        number
      >;

      // Initialize all groups to 0
      for (const group of PatientGroupsList) {
        result[group] = 0;
      }

      // Update with actual counts if data is valid
      if (Array.isArray(data)) {
        for (const item of data) {
          if (
            item &&
            typeof item === 'object' &&
            'group' in item &&
            'count' in item
          ) {
            const group = item.group as PatientGroup;
            const count = Number(item.count);
            if (PatientGroupsList.includes(group)) {
              result[group] = count;
            }
          }
        }
      } else {
        console.warn('Received non-array data:', data);
      }

      return result;
    },
  });
};

// New streaming hook that updates counts as they arrive in real-time

export const useStreamingPatientsGroupCounts = () => {
  const accessToken = useAccessToken();
  const [counts, setCounts] = useState<Record<PatientGroup, number>>(
    {} as Record<PatientGroup, number>,
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [isComplete, setIsComplete] = useState(false);
  const [processedGroups, setProcessedGroups] = useState<Set<string>>(
    new Set(),
  );

  useEffect(() => {
    const controller = new AbortController();

    const fetchData = async () => {
      if (!accessToken?.accessToken) {
        setError(new Error('No access token available'));
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);
      setIsComplete(false);
      setProcessedGroups(new Set());

      try {
        // Initialize all counts to 0
        const initialCounts: Record<PatientGroup, number> = {} as Record<
          PatientGroup,
          number
        >;
        // Set all patient groups to 0 initially
        for (const group of PatientGroupsList) {
          initialCounts[group] = 0;
        }
        setCounts(initialCounts);

        // Use the fetch API for true streaming support
        const apiUrl = `${apiClient.defaults.baseURL}/admin/patients/group-counts`;

        const response = await fetch(apiUrl, {
          method: 'GET',
          headers: {
            Accept: 'text/plain',
            Authorization: `Bearer ${accessToken.accessToken}`,
          },
          signal: controller.signal,
        });

        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        if (!response.body) {
          throw new Error('Response body is null');
        }

        // Get a reader from the response body stream
        const reader = response.body.getReader();

        // We're no longer in initial loading state once we start receiving data
        setIsLoading(false);

        // Buffer to accumulate partial lines
        let buffer = '';

        // Process the stream
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            // Stream is complete
            break;
          }

          // Convert the chunk to text and add it to our buffer
          const chunk = new TextDecoder().decode(value);
          buffer += chunk;

          // Process complete lines in the buffer
          const lines = buffer.split('\n');

          // Keep the last (potentially incomplete) line in the buffer
          buffer = lines.pop() || '';

          // Process each complete line
          for (const line of lines) {
            if (line.trim()) {
              try {
                // Parse the JSON object
                const item = JSON.parse(line);

                if (
                  item &&
                  typeof item === 'object' &&
                  'group' in item &&
                  'count' in item
                ) {
                  const group = item.group as PatientGroup;
                  const count = Number(item.count);

                  if (PatientGroupsList.includes(group)) {
                    // Update the counts for this group
                    setCounts((prevCounts) => {
                      const newCounts = { ...prevCounts };
                      newCounts[group] = count;
                      return newCounts;
                    });

                    // Mark this group as processed
                    setProcessedGroups((prev) => {
                      const newSet = new Set(prev);
                      newSet.add(group);
                      return newSet;
                    });

                    console.log(`Updated count for ${group}: ${count}`);
                  }
                }
              } catch (parseError) {
                console.error('Error parsing line:', line, parseError);
              }
            }
          }
        }

        // Process any remaining data in the buffer
        if (buffer.trim()) {
          try {
            const item = JSON.parse(buffer);

            if (
              item &&
              typeof item === 'object' &&
              'group' in item &&
              'count' in item
            ) {
              const group = item.group as PatientGroup;
              const count = Number(item.count);

              if (PatientGroupsList.includes(group)) {
                setCounts((prevCounts) => {
                  const newCounts = { ...prevCounts };
                  newCounts[group] = count;
                  return newCounts;
                });

                setProcessedGroups((prev) => {
                  const newSet = new Set(prev);
                  newSet.add(group);
                  return newSet;
                });

                console.log(
                  `Updated count for ${group}: ${count} (from buffer)`,
                );
              }
            }
          } catch (parseError) {
            console.error(
              'Error parsing remaining buffer:',
              buffer,
              parseError,
            );
          }
        }

        // We should always receive data now that we've fixed the backend caching

        setIsComplete(true);
      } catch (err) {
        if (axios.isCancel(err)) {
          console.log('Request was cancelled');
        } else {
          console.error('Error fetching data:', err);
          setError(err instanceof Error ? err : new Error('Unknown error'));
        }
        setIsLoading(false);
      }
    };

    void fetchData();

    // Refresh every 5 minutes
    const intervalId = setInterval(
      () => {
        void fetchData();
      },
      5 * 60 * 1000,
    );

    return () => {
      controller.abort();
      clearInterval(intervalId);
    };
  }, [accessToken]);

  return { data: counts, isLoading, error, isComplete, processedGroups };
};

export const useGetPatients = (
  group: PatientGroup,
  params: {
    sortBy?: string;
    direction: string;
    page: number;
    limit: number;
    search?: string;
    doctorId?: string;
    stateCode?: string;
    pharmacyId?: string;
  },
) => {
  return useQuery({
    queryKey: ['patients', group, params],
    staleTime: 1000 * 60 * 5,
    refetchOnWindowFocus: false,
    queryFn: () => {
      return apiClient
        .get('/admin/patients', {
          params: {
            group: group,
            ...params,
          },
        })
        .then((res) => res.data as PaginatedPatients);
    },
  });
};

export const useGetPatientInfo = (patientId: string) => {
  const token = useAccessToken();

  return useQuery({
    queryKey: ['patients', patientId],
    enabled: !!token,
    queryFn: () => {
      return apiClient
        .get(`/admin/patients/${patientId}`)
        .then((res) => res.data as AdminPatientById);
    },
  });
};
export const useGetPatientIntake = (patientId: string) => {
  const token = useAccessToken();

  return useQuery({
    queryKey: ['patients', patientId, 'intake'],
    enabled: !!token,
    queryFn: () => {
      return apiClient
        .get(`/admin/patient/${patientId}/intake`)
        .then((res) => res.data as PatientIntake);
    },
  });
};
export const useGetPatientActivityLog = (
  patientId: string,
  params?: {
    includes?: string;
    excludes?: string;
  },
) => {
  const token = useAccessToken();

  return useQuery({
    queryKey: ['patients', patientId, 'activity-log', params],
    enabled: !!token,
    queryFn: () => {
      return apiClient
        .get(`/admin/patients/${patientId}/activity-log`, {
          params,
        })
        .then((res) => res.data as AuditLogApi[]);
    },
  });
};

export const useCreatePatientNote = (
  patientId: string,
  options: {
    onSuccess?: () => Promise<void> | void;
  } = {},
) => {
  const invalidate = useInvalidatedQuery();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: { note: string }) => {
      return apiClient.post(`/admin/patients/${patientId}/create-note`, data);
    },
    onSuccess: () => {
      void invalidate(['patients', patientId, 'activity-log']);

      toast({
        variant: 'default',
        title: 'Patient note has been created successfully',
      });
      void options.onSuccess?.();
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Error creating Patient note',
        description: error?.message ?? 'An error occurred',
      });
    },
  });
};

export const useImpersonateUser = () => {
  const token = useAccessToken();
  const { mutateAsync: refreshToken } = useRefreshToken();

  return useMutation({
    mutationFn: async ({ userId }: { userId: string }) => {
      if (!token) {
        throw new Error('No access token found');
      }

      // Get fresh token for impersonation
      const refreshedTokens = await refreshToken({
        refreshToken: token.refreshToken,
      });

      const res = await apiClient.post<{ url: string }>(
        `/admin/impersonate`,
        {
          userId,
          impersonationToken: refreshedTokens.data.accessToken,
        },
        {
          headers: {
            Authorization: `Bearer ${token.accessToken}`,
          },
        },
      );

      if (!res.data) throw new Error('Could not impersonate user');
      return res.data;
    },
  });
};

export const useUpdatePatientInfo = (patientId: string) => {
  const invalidate = useInvalidatedQuery();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: {
      patientId: string;
      updatedData: Partial<AdminPatientById>;
    }) => {
      return apiClient.put(
        `/patient/${data.patientId}/admin-update-profile`,
        data.updatedData,
      );
    },
    onSuccess: () => {
      void invalidate(['patients', patientId]);
      toast({ title: 'Patient infos have been updated successfully' });
    },
    onError: (error: Error | AxiosError) => {
      let description = 'An error occurred';
      if (isAxiosError(error)) {
        if (error.response?.data) {
          description = error.response.data.message || description;
        }
      }

      toast({
        variant: 'destructive',
        title: 'Error updating Patient infos',
        description,
      });
    },
  });
};

export const useSetPatientPassword = (
  patientId: string,
  options: {
    onSuccess?: () => Promise<void> | void;
    onError?: () => Promise<void> | void;
    onSettled?: () => Promise<void> | void;
  } = {},
) => {
  const invalidate = useInvalidatedQuery();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: { email: string; password: string }) => {
      return apiClient.post('admin/patients/set-password', data);
    },
    onSuccess: () => {
      void invalidate(['patients', patientId]);
      toast({
        variant: 'default',
        title: 'Patient password has been updated successfully',
      });
      void options.onSuccess?.();
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Error setting Patient password',
        description: error?.message ?? 'An error occurred',
      });
    },
    onSettled: options.onSettled,
  });
};

export const useResetPatientPassword = (
  patientId: string,
  options: {
    onSuccess?: () => Promise<void> | void;
  } = {},
) => {
  const invalidate = useInvalidatedQuery();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: { email: string }) => {
      return apiClient.post('admin/patients/forgot-password', data);
    },
    onSuccess: () => {
      void invalidate(['patients', patientId]);
      toast({
        variant: 'default',
        title: 'email has been sent successfully',
      });
      void options.onSuccess?.();
    },
    onError: (error: Error | AxiosError) => {
      let description = 'An error occurred';
      if (isAxiosError(error)) {
        if (error.response?.data) {
          description = error.response.data.message || description;
        }
      }
      toast({
        variant: 'destructive',
        title: 'Error sending email',
        description,
      });
    },
  });
};

type AddressUpdate = Omit<
  Exclude<AdminPatientById['shippingAddress'], null>,
  'id' | 'state'
>;
export const useUpdatePatientAddresses = (patientId: string) => {
  const invalidate = useInvalidatedQuery();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: {
      shippingAddress: AddressUpdate;
      billingAddress?: AddressUpdate;
      isBillingIsSameAsShipping: boolean;
    }) => {
      return apiClient.post(
        `/admin/patients/${patientId}/update-addresses`,
        data,
      );
    },
    onSuccess: () => {
      void invalidate(['patients', patientId]);
      toast({
        variant: 'default',
        title: 'Patient addresses have been updated successfully',
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Error updating Patient addresses',
        description: error?.message ?? 'An error occurred',
      });
    },
  });
};

export const useCancelPatient = ({ patientId }: { patientId: string }) => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: CancelPatientSchema) => {
      return apiClient.post(`/admin/patient/${patientId}/cancel`, data);
    },
    onSuccess: () => {
      void queryClient.invalidateQueries({
        queryKey: ['patients', patientId],
      });
      toast({
        variant: 'default',
        title: 'Patient has been cancelled successfully',
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Error cancelling Patient',
        description: error?.message ?? 'An error occurred',
      });
    },
  });
};

export const usePermanentlyDeletePatient = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (patientId: string) => {
      return apiClient.delete(`/admin/patient/${patientId}`);
    },
    onSuccess: () => {
      void queryClient.invalidateQueries({
        queryKey: ['patients'],
      });
      toast({
        variant: 'default',
        title: 'Patient has been permanently deleted',
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Error deleting Patient',
        description: error?.message ?? 'An error occurred',
      });
    },
  });
};

export const useRestoreSubscriptionPatient = ({
  patientId,
}: {
  patientId: string;
}) => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  return useMutation({
    mutationFn: async () => {
      const response = await apiClient.post(
        `/admin/patient/${patientId}/restore-subscription`,
      );
      return response;
    },
    onSuccess: () => {
      void queryClient.invalidateQueries({
        queryKey: ['patients', patientId],
      });
      toast({
        variant: 'default',
        title: 'Patient has been successfully uncancelled',
      });
    },
    onError: (error: Error | AxiosError) => {
      let description = 'An error occurred';
      if (isAxiosError(error)) {
        if (error.response?.data) {
          description = error.response.data.message || description;
        }
      }
      toast({
        variant: 'destructive',
        title: 'Error Restoring Subscription',
        description,
      });
    },
  });
};

export const useReassignDoctor = (patientId: string) => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: {
      newDoctorId: string;
      reasonForReassignment: string;
    }) => {
      const res = await apiClient.post(
        `admin/patients/${patientId}/reassign-doctor`,
        data,
      );

      await queryClient.refetchQueries({
        queryKey: ['patients', patientId],
      });

      return res;
    },
    onSuccess: () => {
      void queryClient.invalidateQueries({
        queryKey: ['patients', patientId],
      });
      void queryClient.invalidateQueries({
        queryKey: ['patient', patientId, 'treatments'],
      });
      toast({
        variant: 'default',
        title: 'Patient has been reassigned successfully',
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Error reassigning Patient',
        description: error?.message ?? 'An error occurred',
      });
    },
  });
};

export const useGetEligibleDoctors = (patientId: string) => {
  return useQuery({
    queryKey: ['patients', patientId, 'eligible-doctors'],
    queryFn: async () => {
      return apiClient
        .get(`admin/patients/${patientId}/eligible-doctors`)
        .then((res) => res.data as EligibleDoctor[]);
    },
    enabled: Boolean(patientId),
  });
};

export const useUpdatePatientPhoto = (
  patientId: string,
  type: 'face-photo' | 'id-photo',
) => {
  const invalidateQuery = useInvalidatedQuery();
  return useMutation<unknown, unknown, Blob>({
    mutationFn: async (file: Blob) => {
      const preSignedUrl = await apiClient
        .get(`/admin/patient/${patientId}/get-upload-url/${type}`)
        .then((res) => res.data as string);

      await axios.put(preSignedUrl, file, {
        headers: {
          'Content-Type': file.type,
        },
      });

      return apiClient.post(`/admin/patient/${patientId}/photo`, {
        type,
      });
    },
    onSuccess: () => {
      void invalidateQuery(['patient', patientId, 'info']);
    },
  });
};

export const useGetEligibleStatesWithDoctors = (patientId: string) => {
  return useQuery({
    queryKey: ['patients', patientId, 'eligible-states-with-doctors'],
    queryFn: async () => {
      return apiClient
        .get(`admin/patients/${patientId}/states-with-doctors`)
        .then((res) => res.data as EligibleStateWithDoctors[]);
    },
    enabled: Boolean(patientId),
  });
};

export const useTransferPatientState = (patientId: string) => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: {
      patientId: string;
      newDoctorId?: string;
      shippingAddress: AddressUpdate & { state: string };
      billingAddress: (AddressUpdate & { state: string }) | undefined;
      isBillingIsSameAsShipping: boolean;
    }) => {
      const res = await apiClient.post(
        `admin/patients/${patientId}/update-full-addresses`,
        data,
      );

      await queryClient.refetchQueries({
        queryKey: ['patients', patientId],
      });

      return res;
    },
    onSuccess: () => {
      void queryClient.invalidateQueries({
        queryKey: ['patients', patientId],
      });
      void queryClient.invalidateQueries({
        queryKey: ['patient', patientId, 'treatments'],
      });
      toast({
        variant: 'default',
        title: 'Patient has been transferred successfully',
      });
    },
    onError: (error) => {
      console.log('error', error);
      let description = 'An error occurred';
      if (isAxiosError(error)) {
        if (error.response?.data) {
          description = error.response.data.message || description;
        }
      }
      toast({
        variant: 'destructive',
        title: 'Error transferring patient',
        description: description,
      });
    },
  });
};
