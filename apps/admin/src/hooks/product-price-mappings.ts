import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { apiClient } from '@willow/utils/api/client';

export interface ProductPriceMapping {
  id: string;
  externalId: string;
  name?: string;
  productPriceId: string;
  metadata?: Record<string, any>;
  productPrice?: {
    id: string;
    name: string;
    product?: {
      id: string;
      name: string;
    };
  };
  createdAt: string;
  updatedAt: string;
}

export interface CreateProductPriceMappingDto {
  externalId: string;
  name?: string;
  productPriceId: string;
  metadata?: Record<string, any>;
}

export interface UpdateProductPriceMappingDto {
  externalId?: string;
  name?: string;
  metadata?: Record<string, any>;
}

interface FetchParams {
  productPriceId?: string;
  productId?: string;
  search?: string;
}

interface ProductPriceMappingsResponse {
  mappings: ProductPriceMapping[];
  total: number;
}

// Get product price mappings with search
export const useGetProductPriceMappings = (params: FetchParams) => {
  return useQuery<ProductPriceMappingsResponse>({
    queryKey: ['product-price-mappings', params],
    queryFn: async () => {
      const { data } = await apiClient.get(`/product-price-mapping`, {
        params: {
          productPriceId: params.productPriceId,
          productId: params.productId,
          search: params.search,
        },
      });
      return data;
    },
    enabled: !!params.productPriceId || !!params.productId,
  });
};

// Get a single product price mapping by ID
export const useGetProductPriceMapping = (mappingId: string) => {
  return useQuery<ProductPriceMapping>({
    queryKey: ['product-price-mapping', mappingId],
    queryFn: async () => {
      const { data } = await apiClient.get(
        `/product-price-mapping/${mappingId}`,
      );
      return data;
    },
    enabled: !!mappingId,
  });
};

// Create a new product price mapping
export const useCreateProductPriceMapping = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (mappingData: CreateProductPriceMappingDto) => {
      const { data } = await apiClient.post(
        '/product-price-mapping',
        mappingData,
      );
      return data;
    },
    onSuccess: (_data, variables) => {
      // Invalidate the mappings list
      void queryClient.invalidateQueries({
        queryKey: ['product-price-mappings'],
      });
      // Invalidate the product price to update any related info
      void queryClient.invalidateQueries({
        queryKey: ['product-price', variables.productPriceId],
      });
    },
  });
};

// Update an existing product price mapping
export const useUpdateProductPriceMapping = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      mappingId,
      mappingData,
    }: {
      mappingId: string;
      mappingData: UpdateProductPriceMappingDto;
    }) => {
      const { data } = await apiClient.patch(
        `/product-price-mapping/${mappingId}`,
        mappingData,
      );
      return data;
    },
    onSuccess: (_data, variables) => {
      // Invalidate specific cache
      void queryClient.invalidateQueries({
        queryKey: ['product-price-mapping', variables.mappingId],
      });
      // Also invalidate the mappings list
      void queryClient.invalidateQueries({
        queryKey: ['product-price-mappings'],
      });
    },
  });
};

// Delete a product price mapping
export const useDeleteProductPriceMapping = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ mappingId }: { mappingId: string }) => {
      const { data } = await apiClient.delete(
        `/product-price-mapping/${mappingId}`,
      );
      return data;
    },
    onSuccess: (_data, variables) => {
      // Invalidate the specific mapping cache
      void queryClient.invalidateQueries({
        queryKey: ['product-price-mapping', variables.mappingId],
      });
      // Also invalidate the mappings list
      void queryClient.invalidateQueries({
        queryKey: ['product-price-mappings'],
      });
    },
  });
};

// Get pharmacies for dropdown
export const useGetPharmacies = () => {
  return useQuery({
    queryKey: ['pharmacies'],
    queryFn: async () => {
      const { data } = await apiClient.get('/pharmacy');
      return data.pharmacies;
    },
  });
};

// Get product price mappings for a specific pharmacy
export const useGetProductPriceMappingsByPharmacy = (pharmacyId: string) => {
  return useQuery<ProductPriceMappingsResponse>({
    queryKey: ['product-price-mappings-by-pharmacy', pharmacyId],
    queryFn: async () => {
      const { data } = await apiClient.get(
        `/product-price-mapping/by-pharmacy/${pharmacyId}`,
      );
      return data;
    },
    enabled: !!pharmacyId,
  });
};
