import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import type { AdminRole } from '@willow/auth';
import { apiClient } from '@willow/utils/api/client';

export interface ListedAdmin {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  type: string;
  createdAt: string;
  deletedAt: string | null;
  admin: {
    role: AdminRole;
  };
}

export interface CreateAdminDto {
  email: string;
  firstName: string;
  lastName: string;
  role?: AdminRole;
}

export interface UpdateAdminDto {
  firstName?: string;
  lastName?: string;
  email?: string;
  password?: string;
  role?: AdminRole;
}

interface FetchParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  direction?: 'asc' | 'desc';
  showInactive?: boolean;
}

interface AdminsResponse {
  data: ListedAdmin[];
  meta: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

// Get admins with pagination and search
export const useGetAdmins = (params: FetchParams) => {
  return useQuery<AdminsResponse>({
    queryKey: ['admins', params],
    queryFn: async () => {
      const { data } = await apiClient.get('/admin/admins', {
        params,
      });
      return data;
    },
  });
};

// Get a single admin by ID
export const useGetAdmin = (id: string) => {
  return useQuery<ListedAdmin>({
    queryKey: ['admin', id],
    queryFn: async () => {
      const { data } = await apiClient.get(`/admin/admins/${id}`);
      return data;
    },
    enabled: !!id,
  });
};

// Create a new admin
export const useCreateAdmin = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (adminData: CreateAdminDto) => {
      const { data } = await apiClient.post('/admin/admins', adminData);
      return data;
    },
    onSuccess: () => {
      // Invalidate the admins list to update the UI with the new admin
      void queryClient.invalidateQueries({ queryKey: ['admins'] });
    },
  });
};

// Update an existing admin
export const useUpdateAdmin = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      adminData,
    }: {
      id: string;
      adminData: UpdateAdminDto;
    }) => {
      const { data } = await apiClient.patch(`/admin/admins/${id}`, adminData);
      return data;
    },
    onSuccess: (_data, variables) => {
      // Invalidate the specific admin cache
      void queryClient.invalidateQueries({ queryKey: ['admin', variables.id] });
      // Also invalidate the admins list to update any changes there
      void queryClient.invalidateQueries({ queryKey: ['admins'] });
    },
  });
};

// Deactivate an admin
export const useDeactivateAdmin = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const { data } = await apiClient.delete(`/admin/admins/${id}/deactivate`);
      return data;
    },
    onSuccess: (_data, variables) => {
      // Invalidate the specific admin cache
      void queryClient.invalidateQueries({ queryKey: ['admin', variables] });
      // Also invalidate the admins list to update the UI
      void queryClient.invalidateQueries({ queryKey: ['admins'] });
    },
  });
};

// Permanently delete an admin
export const useDeleteAdmin = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const { data } = await apiClient.delete(`/admin/admins/${id}`);
      return data;
    },
    onSuccess: (_data, variables) => {
      // Invalidate the specific admin cache
      void queryClient.invalidateQueries({ queryKey: ['admin', variables] });
      // Also invalidate the admins list to update the UI
      void queryClient.invalidateQueries({ queryKey: ['admins'] });
    },
  });
};

// Reactivate an admin
export const useReactivateAdmin = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const { data } = await apiClient.patch(`/admin/admins/${id}/reactivate`);
      return data;
    },
    onSuccess: (_data, variables) => {
      // Invalidate the specific admin cache
      void queryClient.invalidateQueries({ queryKey: ['admin', variables] });
      // Also invalidate the admins list to update any changes there
      void queryClient.invalidateQueries({ queryKey: ['admins'] });
    },
  });
};
