import { useQuery } from '@tanstack/react-query';

import { apiClient } from '@willow/utils/api/client';

export interface TopicInsight {
  name: string;
  topicArn: string;
  backupQueue: {
    arn: string;
    url: string;
  } | null;
  subscriptions: {
    arn: string;
    name: string;
    protocol: string;
    endpoint: string;
    isConfirmed: boolean;
    dlrQueueUrl: string | null;
    failedMessages: {
      messageId: string;
    }[];
  }[];
  messages: {
    patientId: string;
    messageId: string;
    topicArn: string;
    topicName: string;
    event: string;
    date: string;
    body: string;
  }[];
  messagesCount: number;
}

interface GlobalInsights {
  topics: TopicInsight[];
  date: string;
}

export const useQueuesInsights = ({
  forceRefresh,
}: {
  forceRefresh: boolean;
}) => {
  return useQuery<GlobalInsights>({
    queryKey: ['insignts', 'global-queues'],
    queryFn: async () => {
      const response = await apiClient.get<GlobalInsights>(
        `/insights/global-queues?forceRefresh=${forceRefresh}`,
      );
      return response.data;
    },
  });
};

export interface TopicInsightV2 {
  name: string;
  topicArn: string;
  backupQueue: {
    arn: string;
    url: string;
  } | null;
  subscriptions: {
    arn: string;
    name: string;
    protocol: string;
    endpoint: string;
    isConfirmed: boolean;
    dlrQueueUrl: string | null;
    failedMessages: {
      messageId: string;
    }[];
  }[];
  messages: Record<string, QueueMessage>;
  messagesCount: number;
}

interface QueueMessage {
  messageId: string;
  body: string;
  timestamp: number;
  patientId: string;
  topicName: string;
  event: string;
}

interface GlobalInsightsV2 {
  topics: TopicInsightV2[];
  date: string;
}

export const useTopcisInsights = ({
  forceRefresh,
}: {
  forceRefresh: boolean;
}) => {
  return useQuery<GlobalInsightsV2>({
    queryKey: ['insignts', 'topics', 'global'],
    queryFn: async () => {
      const response = await apiClient.get<GlobalInsightsV2>(
        `/insights/topics-global?forceRefresh=${forceRefresh}`,
      );
      return response.data;
    },
  });
};
