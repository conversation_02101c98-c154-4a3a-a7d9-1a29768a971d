import { useMutation, useQueryClient } from '@tanstack/react-query';

import { apiClient } from '@willow/utils/api/client';

// Type for transfer pharmacies request
export interface TransferPharmaciesRequest {
  sourcePharmacyId: string;
  targetPharmacyId: string;
  stateIds: string[];
  forms?: string[];
  reason?: string;
}

// Hook to transfer patients between pharmacies
export const useTransferPharmacies = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (transferData: TransferPharmaciesRequest) => {
      const { data } = await apiClient.post(
        '/pharmacy/transfer-pharmacies',
        transferData,
      );
      return data;
    },
    onSuccess: () => {
      // Invalidate pharmacies data to reflect updated status
      void queryClient.invalidateQueries({ queryKey: ['pharmacies'] });

      // Also invalidate patients data since their pharmacy assignments changed
      void queryClient.invalidateQueries({ queryKey: ['patients'] });
    },
  });
};
