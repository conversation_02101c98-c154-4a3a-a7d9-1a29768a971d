import {
  useMutation,
  useQueries,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';

import { apiClient } from '@willow/utils/api/client';

import { useGetPharmacies } from './pharmacies';

export interface ProductPrice {
  id: string;
  productId: string;
  name: string;
  unit_amount: number;
  active: boolean;
  canBeDeleted?: boolean;
  isDefaultPrice?: boolean;

  // New fields extracted from metadata
  description?: string;
  dosageDescription?: string;
  dosageLabel?: string;
  dosageTimeframe?: string;
  dosageAdditionalMessage?: string;
  compoundName?: string;
  patientDirections?: string;
  label?: string;
  milligrams?: number;
  phase?: number;
  additiveBenefit?: string;

  // Added product relation that comes from API
  product?: {
    id: string;
    name: string;
    form?: string;
    type?: string;
    label?: string;
    defaultPriceId: string;
    pharmacy?: {
      id: string;
      name: string;
    };
  };

  metadata?: Record<string, unknown>;
  created: string;
  updatedAt: string;
}

export interface CreateProductPriceDto {
  id?: string; // Generated by backend if not provided
  productId: string;
  name: string;
  unit_amount: number;
  active?: boolean;
  isDefaultPrice?: boolean;

  // New fields extracted from metadata
  description?: string;
  dosageDescription?: string;
  dosageAdditionalMessage?: string;
  compoundName?: string;
  patientDirections?: string;
  dosageLabel?: string;
  dosageTimeframe?: string;
  label?: string;
  milligrams?: number;
  phase?: number;
  additiveBenefit?: string;

  // Keep metadata for backward compatibility
  metadata?: Record<string, unknown>;
}

export interface UpdateProductPriceDto {
  name?: string;
  unit_amount?: number;
  active?: boolean;
  isDefaultPrice?: boolean;

  // New fields extracted from metadata
  description?: string;
  dosageDescription?: string;
  dosageLabel?: string;
  dosageTimeframe?: string;
  dosageAdditionalMessage?: string;
  compoundName?: string;
  patientDirections?: string;
  label?: string;
  milligrams?: number;
  phase?: number;
  additiveBenefit?: string;

  // Keep metadata for backward compatibility
  metadata?: Record<string, unknown>;
}

interface FetchParams {
  productId: string;
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  direction?: 'asc' | 'desc';
  showInactive?: boolean;
}

interface ProductPricesResponse {
  productPrices: ProductPrice[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export interface PharmacyWithProductPrices {
  pharmacy: {
    id: string;
    name: string;
    enabled?: boolean;
  };
  productPrices: ProductPrice[];
}

// Get product prices with pagination and search
export const useGetProductPrices = (params: FetchParams) => {
  return useQuery<ProductPricesResponse>({
    queryKey: ['product-prices', params],
    queryFn: async () => {
      const { data } = await apiClient.get(`/product-price`, {
        params: {
          productId: params.productId,
          page: params.page,
          limit: params.limit,
          sortBy: params.sortBy,
          direction: params.direction,
        },
      });
      return data;
    },
    enabled: !!params.productId,
  });
};

// Get product prices for all pharmacies (with search filter)
export const useGetAllPharmaciesWithProductPrices = (search?: string) => {
  const { data: pharmaciesData } = useGetPharmacies({
    limit: 100,
    showInactive: true,
  });
  const pharmacies = pharmaciesData?.pharmacies || [];

  const pharmacyPricesQueries = useQueries({
    queries: pharmacies.map((pharmacy) => ({
      queryKey: ['product-prices-by-pharmacy', pharmacy.id, search],
      queryFn: async () => {
        const { data } = await apiClient.get(`/product-price`, {
          params: {
            pharmacyId: pharmacy.id,
            limit: 100,
          },
        });
        return {
          pharmacy,
          productPrices: data.productPrices || [],
        } as PharmacyWithProductPrices;
      },
      enabled: !!pharmacy.id,
    })),
  });

  const isLoading = pharmacyPricesQueries.some((query) => query.isLoading);
  const isError = pharmacyPricesQueries.some((query) => query.isError);

  // Get the data but apply the filter client-side
  const pharmaciesWithPrices = pharmacyPricesQueries
    .filter((query) => query.data)
    .map((query) => {
      if (!query.data) return null;

      // Apply search filter client-side if provided
      const filteredPrices =
        search && search.trim() !== ''
          ? query.data.productPrices.filter((price: ProductPrice) => {
              // Create a searchable string that includes all relevant fields
              const searchableString = `${price.name} ${price.product?.name || ''} ${price.product?.form || ''} $${(price.unit_amount / 100).toFixed(2)}`;

              // Special handling for phase search
              const phaseString =
                price.phase !== undefined ? `Phase ${price.phase}` : '';

              // Tokenize the search input, but keep "phase X" as a single token
              const searchInput = search.toLowerCase();

              // Check if the search contains "phase" followed by a number
              const phaseMatch = searchInput.match(/phase\s+\d+/g);

              if (phaseMatch) {
                // If we have a phase match, we need to handle it specially
                // Remove the phase match from the search input to process other tokens
                let remainingSearch = searchInput;
                let allTokensMatch = true;

                // Check each phase token
                for (const match of phaseMatch) {
                  // Check if the phase token matches
                  if (!phaseString.toLowerCase().includes(match)) {
                    allTokensMatch = false;
                    break;
                  }
                  // Remove this match from the remaining search
                  remainingSearch = remainingSearch.replace(match, '');
                }

                // Process remaining tokens
                const remainingTokens = remainingSearch
                  .trim()
                  .split(/\s+/)
                  .filter((token) => token.length > 0);

                for (const token of remainingTokens) {
                  if (!searchableString.toLowerCase().includes(token)) {
                    allTokensMatch = false;
                    break;
                  }
                }

                return allTokensMatch;
              } else {
                // No phase in search, just do regular token matching
                const tokens = searchInput
                  .split(/\s+/)
                  .filter((token) => token.length > 0);

                // All tokens must match somewhere in the searchable string
                return tokens.every(
                  (token) =>
                    searchableString.toLowerCase().includes(token) ||
                    phaseString.toLowerCase().includes(token),
                );
              }
            })
          : query.data.productPrices;

      return {
        pharmacy: query.data.pharmacy,
        productPrices: filteredPrices,
      } as PharmacyWithProductPrices;
    });

  return {
    data: pharmaciesWithPrices,
    isLoading,
    isError,
  };
};

// Get a single product price by ID
export const useGetProductPrice = (productId: string, priceId: string) => {
  return useQuery<ProductPrice>({
    queryKey: ['product-price', priceId],
    queryFn: async () => {
      const { data } = await apiClient.get(`/product-price/${priceId}`);
      return data;
    },
    enabled: !!productId && !!priceId,
  });
};

// Create a new product price
export const useCreateProductPrice = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (priceData: CreateProductPriceDto) => {
      const { data } = await apiClient.post('/product-price', priceData);
      return data;
    },
    onSuccess: (_data, variables) => {
      // Invalidate the product prices list to update the UI with the new price
      void queryClient.invalidateQueries({ queryKey: ['product-prices'] });
      // Also invalidate the product to update any related info
      void queryClient.invalidateQueries({
        queryKey: ['product', variables.productId],
      });
    },
  });
};

interface UpdateProductPriceParams {
  productId: string;
  priceId: string;
  priceData: UpdateProductPriceDto;
}

// Update an existing product price
export const useUpdateProductPrice = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ priceId, priceData }: UpdateProductPriceParams) => {
      const { data } = await apiClient.patch(
        `/product-price/${priceId}`,
        priceData,
      );
      return data;
    },
    onSuccess: (_data, variables) => {
      // Invalidate specific cache
      void queryClient.invalidateQueries({
        queryKey: ['product-price', variables.priceId],
      });
      // Also invalidate the product prices list to update any changes there
      void queryClient.invalidateQueries({ queryKey: ['product-prices'] });
      // And the product itself
      void queryClient.invalidateQueries({
        queryKey: ['product', variables.productId],
      });
    },
  });
};

interface ProductPriceActionParams {
  productId: string;
  priceId: string;
}

// Deactivate a product price
export const useDeactivateProductPrice = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ priceId }: ProductPriceActionParams) => {
      const { data } = await apiClient.patch(
        `/product-price/${priceId}/deactivate`,
      );
      return data;
    },
    onSuccess: (_data, variables) => {
      // Invalidate the specific product price cache
      void queryClient.invalidateQueries({
        queryKey: ['product-price', variables.priceId],
      });
      // Also invalidate the product prices list to update the UI
      void queryClient.invalidateQueries({ queryKey: ['product-prices'] });
      // And the product itself
      void queryClient.invalidateQueries({
        queryKey: ['product', variables.productId],
      });
    },
  });
};

// Reactivate a product price
export const useReactivateProductPrice = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ priceId }: ProductPriceActionParams) => {
      const { data } = await apiClient.patch(
        `/product-price/${priceId}/activate`,
      );
      return data;
    },
    onSuccess: (_data, variables) => {
      // Invalidate the specific product price cache
      void queryClient.invalidateQueries({
        queryKey: ['product-price', variables.priceId],
      });
      // Also invalidate the product prices list to update any changes there
      void queryClient.invalidateQueries({ queryKey: ['product-prices'] });
      // And the product itself
      void queryClient.invalidateQueries({
        queryKey: ['product', variables.productId],
      });
    },
  });
};

// Delete a product price
export const useDeleteProductPrice = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ priceId }: ProductPriceActionParams) => {
      const { data } = await apiClient.delete(`/product-price/${priceId}`);
      return data;
    },
    onSuccess: (_data, variables) => {
      // Invalidate the specific product price cache
      void queryClient.invalidateQueries({
        queryKey: ['product-price', variables.priceId],
      });
      // Also invalidate the product prices list to update any changes there
      void queryClient.invalidateQueries({ queryKey: ['product-prices'] });
      // And the product itself
      void queryClient.invalidateQueries({
        queryKey: ['product', variables.productId],
      });
    },
  });
};

interface ReplaceProductPriceParams {
  productId: string;
  priceId: string;
  unit_amount: number;
}

// Replace a product price (creates new price and archives old one)
export const useReplaceProductPrice = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ priceId, unit_amount }: ReplaceProductPriceParams) => {
      const { data } = await apiClient.post(
        `/product-price/${priceId}/replace`,
        { unit_amount },
      );
      return data;
    },
    onSuccess: (_data, variables) => {
      // Invalidate the specific product price cache
      void queryClient.invalidateQueries({
        queryKey: ['product-price', variables.priceId],
      });
      // Also invalidate the product prices list to update any changes there
      void queryClient.invalidateQueries({ queryKey: ['product-prices'] });
      // And the product itself
      void queryClient.invalidateQueries({
        queryKey: ['product', variables.productId],
      });
      // Invalidate all pharmacy-based price queries too
      void queryClient.invalidateQueries({
        queryKey: ['product-prices-by-pharmacy'],
      });
    },
  });
};
