import { useQuery } from '@tanstack/react-query';

import type { AdminProfile } from '@willow/db';
import { apiClient } from '@willow/utils/api/client';

import { useAccessToken } from './useHasAccessToken';

export const useProfileQuery = () => {
  const token = useAccessToken();

  return useQuery<AdminProfile>({
    queryKey: ['user', 'profile'],
    queryFn: async () => {
      const response = await apiClient.get('/admin/profile', {
        headers: {
          Authorization: `Bearer ${token?.accessToken}`,
        },
      });
      return response.data as AdminProfile;
    },
    staleTime: 60 * 60 * 1000, // 1 hour
    gcTime: 30 * 60 * 1000, // 30 minutes
    refetchOnMount: false,
    enabled: !!token?.accessToken,
  });
};

export const useProfile = () => {
  const { data } = useProfileQuery();
  if (!data) throw new Error('user not connected');
  return data;
};
