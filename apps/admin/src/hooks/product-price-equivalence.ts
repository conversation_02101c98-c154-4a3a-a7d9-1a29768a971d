import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { apiClient } from '@willow/utils/api/client';

export interface ProductPriceEquivalence {
  id: string;
  name: string;
  productPrices: {
    id: string;
    name: string;
    unit_amount: number;
    product?: {
      id: string;
      name: string;
      label: string;
      form: string;
      pharmacy?: {
        id: string;
        name: string;
      };
    };
  }[];
  createdAt: string;
  updatedAt: string;
}

export interface CreateProductPriceEquivalenceDto {
  name: string;
  productPriceIds: string[];
}

export interface UpdateProductPriceEquivalenceDto {
  name?: string;
  productPriceIds?: string[];
  _comment?: string; // Optional comment field to help backend understand intent
}

interface FetchParams {
  pharmacyId: string;
  search?: string;
}

interface ProductPriceEquivalencesResponse {
  equivalenceGroups: ProductPriceEquivalence[];
  total: number;
}

// Get product price equivalences with search
export const useGetProductPriceEquivalences = (params: FetchParams) => {
  return useQuery<ProductPriceEquivalencesResponse>({
    queryKey: ['product-price-equivalences', params],
    queryFn: async () => {
      const { data } = await apiClient.get(`/product-price-equivalence`, {
        params: {
          pharmacyId: params.pharmacyId,
          search: params.search,
        },
      });
      return data;
    },
    enabled: !!params.pharmacyId,
  });
};

// Get a single product price equivalence by ID
export const useGetProductPriceEquivalence = (equivalenceId: string) => {
  const queryResult = useQuery<ProductPriceEquivalence>({
    queryKey: ['product-price-equivalence', equivalenceId],
    queryFn: async () => {
      console.log('Fetching product price equivalence for ID:', equivalenceId);
      const { data } = await apiClient.get(
        `/product-price-equivalence/${equivalenceId}`,
      );
      console.log('Product price equivalence data received:', data);
      return data;
    },
    enabled: !!equivalenceId,
  });

  console.log('useGetProductPriceEquivalence result:', {
    data: queryResult.data,
    isLoading: queryResult.isLoading,
    isError: queryResult.isError,
    error: queryResult.error,
  });

  return queryResult;
};

// Get product price equivalence by product price ID
export const useGetProductPriceEquivalenceByProductPriceId = (
  productPriceId: string,
) => {
  return useQuery<ProductPriceEquivalence>({
    queryKey: ['product-price-equivalence-by-product-price', productPriceId],
    queryFn: async () => {
      const { data } = await apiClient.get(
        `/product-price-equivalence/by-product-price/${productPriceId}`,
      );
      return data;
    },
    enabled: !!productPriceId,
  });
};

// Create a new product price equivalence
export const useCreateProductPriceEquivalence = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (equivalenceData: CreateProductPriceEquivalenceDto) => {
      const { data } = await apiClient.post(
        '/product-price-equivalence',
        equivalenceData,
      );
      return data;
    },
    onSuccess: (data) => {
      // Invalidate the equivalences list
      void queryClient.invalidateQueries({
        queryKey: ['product-price-equivalences'],
      });
      // Invalidate any product price equivalence by product price ID queries
      void queryClient.invalidateQueries({
        queryKey: ['product-price-equivalence-by-product-price'],
      });
      // Invalidate the specific equivalence cache if we have an ID
      if (data?.id) {
        void queryClient.invalidateQueries({
          queryKey: ['product-price-equivalence', data.id],
        });
      }
    },
  });
};

// Update an existing product price equivalence
export const useUpdateProductPriceEquivalence = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      equivalenceId,
      equivalenceData,
    }: {
      equivalenceId: string;
      equivalenceData: UpdateProductPriceEquivalenceDto;
    }) => {
      console.log('Sending update with data:', equivalenceData);

      if (
        equivalenceData.productPriceIds &&
        equivalenceData.productPriceIds.length > 0
      ) {
        try {
          // Try a DELETE+POST approach to force replacement
          // First get the existing data
          const { data: existingData } = await apiClient.get(
            `/product-price-equivalence/${equivalenceId}`,
          );
          console.log('Existing data before update:', existingData);

          // Then delete everything and recreate
          if (existingData) {
            try {
              // Method 1: Try to use PUT for full replacement
              const { data } = await apiClient.put(
                `/product-price-equivalence/${equivalenceId}`,
                {
                  ...equivalenceData,
                  _method: 'PUT', // Some frameworks use this to override method
                },
              );
              console.log('PUT response:', data);
              return data;
            } catch (putError) {
              console.error(
                'PUT failed, trying PATCH with clean productPriceIds:',
                putError,
              );

              // Method 2: Since PUT failed, try using PATCH but with special handling
              // First, clean up the DTO to avoid validation errors
              const cleanData = { ...equivalenceData };
              if (cleanData._comment) delete cleanData._comment;

              // Send the patch request
              const { data } = await apiClient.patch(
                `/product-price-equivalence/${equivalenceId}`,
                cleanData,
              );
              return data;
            }
          }
        } catch (error) {
          console.error(
            'Update strategy failed, falling back to basic PATCH:',
            error,
          );
          // Last resort: basic PATCH
          const cleanData = { ...equivalenceData };
          if (cleanData._comment) delete cleanData._comment;

          const { data } = await apiClient.patch(
            `/product-price-equivalence/${equivalenceId}`,
            cleanData,
          );
          return data;
        }
      } else {
        // If no product price IDs, just do a regular PATCH for the name
        const cleanData = { ...equivalenceData };
        if (cleanData._comment) delete cleanData._comment;

        const { data } = await apiClient.patch(
          `/product-price-equivalence/${equivalenceId}`,
          cleanData,
        );
        return data;
      }
    },
    onSuccess: (_data, variables) => {
      // Invalidate specific cache
      void queryClient.invalidateQueries({
        queryKey: ['product-price-equivalence', variables.equivalenceId],
      });
      // Also invalidate the equivalences list
      void queryClient.invalidateQueries({
        queryKey: ['product-price-equivalences'],
      });
      // Invalidate any product price equivalence by product price ID queries
      void queryClient.invalidateQueries({
        queryKey: ['product-price-equivalence-by-product-price'],
      });
    },
  });
};

// Delete a product price equivalence
export const useDeleteProductPriceEquivalence = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ equivalenceId }: { equivalenceId: string }) => {
      const { data } = await apiClient.delete(
        `/product-price-equivalence/${equivalenceId}`,
      );
      return data;
    },
    onSuccess: (_data, variables) => {
      // Invalidate the specific equivalence cache
      void queryClient.invalidateQueries({
        queryKey: ['product-price-equivalence', variables.equivalenceId],
      });
      // Also invalidate the equivalences list
      void queryClient.invalidateQueries({
        queryKey: ['product-price-equivalences'],
      });
      // Invalidate any product price equivalence by product price ID queries
      void queryClient.invalidateQueries({
        queryKey: ['product-price-equivalence-by-product-price'],
      });
    },
  });
};
