import type { UseQueryOptions } from '@tanstack/react-query';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import type { TreatmentItemAPI, TreatmentLogDetails } from '@willow/db';
import { useToast } from '@willow/ui/base/use-toast';
import { apiClient } from '@willow/utils/api/client';

import { useAccessToken } from './useHasAccessToken';

export const useGetPatientTreatments = (patientId: string) => {
  const token = useAccessToken();

  return useQuery({
    queryKey: ['patient', patientId, 'treatments'],
    enabled: !!token?.accessToken,
    queryFn: () =>
      apiClient
        .get(`/treatment/list/${patientId}`, {
          headers: {
            Authorization: `Bearer ${token?.accessToken}`,
          },
        })
        .then((res) => res.data as TreatmentItemAPI[]),
  });
};

export const useGetTreatment = (
  patientId: string,
  treatmentId: string,
  options?: Partial<UseQueryOptions<TreatmentItemAPI>>,
) => {
  const token = useAccessToken();
  return useQuery({
    queryKey: ['patient', patientId, 'treatments', treatmentId],
    enabled: !!token?.accessToken,
    ...options,
    queryFn: () =>
      apiClient
        .get(`/treatment/${treatmentId}`)
        .then((res) => res.data as TreatmentItemAPI),
  });
};

export const useGetPatientTreatmentsLogs = (treatmentId: string) => {
  const token = useAccessToken();

  return useQuery({
    queryKey: ['treatments', treatmentId, 'logs'],
    enabled: !!token?.accessToken,
    queryFn: () =>
      apiClient
        .get(`/treatment/${treatmentId}/logs`, {
          headers: {
            Authorization: `Bearer ${token?.accessToken}`,
          },
        })
        .then((res) => res.data as TreatmentLogDetails[]),
  });
};

export const useCancelTreatment = (
  patientId: string,
  options: {
    onSuccess?: (data: TreatmentItemAPI) => Promise<void> | void;
    onError?: (e: Error) => Promise<void> | void;
    onSettled?: () => Promise<void> | void;
  } = {},
) => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const token = useAccessToken();

  return useMutation({
    mutationFn: (treatmentId: string) =>
      apiClient
        .post(
          '/treatment/cancel',
          { treatmentId },
          {
            headers: {
              Authorization: `Bearer ${token?.accessToken}`,
            },
          },
        )
        .then((res) => res.data as TreatmentItemAPI),
    onSuccess: (data) => {
      void queryClient.invalidateQueries({
        queryKey: ['patient', patientId, 'treatments'],
      });
      void queryClient.invalidateQueries({
        queryKey: ['treatments', data.treatmentId, 'logs'],
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Error canceling treatment',
        description: error?.message ?? 'An error occurred',
      });
      void options.onError?.(error);
    },
    onSettled: options.onSettled,
  });
};

export const usePauseTreatment = (
  patientId: string,
  options: {
    onSuccess?: (data: TreatmentItemAPI) => Promise<void> | void;
    onError?: () => Promise<void> | void;
    onSettled?: () => Promise<void> | void;
  } = {},
) => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  return useMutation({
    mutationFn: ({
      treatmentId,
      date,
    }: {
      treatmentId: string;
      date?: string;
    }) =>
      apiClient
        .post('/treatment/pause', { treatmentId, date })
        .then((res) => res.data as TreatmentItemAPI),
    onSuccess: (data) => {
      void queryClient.invalidateQueries({
        queryKey: ['patient', patientId, 'treatments'],
      });
      void queryClient.invalidateQueries({
        queryKey: ['treatments', data.treatmentId, 'logs'],
      });
      void options.onSuccess?.(data);
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Error pausing treatment',
        description: error?.message ?? 'An error occurred',
      });
    },
    onSettled: options.onSettled,
  });
};

export const useResumeTreatment = (
  patientId: string,
  options: {
    onSuccess?: (data: TreatmentItemAPI) => Promise<void> | void;
    onError?: () => Promise<void> | void;
    onSettled?: () => Promise<void> | void;
  } = {},
) => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  return useMutation({
    mutationFn: ({
      treatmentId,
      date,
    }: {
      treatmentId: string;
      date?: string;
    }) =>
      apiClient
        .post('/treatment/resume', { treatmentId, date })
        .then((res) => res.data as TreatmentItemAPI),
    onSuccess: (data) => {
      void queryClient.invalidateQueries({
        queryKey: ['patient', patientId, 'treatments'],
      });
      void queryClient.invalidateQueries({
        queryKey: ['treatments', data.treatmentId, 'logs'],
      });
      void options.onSuccess?.(data);
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Error resuming treatment',
        description: error?.message ?? 'An error occurred',
      });
    },
    onSettled: options.onSettled,
  });
};

export const useMoveRefillDate = (patientId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      treatmentId,
      date,
    }: {
      treatmentId: string;
      date: string;
    }) =>
      apiClient
        .post('/treatment/move-refill-date', { treatmentId, date })
        .then((res) => res.data as TreatmentItemAPI),
    onSuccess: (data) => {
      void queryClient.invalidateQueries({
        queryKey: ['patient', patientId, 'treatments'],
      });
      void queryClient.invalidateQueries({
        queryKey: ['treatments', data.treatmentId, 'logs'],
      });
    },
  });
};

export const useFireNext = (
  patientId: string,
  options: {
    onSuccess?: (data: TreatmentItemAPI) => Promise<void> | void;
    onError?: () => Promise<void> | void;
    onSettled?: () => Promise<void> | void;
  } = {},
) => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ treatmentId }: { treatmentId: string }) =>
      apiClient
        .post('/treatment/fire-next', { treatmentId })
        .then((res) => res.data as TreatmentItemAPI),
    onSuccess: (data) => {
      void queryClient.invalidateQueries({
        queryKey: ['patient', patientId, 'treatments'],
      });
      void queryClient.invalidateQueries({
        queryKey: ['treatments', data.treatmentId, 'logs'],
      });
      void options.onSuccess?.(data);
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Error firing next order',
        description: error?.message ?? 'An error occurred',
      });
    },
    onSettled: options.onSettled,
  });
};

export const useRetryPayment = (
  patientId: string,
  options: {
    onSuccess?: (data: TreatmentItemAPI) => Promise<void> | void;
    onError?: () => Promise<void> | void;
    onSettled?: () => Promise<void> | void;
  } = {},
) => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  return useMutation({
    mutationFn: ({ treatmentId }: { treatmentId: string }) =>
      apiClient
        .post('/treatment/retry-payment', { treatmentId })
        .then((res) => res.data as TreatmentItemAPI),
    onSuccess: (data) => {
      void queryClient.invalidateQueries({
        queryKey: ['patient', patientId, 'treatments'],
      });
      void queryClient.invalidateQueries({
        queryKey: ['treatments', data.treatmentId, 'logs'],
      });
      void options.onSuccess?.(data);
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Error retry treatment',
        description: error?.message ?? 'An error occurred',
      });
    },
    onSettled: options.onSettled,
  });
};
