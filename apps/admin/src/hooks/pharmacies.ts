import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { apiClient } from '@willow/utils/api/client';

export interface State {
  id: string;
  name: string;
  code: string;
  enabled: boolean;
}

export interface Product {
  id: string;
  name: string;
  description: string | null;
  active: boolean;
}

export interface ListedPharmacy {
  id: string;
  name: string;
  slug?: string;
  doseSpotPharmacyId: string;
  enabled: boolean;
  enableApi: boolean | null;
  regularPriority?: number;
  usingGLP1Priority?: number;
  color?: string;
  metadata?: any;
  PharmacyOnState: {
    state: {
      id: string;
      name: string;
      code: string;
      enabled: boolean;
    };
  }[];
  Product: {
    id: string;
    name: string;
    description?: string;
    image?: string;
    isCore?: boolean;
    active?: boolean;
  }[];
  createdAt: string;
  patientCount?: number;
}

export interface CreatePharmacyDto {
  name: string;
  doseSpotPharmacyId: string;
  slug?: string;
  color?: string;
  enabled?: boolean;
  enableApi?: boolean | null;
  regularPriority?: number;
  usingGLP1Priority?: number;
  metadata?: any;
  stateIds: string[];
}

export interface UpdatePharmacyDto {
  name?: string;
  doseSpotPharmacyId?: string;
  slug?: string;
  color?: string;
  enabled?: boolean;
  enableApi?: boolean | null;
  regularPriority?: number;
  usingGLP1Priority?: number;
  metadata?: any;
  stateIds?: string[];
}

interface FetchParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  direction?: 'asc' | 'desc';
  showInactive?: boolean;
  showPatients?: boolean;
}

interface PharmaciesResponse {
  pharmacies: ListedPharmacy[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

// Get pharmacies with pagination and search
export const useGetPharmacies = (params: FetchParams) => {
  return useQuery<PharmaciesResponse>({
    queryKey: ['pharmacies', params],
    queryFn: async () => {
      const { data } = await apiClient.get('/pharmacy', {
        params: {
          ...params,
          showPatients: true,
        },
      });
      return data;
    },
  });
};

// Get a single pharmacy by ID
export const useGetPharmacy = (id: string) => {
  return useQuery<ListedPharmacy>({
    queryKey: ['pharmacy', id],
    queryFn: async () => {
      const { data } = await apiClient.get(`/pharmacy/${id}`);
      return data;
    },
    enabled: !!id,
  });
};

// Create a new pharmacy
export const useCreatePharmacy = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (pharmacyData: CreatePharmacyDto) => {
      const { data } = await apiClient.post('/pharmacy', pharmacyData);
      return data;
    },
    onSuccess: () => {
      // Invalidate the pharmacies list to update the UI with the new pharmacy
      void queryClient.invalidateQueries({ queryKey: ['pharmacies'] });
    },
  });
};

// Update an existing pharmacy
export const useUpdatePharmacy = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      pharmacyData,
    }: {
      id: string;
      pharmacyData: UpdatePharmacyDto;
    }) => {
      const { data } = await apiClient.patch(`/pharmacy/${id}`, pharmacyData);
      return data;
    },
    onSuccess: (_data, variables) => {
      // Invalidate the specific pharmacy cache
      void queryClient.invalidateQueries({
        queryKey: ['pharmacy', variables.id],
      });
      // Also invalidate the pharmacies list to update any changes there
      void queryClient.invalidateQueries({ queryKey: ['pharmacies'] });
    },
  });
};

// Deactivate a pharmacy
export const useDeactivatePharmacy = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const { data } = await apiClient.delete(`/pharmacy/${id}/deactivate`);
      return data;
    },
    onSuccess: (_data, variables) => {
      // Invalidate the specific pharmacy cache
      void queryClient.invalidateQueries({ queryKey: ['pharmacy', variables] });
      // Invalidate the pharmacies list to update the UI
      void queryClient.invalidateQueries({ queryKey: ['pharmacies'] });
    },
  });
};

// Reactivate a pharmacy
export const useReactivatePharmacy = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const { data } = await apiClient.patch(`/pharmacy/${id}/reactivate`);
      return data;
    },
    onSuccess: (_data, variables) => {
      // Invalidate the specific pharmacy cache
      void queryClient.invalidateQueries({ queryKey: ['pharmacy', variables] });
      // Also invalidate the pharmacies list to update any changes there
      void queryClient.invalidateQueries({ queryKey: ['pharmacies'] });
    },
  });
};

// Delete a pharmacy
export const useDeletePharmacy = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const { data } = await apiClient.delete(`/pharmacy/${id}`);
      return data;
    },
    onSuccess: (_data, variables) => {
      // Remove the deleted pharmacy from the cache
      queryClient.removeQueries({ queryKey: ['pharmacy', variables] });
      // Invalidate the pharmacies list to update the UI
      void queryClient.invalidateQueries({ queryKey: ['pharmacies'] });
    },
  });
};
