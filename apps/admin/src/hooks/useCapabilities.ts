import type { AdminRole, Capability } from '@willow/auth';
import {
  hasAllCapabilities as roleHasAllCapabilities,
  hasAnyCapability as roleHasAnyCapability,
  hasCapability as roleHasCapability,
} from '@willow/auth';

import { useProfileQuery } from './useProfile';

/**
 * Hook to check if the current admin has specific capabilities
 *
 * Unlike the original useProfile hook, this doesn't throw an error if
 * the user is not logged in, making it safe to use in layouts.
 *
 * This is a simplified implementation that avoids useMemo to prevent
 * runtime errors in the dashboard layout.
 */
export const useCapabilities = () => {
  const { data: profile } = useProfileQuery();
  const adminRole = profile?.admin?.role as AdminRole | undefined;

  // Simple implementation without useMemo to avoid dependency array issues
  return {
    hasCapability: (capability: Capability): boolean => {
      if (!adminRole) return false;
      return roleHasCapability(adminRole, capability);
    },

    hasAnyCapability: (
      requiredCapabilities: Capability[] | Capability,
    ): boolean => {
      if (!adminRole) return false;
      const capabilities = Array.isArray(requiredCapabilities)
        ? requiredCapabilities
        : [requiredCapabilities];
      return roleHasAnyCapability(adminRole, capabilities);
    },

    hasAllCapabilities: (
      requiredCapabilities: Capability[] | Capability,
    ): boolean => {
      if (!adminRole) return false;
      const capabilities = Array.isArray(requiredCapabilities)
        ? requiredCapabilities
        : [requiredCapabilities];
      return roleHasAllCapabilities(adminRole, capabilities);
    },
  };
};
