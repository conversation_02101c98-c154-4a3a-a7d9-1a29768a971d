import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { apiClient } from '@willow/utils/api/client';

export interface ProductCategory {
  id: string;
  name: string;
  form: string | null;
  shortDescription: string | null;
  description: string | null;
  label: string | null;
  image: string | null;
  tags: string | null;
  customCard: string | null;
  enabled: boolean;
  order: number;
  createdAt: string;
  updatedAt: string;
  _count?: {
    Product: number;
  };
}

interface FetchParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  direction?: 'asc' | 'desc';
}

interface ProductCategoriesResponse {
  categories: ProductCategory[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

// Get product categories with pagination and search
export const useGetProductCategories = (params: FetchParams) => {
  return useQuery({
    queryKey: ['product-categories', params],
    queryFn: async () => {
      const response = await apiClient.get('/product-category', {
        params,
      });
      return response.data as ProductCategoriesResponse;
    },
  });
};

// Get a single product category by ID
export const useGetProductCategory = (id: string) => {
  return useQuery({
    queryKey: ['product-category', id],
    queryFn: async () => {
      const { data } = await apiClient.get(`/product-category/${id}`);
      return data as ProductCategory;
    },
    enabled: !!id,
  });
};

// Create a new product category
export const useCreateProductCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (categoryData: {
      name: string;
      form?: string | null;
      shortDescription?: string | null;
      description?: string | null;
      label?: string | null;
      customCard?: string | null;
      enabled?: boolean;
      order?: number;
    }) => {
      const { data } = await apiClient.post('/product-category', categoryData);
      return data;
    },
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: ['product-categories'] });
    },
  });
};

// Update an existing product category
export const useUpdateProductCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: {
        name?: string;
        form?: string | null;
        shortDescription?: string | null;
        description?: string | null;
        label?: string | null;
        customCard?: string | null;
        enabled?: boolean;
        order?: number;
      };
    }) => {
      const response = await apiClient.patch(`/product-category/${id}`, data);
      return response.data;
    },
    onSuccess: (_data, variables) => {
      void queryClient.invalidateQueries({
        queryKey: ['product-category', variables.id],
      });
      void queryClient.invalidateQueries({ queryKey: ['product-categories'] });
    },
  });
};

// Delete a product category
export const useDeleteProductCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const { data } = await apiClient.delete(`/product-category/${id}`);
      return data;
    },
    onSuccess: (_data, id) => {
      void queryClient.invalidateQueries({
        queryKey: ['product-category', id],
      });
      void queryClient.invalidateQueries({ queryKey: ['product-categories'] });
    },
  });
};

// Upload product category image
export const useUploadProductCategoryImage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      categoryId,
      file,
    }: {
      categoryId: string;
      file: File;
    }): Promise<{ url: string }> => {
      const formData = new FormData();
      formData.append('image', file);

      const { data } = await apiClient.post(
        `/product-category/${categoryId}/upload-image`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        },
      );

      return data;
    },
    onSuccess: (_data, variables) => {
      // Invalidate the specific category cache to force refetch with new image
      void queryClient.invalidateQueries({
        queryKey: ['product-category', variables.categoryId],
      });
      // Also invalidate the categories list to update any images shown there
      void queryClient.invalidateQueries({ queryKey: ['product-categories'] });
    },
  });
};
