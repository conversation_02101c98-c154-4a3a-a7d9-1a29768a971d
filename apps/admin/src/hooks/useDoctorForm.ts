import { useRef, useState } from 'react';

import type { ProfilePictureHandle } from '~/app/(dashboard)/doctors/_components/doctor-profile-picture';
import { useCreateDoctor, useUpdateDoctor } from './doctors';

export function useDoctorForm(doctorId?: string) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const profilePictureRef = useRef<ProfilePictureHandle>(null);

  const { mutateAsync: createDoctor } = useCreateDoctor();
  const { mutateAsync: updateDoctor } = useUpdateDoctor();

  const handleSubmit = async (formData: FormData) => {
    try {
      setIsSubmitting(true);
      setError(null);

      // First, upload the profile picture if there is one
      let temporaryImageKey = null;
      if (profilePictureRef.current) {
        temporaryImageKey = await profilePictureRef.current.handleFormSubmit();
      }

      // Prepare the doctor data from the form
      const doctorData: any = {};
      formData.forEach((value, key) => {
        doctorData[key] = value;
      });

      // Add the temporary image key if it exists
      if (temporaryImageKey) {
        doctorData.temporaryImageKey = temporaryImageKey;
      }

      // Create or update the doctor
      if (doctorId) {
        await updateDoctor({ id: doctorId, doctorData });
      } else {
        await createDoctor(doctorData);
      }

      return true;
    } catch (err) {
      const error =
        err instanceof Error ? err : new Error('Failed to submit form');
      setError(error);
      console.error('Error submitting form:', error);
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    isSubmitting,
    error,
    handleSubmit,
    profilePictureRef,
  };
}
