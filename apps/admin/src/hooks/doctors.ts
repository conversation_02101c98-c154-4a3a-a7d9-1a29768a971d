import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { apiClient } from '@willow/utils/api/client';

export interface State {
  id: string;
  name: string;
  code: string;
  enabled: boolean;
}

export interface ListedDoctor {
  id: string;
  userId: string;
  role: 'doctor' | 'superDoctor';
  user: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    deletedAt?: string | null;
  };
  state: {
    id: string;
    code: string;
    name: string;
    enabled: boolean;
  };
  address1: string;
  address2?: string;
  city: string;
  zip: string;
  doseSpotClinicianId?: string;
  doseSpotRegistrationStatus?: string;
  // Fields from DoseSpot
  dateOfBirth?: string;
  npiNumber?: string;
  primaryPhone?: string;
  primaryFax?: string;
  middleName?: string;
  image?: string; // Profile picture path
  prescribesIn: {
    doctorId: string;
    stateId: string;
    limit?: number;
    licenseNumber?: string;
    state: {
      id: string;
      name: string;
      code: string;
      enabled: boolean;
    };
  }[];
  active?: boolean; // Computed property for active status
  // Patient count information
  statePatientCounts?: {
    stateId: string;
    stateName: string;
    stateCode: string;
    patientCount: number;
    enabled: boolean;
  }[];
  totalPatientCount?: number;
  activeBulkTransfers?: {
    id: string;
    status: string;
    transferAt: string;
    revertAt?: string;
    reason?: string;
    rules?: {
      stateIds: string[];
      targetDoctorIds: string[];
    }[];
  }[];
}

export interface StateLicenseInfo {
  stateCode: string;
  licenseNumber?: string;
}

export interface CreateDoctorDto {
  email: string;
  password?: string; // Now optional as it will be generated if not provided
  firstName: string;
  lastName: string;
  dateOfBirth: string; // Expected format: YYYY-MM-DD
  states: string[];
  stateLicenses?: StateLicenseInfo[]; // Now used by the API to store license details
  npiNumber?: string;
  primaryPhone?: string;
  primaryFax?: string;
  doseSpotClinicianId?: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  zip: string;
  role: 'doctor' | 'superDoctor';
}

export interface UpdateDoctorDto {
  firstName?: string;
  lastName?: string;
  dateOfBirth?: string;
  email?: string; // Added email field for updates
  states?: string[];
  stateLicenses?: StateLicenseInfo[]; // Now used by the API to store license details
  npiNumber?: string;
  primaryPhone?: string;
  primaryFax?: string;
  middleName?: string;
  doseSpotClinicianId?: string;
  address1?: string;
  address2?: string;
  city?: string;
  state?: string;
  zip?: string;
  role?: 'doctor' | 'superDoctor';
  password?: string; // Optional password field for updates
}

interface FetchParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  direction?: 'asc' | 'desc';
  showInactive?: boolean;
}

interface DoctorsResponse {
  doctors: ListedDoctor[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

// Get doctors with pagination and search
export const useGetDoctors = (params: FetchParams) => {
  return useQuery<DoctorsResponse>({
    queryKey: ['doctors', params],
    queryFn: async () => {
      const { data } = await apiClient.get('/admin/doctors', {
        params,
      });

      // Process data to add active property based on deletedAt
      return {
        ...data,
        doctors: data.doctors.map((doctor: ListedDoctor) => ({
          ...doctor,
          active:
            doctor.user.deletedAt === null ||
            doctor.user.deletedAt === undefined,
        })),
      };
    },
  });
};

// Get a single doctor by ID
export const useGetDoctor = (id: string) => {
  return useQuery<ListedDoctor>({
    queryKey: ['doctor', id],
    queryFn: async () => {
      const { data } = await apiClient.get(`/admin/doctors/${id}`);

      // Add active property based on deletedAt
      return {
        ...data,
        active:
          data.user?.deletedAt === null || data.user?.deletedAt === undefined,
      };
    },
    enabled: !!id,
  });
};

// Create a new doctor
export const useCreateDoctor = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (doctorData: CreateDoctorDto) => {
      const { data } = await apiClient.post('/admin/doctors', doctorData);
      return data;
    },
    onSuccess: () => {
      // Invalidate the doctors list to update the UI with the new doctor
      void queryClient.invalidateQueries({ queryKey: ['doctors'] });
    },
  });
};

// Update an existing doctor
export const useUpdateDoctor = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      doctorData,
    }: {
      id: string;
      doctorData: UpdateDoctorDto;
    }) => {
      const { data } = await apiClient.patch(
        `/admin/doctors/${id}`,
        doctorData,
      );
      return data;
    },
    onSuccess: (_data, variables) => {
      // Invalidate the specific doctor cache
      void queryClient.invalidateQueries({
        queryKey: ['doctor', variables.id],
      });
      // Also invalidate the doctors list to update any changes there
      void queryClient.invalidateQueries({ queryKey: ['doctors'] });
    },
  });
};

// Deactivate a doctor (previously delete)
export const useDeactivateDoctor = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const { data } = await apiClient.delete(`/admin/doctors/${id}`);
      return data;
    },
    onSuccess: (_data, variables) => {
      // Remove the deleted doctor from the cache
      queryClient.removeQueries({ queryKey: ['doctor', variables] });
      // Invalidate the doctors list to update the UI
      void queryClient.invalidateQueries({ queryKey: ['doctors'] });
    },
  });
};

// Reactivate a doctor
export const useReactivateDoctor = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const { data } = await apiClient.patch(`/admin/doctors/${id}/reactivate`);
      return data;
    },
    onSuccess: (_data, variables) => {
      // Invalidate the specific doctor cache
      void queryClient.invalidateQueries({ queryKey: ['doctor', variables] });
      // Also invalidate the doctors list to update any changes there
      void queryClient.invalidateQueries({ queryKey: ['doctors'] });
    },
  });
};

// Type for transfer patient blocks
export interface TransferBlock {
  stateIds: string[];
  targetDoctorIds: string[];
}

// Type for transfer patients request
export interface TransferPatientsRequest {
  sourceDoctorId: string;
  reason: string;
  transferBlocks: TransferBlock[];
  transferAt?: Date;
  revertAt?: Date;
  bulkTransferId?: string; // Used for updating existing transfers
}

// Hook to transfer patients from one doctor to others
export const useTransferPatients = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (transferData: TransferPatientsRequest) => {
      const { data } = await apiClient.post(
        '/admin/doctors/transfer-patients',
        transferData,
      );
      return data;
    },
    onSuccess: (_data, variables) => {
      // Invalidate doctors data to reflect updated patient counts
      void queryClient.invalidateQueries({ queryKey: ['doctors'] });
      // Also invalidate the specific source doctor's cache
      if (variables.sourceDoctorId) {
        void queryClient.invalidateQueries({
          queryKey: ['doctor', variables.sourceDoctorId],
        });
      }
      // Invalidate target doctor caches too if we have access to them
      if (variables.transferBlocks) {
        const targetDoctorIds = new Set<string>();
        variables.transferBlocks.forEach((block) => {
          block.targetDoctorIds.forEach((doctorId) =>
            targetDoctorIds.add(doctorId),
          );
        });

        targetDoctorIds.forEach((doctorId) => {
          void queryClient.invalidateQueries({
            queryKey: ['doctor', doctorId],
          });
        });
      }
    },
  });
};

// Hook to delete a bulk transfer
export const useDeleteBulkTransfer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      bulkTransferId,
      doctorId,
    }: {
      bulkTransferId: string;
      doctorId: string;
    }) => {
      const { data } = await apiClient.delete(
        `/admin/doctors/bulk-transfer/${bulkTransferId}?doctorId=${doctorId}`,
      );
      return data;
    },
    onSuccess: (_data, variables) => {
      // Invalidate doctors data to reflect updated status
      void queryClient.invalidateQueries({ queryKey: ['doctors'] });
      // Also invalidate the specific doctor cache to update bulk transfer info
      void queryClient.invalidateQueries({
        queryKey: ['doctor', variables.doctorId],
      });
    },
  });
};

// Type for update bulk transfer request
export interface UpdateBulkTransferData {
  reason?: string;
  // Use transferBlocks to match the original DTO's field name
  transferBlocks: {
    stateIds: string[];
    targetDoctorIds: string[];
  }[];
  transferAt?: Date;
  revertAt?: Date;
}

// Hook to update a bulk transfer
export const useUpdateBulkTransfer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      bulkTransferId,
      doctorId,
      updateData,
    }: {
      bulkTransferId: string;
      doctorId: string;
      updateData: UpdateBulkTransferData;
    }) => {
      const { data } = await apiClient.patch(
        `/admin/doctors/bulk-transfer/${bulkTransferId}?doctorId=${doctorId}`,
        updateData,
      );
      return data;
    },
    onSuccess: (_data, variables) => {
      // Invalidate doctors data to reflect updated status
      void queryClient.invalidateQueries({ queryKey: ['doctors'] });
      // Also invalidate the specific doctor cache to update bulk transfer info
      void queryClient.invalidateQueries({
        queryKey: ['doctor', variables.doctorId],
      });
    },
  });
};

// Hook to get all active doctors for transfers
export const useGetAllActiveDoctors = () => {
  return useQuery<ListedDoctor[]>({
    queryKey: ['all-active-doctors'],
    queryFn: async () => {
      const { data } = await apiClient.get('/admin/doctors', {
        params: {
          showInactive: false,
          limit: 1000,
          page: 1,
        },
      });

      // Process data to return only active doctors
      return data.doctors
        .map((doctor: ListedDoctor) => ({
          ...doctor,
          active:
            doctor.user.deletedAt === null ||
            doctor.user.deletedAt === undefined,
        }))
        .filter((doctor: ListedDoctor) => doctor.active);
    },
  });
};
