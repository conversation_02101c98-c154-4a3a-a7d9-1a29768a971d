import { useCallback, useEffect } from 'react';
import { atom, useAtom } from 'jotai';

import { env } from '~/env';

// Cache settings
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours
const CACHE_KEY = 'willow_asset_cache';

// Create or retrieve a cache timestamp
const getCacheTimestamp = (): number => {
  if (typeof window === 'undefined') return Date.now();

  try {
    const cached = localStorage.getItem(CACHE_KEY);
    if (cached) {
      const { timestamp, expiry } = JSON.parse(cached) as {
        timestamp: number;
        expiry: number;
      };
      if (expiry > Date.now()) return timestamp;
    }
  } catch (_e) {
    // Ignore storage errors
  }

  // Create a new timestamp if none exists or is expired
  const timestamp = Date.now();
  try {
    localStorage.setItem(
      CACHE_KEY,
      JSON.stringify({
        timestamp,
        expiry: timestamp + CACHE_DURATION,
      }),
    );
  } catch (_e) {
    // Ignore storage errors
  }

  return timestamp;
};

// Shared atom for cache key
export const imageCacheKeyAtom = atom(getCacheTimestamp());

// Asset URL generation functions
export const generatePatientAssetLink = (asset: string) =>
  `${env.NEXT_PUBLIC_API_PATIENTS_PHOTOS_URL}/${asset}`;

export const generateDoctorAssetLink = (asset: string) =>
  asset ? `${env.NEXT_PUBLIC_API_S3_URL}/${asset}` : '';

// Generic asset link hook with customizable URL generator
const useAssetLink = (
  urlGenerator: (path: string) => string,
  acceptsNullOrUndefined = false,
) => {
  const [cacheKey, setCacheKey] = useAtom(imageCacheKeyAtom);

  // Initialize from localStorage if needed
  useEffect(() => {
    const timestamp = getCacheTimestamp();
    if (timestamp !== cacheKey) {
      setCacheKey(timestamp);
    }
  }, [cacheKey, setCacheKey]);

  const genAssetLink = useCallback(
    (path: string | null | undefined) => {
      if (path == null && acceptsNullOrUndefined) return '';
      return `${urlGenerator(path!)}?cacheKey=${cacheKey}`;
    },
    [cacheKey, urlGenerator, acceptsNullOrUndefined],
  );

  const invalidateAssetsCache = useCallback(() => {
    const timestamp = Date.now();
    setCacheKey(timestamp);

    try {
      localStorage.setItem(
        CACHE_KEY,
        JSON.stringify({
          timestamp,
          expiry: timestamp + CACHE_DURATION,
        }),
      );
    } catch (_e) {
      // Ignore storage errors
    }
  }, [setCacheKey]);

  return { genAssetLink, invalidateAssetsCache };
};

// Patient assets hook
export const usePatientAssetLink = () => useAssetLink(generatePatientAssetLink);

// Doctor assets hook
export const useDoctorAssetLink = () =>
  useAssetLink(generateDoctorAssetLink, true);
