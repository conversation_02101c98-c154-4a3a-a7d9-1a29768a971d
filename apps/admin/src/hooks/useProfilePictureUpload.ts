import { useCallback, useState } from 'react';
import axios from 'axios';

import { apiClient } from '@willow/utils/api/client';

interface UseProfilePictureUploadResult {
  isUploading: boolean;
  uploadImage: (file: File) => Promise<string | null>;
  error: Error | null;
}

export function useProfilePictureUpload(): UseProfilePictureUploadResult {
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const uploadImage = useCallback(
    async (file: File): Promise<string | null> => {
      console.log(
        'useProfilePictureUpload: uploadImage called with file:',
        file?.name,
      );
      if (!file) return null;

      try {
        setIsUploading(true);
        setError(null);
        console.log('useProfilePictureUpload: Getting pre-signed URL');

        // Get pre-signed URL
        const { data } = await apiClient.get('/admin/doctors/get-upload-url');
        const { key, preSignedURL } = data;
        console.log(
          'useProfilePictureUpload: Got pre-signed URL and key:',
          key,
        );

        // Upload image to S3
        console.log('useProfilePictureUpload: Uploading image to S3');
        try {
          await axios.put(preSignedURL, file, {
            headers: {
              'Content-Type': 'image/jpeg',
            },
          });
        } catch (uploadError) {
          console.error('Error in axios.put:', uploadError);
          throw uploadError;
        }
        console.log('useProfilePictureUpload: Upload complete');

        return key;
      } catch (err) {
        const error =
          err instanceof Error ? err : new Error('Failed to upload image');
        setError(error);
        console.error('Error uploading image:', error);
        return null;
      } finally {
        setIsUploading(false);
      }
    },
    [],
  );

  return { isUploading, uploadImage, error };
}
