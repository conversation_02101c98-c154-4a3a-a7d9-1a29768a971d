import { Capability } from '@willow/auth';

/**
 * Grouped capabilities for easier use in components
 * These groups combine related capabilities into a single constant
 */

// Admin management capability groups
export const AdminCapabilityGroups = {
  // Section-level capability (all admin actions)
  MANAGE_ALL: Capability.MANAGE_ADMINS,

  // View capabilities
  VIEW: Capability.VIEW_ADMINS,

  // Edit capabilities
  MANAGE: [
    Capability.CREATE_ADMINS,
    Capability.EDIT_ADMINS,
    Capability.DEACTIVATE_ADMINS,
    Capability.REACTIVATE_ADMINS,
    Capability.DELETE_ADMINS,
  ],

  // Individual capabilities for specific actions
  CREATE: Capability.CREATE_ADMINS,
  EDIT: Capability.EDIT_ADMINS,
  DEACTIVATE: Capability.DEACTIVATE_ADMINS,
  REACTIVATE: Capability.REACTIVATE_ADMINS,
  DELETE: Capability.DELETE_ADMINS,
};

// SuperAdmin management capability groups
export const SuperAdminCapabilityGroups = {
  // Section-level capability (all superadmin actions)
  MANAGE_ALL: Capability.<PERSON><PERSON><PERSON>_SUPER_ADMINS,
};

// Doctor management capability groups
export const DoctorCapabilityGroups = {
  // Section-level capability (all doctor actions)
  MANAGE_ALL: Capability.MANAGE_DOCTORS,

  // View capabilities
  VIEW: Capability.VIEW_DOCTORS,

  // Edit capabilities
  MANAGE: [
    Capability.CREATE_DOCTORS,
    Capability.EDIT_DOCTORS,
    Capability.DEACTIVATE_DOCTORS,
    Capability.REACTIVATE_DOCTORS,
  ],

  // Individual capabilities for specific actions
  CREATE: Capability.CREATE_DOCTORS,
  EDIT: Capability.EDIT_DOCTORS,
  DEACTIVATE: Capability.DEACTIVATE_DOCTORS,
  REACTIVATE: Capability.REACTIVATE_DOCTORS,
};

// Patient management capability groups
export const PatientCapabilityGroups = {
  // Section-level capability (all patient actions)
  MANAGE_ALL: Capability.MANAGE_PATIENTS,

  // View capabilities
  VIEW: Capability.VIEW_PATIENTS,

  // Edit capabilities
  MANAGE_INFO: [
    Capability.EDIT_PATIENT_INFO,
    Capability.EDIT_PATIENT_ADDRESSES,
    Capability.RESET_PATIENT_PASSWORD,
  ],

  // Transfer capabilities
  MANAGE_TRANSFERS: [
    Capability.TRANSFER_PATIENT_DOCTOR,
    Capability.TRANSFER_PATIENT_PHARMACY,
  ],

  // Individual capabilities for specific actions
  EDIT_INFO: Capability.EDIT_PATIENT_INFO,
  EDIT_ADDRESSES: Capability.EDIT_PATIENT_ADDRESSES,
  RESET_PASSWORD: Capability.RESET_PATIENT_PASSWORD,
  TRANSFER_DOCTOR: Capability.TRANSFER_PATIENT_DOCTOR,
  TRANSFER_PHARMACY: Capability.TRANSFER_PATIENT_PHARMACY,
  CREATE_NOTES: Capability.CREATE_PATIENT_NOTES,
  DELETE: Capability.DELETE_PATIENTS,
};

// Pharmacy management capability groups
export const PharmacyCapabilityGroups = {
  // Section-level capability (all pharmacy actions)
  MANAGE_ALL: Capability.MANAGE_PHARMACIES,

  // View capabilities
  VIEW: Capability.VIEW_PHARMACIES,

  // Edit capabilities
  MANAGE: [
    Capability.CREATE_PHARMACIES,
    Capability.EDIT_PHARMACIES,
    Capability.DEACTIVATE_PHARMACIES,
    Capability.REACTIVATE_PHARMACIES,
    Capability.DELETE_PHARMACIES,
    Capability.TRANSFER_PHARMACY_PATIENTS,
  ],

  // Individual capabilities for specific actions
  CREATE: Capability.CREATE_PHARMACIES,
  EDIT: Capability.EDIT_PHARMACIES,
  DEACTIVATE: Capability.DEACTIVATE_PHARMACIES,
  REACTIVATE: Capability.REACTIVATE_PHARMACIES,
  DELETE: Capability.DELETE_PHARMACIES,
  TRANSFER_PATIENTS: Capability.TRANSFER_PHARMACY_PATIENTS,
};

// Product management capability groups
export const ProductCapabilityGroups = {
  // Section-level capability (all product actions)
  MANAGE_ALL: Capability.MANAGE_PRODUCTS,

  // View capabilities
  VIEW: Capability.VIEW_PRODUCTS,

  // Edit capabilities
  MANAGE: [
    Capability.CREATE_PRODUCTS,
    Capability.EDIT_PRODUCTS,
    Capability.ACTIVATE_PRODUCTS,
    Capability.DEACTIVATE_PRODUCTS,
    Capability.DELETE_PRODUCTS,
  ],

  // Individual capabilities for specific actions
  CREATE: Capability.CREATE_PRODUCTS,
  EDIT: Capability.EDIT_PRODUCTS,
  ACTIVATE: Capability.ACTIVATE_PRODUCTS,
  DEACTIVATE: Capability.DEACTIVATE_PRODUCTS,
  DELETE: Capability.DELETE_PRODUCTS,
};

// Product price management capability groups
export const ProductPriceCapabilityGroups = {
  // Section-level capability (all product price actions)
  MANAGE_ALL: Capability.MANAGE_PRODUCT_PRICES,

  // View capabilities
  VIEW: Capability.VIEW_PRODUCT_PRICES,

  // Edit capabilities
  MANAGE: [
    Capability.CREATE_PRODUCT_PRICES,
    Capability.EDIT_PRODUCT_PRICES,
    Capability.DELETE_PRODUCT_PRICES,
  ],

  // Individual capabilities for specific actions
  CREATE: Capability.CREATE_PRODUCT_PRICES,
  EDIT: Capability.EDIT_PRODUCT_PRICES,
  DELETE: Capability.DELETE_PRODUCT_PRICES,
};

// Treatment management capability groups
export const TreatmentCapabilityGroups = {
  // Section-level capability (all treatment actions)
  MANAGE_ALL: Capability.MANAGE_TREATMENTS,

  // View capabilities
  VIEW: Capability.VIEW_TREATMENTS,

  // Edit capabilities
  MANAGE: [
    Capability.CREATE_TREATMENTS,
    Capability.CANCEL_TREATMENTS,
    Capability.FIRE_NEXT_TREATMENT,
    Capability.MOVE_REFILL_DATE,
  ],

  // Individual capabilities for specific actions
  CREATE: Capability.CREATE_TREATMENTS,
  CANCEL: Capability.CANCEL_TREATMENTS,
  FIRE_NEXT: Capability.FIRE_NEXT_TREATMENT,
  MOVE_REFILL_DATE: Capability.MOVE_REFILL_DATE,
};

// Billing management capability groups
export const BillingCapabilityGroups = {
  // Section-level capability (all billing actions)
  MANAGE_ALL: Capability.MANAGE_BILLING,

  // View capabilities
  VIEW: Capability.VIEW_BILLING,

  // Edit capabilities
  MANAGE: [Capability.PROCESS_REFUNDS, Capability.MANAGE_PAYMENT_METHODS],

  // Individual capabilities for specific actions
  PROCESS_REFUNDS: Capability.PROCESS_REFUNDS,
  MANAGE_PAYMENT_METHODS: Capability.MANAGE_PAYMENT_METHODS,
};
