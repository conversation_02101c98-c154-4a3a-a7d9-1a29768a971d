/* eslint-disable no-restricted-properties */
import { createEnv } from '@t3-oss/env-nextjs';
import { z } from 'zod';

// import { env as authEnv } from "@willow/auth/env";

export const env = createEnv({
  extends: [],
  shared: {
    NODE_ENV: z
      .enum(['development', 'production', 'test'])
      .default('development'),
  },
  /**
   * Specify your server-side environment variables schema here.
   * This way you can ensure the app isn't built with invalid env vars.
   */
  server: {},

  /**
   * Specify your client-side environment variables schema here.
   * For them to be exposed to the client, prefix them with `NEXT_PUBLIC_`.
   */
  client: {
    NEXT_PUBLIC_API_URL: z.string().url(),
    NEXT_PUBLIC_ENVIRONMENT: z
      .enum(['development', 'staging', 'production', 'test'])
      .default('development'),
    NEXT_PUBLIC_API_PATIENTS_PHOTOS_URL: z.string().url(),
    NEXT_PUBLIC_API_S3_URL: z.string().url(),
    NEXT_PUBLIC_INTERCOM_APP_ID: z.string(),
  },
  /**
   * Destructure all variables from `process.env` to make sure they aren't tree-shaken away.
   */
  experimental__runtimeEnv: {
    NODE_ENV: process.env.NODE_ENV,
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
    NEXT_PUBLIC_ENVIRONMENT: process.env.NEXT_PUBLIC_ENVIRONMENT,
    NEXT_PUBLIC_API_PATIENTS_PHOTOS_URL:
      process.env.NEXT_PUBLIC_API_PATIENTS_PHOTOS_URL,
    NEXT_PUBLIC_API_S3_URL: process.env.NEXT_PUBLIC_API_S3_URL,
    NEXT_PUBLIC_INTERCOM_APP_ID: process.env.NEXT_PUBLIC_INTERCOM_APP_ID,
  },
  skipValidation:
    !!process.env.CI || process.env.npm_lifecycle_event === 'lint',
});
