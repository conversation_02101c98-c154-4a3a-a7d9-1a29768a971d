import type { Metadata, Viewport } from 'next';
import localFont from 'next/font/local';
import { NuqsAdapter } from 'nuqs/adapters/next/app';

import { cn } from '@willow/ui';
import { Toaster } from '@willow/ui/base/toaster';

import TanstackProvider from '~/providers/TanstackProvider';

import '~/app/globals.css';

import { env } from '~/env';

export const metadata: Metadata = {
  metadataBase: new URL(
    env.NODE_ENV === 'production'
      ? 'https://admin.startwillow.com/'
      : 'http://localhost:3002',
  ),
  title: 'Willow Admin Dashboard',
  description: 'Internal admin dashboard for Willow',
  openGraph: {
    title: 'Willow Admin Dashboard',
    description: 'Internal admin dashboard for Willow',
    url: 'https://admin.startwillow.com/',
    siteName: 'Willow Admin Dashboard',
  },
};

export const viewport: Viewport = {
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: 'white' },
    { media: '(prefers-color-scheme: dark)', color: 'black' },
  ],
};

const neueFont = localFont({
  src: [
    {
      path: '../assets/fonts/NeueHaasDisplayBold.ttf',
      weight: '700',
      style: 'normal',
    },
    {
      path: '../assets/fonts/NeueHaasDisplayMediu.ttf',
      weight: '600',
      style: 'normal',
    },
    {
      path: '../assets/fonts/NeueHaasDisplayRoman.ttf',
      weight: '500',
      style: 'normal',
    },
    {
      path: '../assets/fonts/NeueHaasDisplayLight.ttf',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../assets/fonts/NeueHaasDisplayThin.ttf',
      weight: '300',
      style: 'normal',
    },
  ],
  variable: '--font-neue',
});

export default function RootLayout(props: { children: React.ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head></head>
      <body
        className={cn(
          'min-h-screen bg-background font-sans text-foreground antialiased',
        )}
      >
        <TanstackProvider>
          <NuqsAdapter>
            <main className={cn('min-h-screen', neueFont.className)}>
              {props.children}
            </main>
          </NuqsAdapter>
        </TanstackProvider>
        <Toaster />
      </body>
    </html>
  );
}
