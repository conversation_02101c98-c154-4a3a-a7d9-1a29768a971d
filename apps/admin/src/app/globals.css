@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --electric: 70 99% 73%; /* #e8fe76 */
    --electric-light: 70 73% 75%; /* #deee91 */
    --electric-muted: 32 22% 80%; /* #d7cdc1 */

    --denim: 216 44% 33%; /* #2f4d79 */
    --denim-light: 216 32% 41%; /* #47628a */
    --denim-muted: 216 14% 79%; /* #c2c8d1 */

    --orange: 14 99% 60%; /* # */
    --orange-light: 14 84% 65%; /* # */
    --orange-muted: 15 51% 87%; /* # */

    --error: 0 79% 63%; /* # */
    --error-light: 6 100% 83%; /* # */
    --success: 130 41% 58%;
    --success-light: 127 31% 89%;
    --warning: 43 70% 40%;
    --warning-light: 46 95% 77%;
    --info: 219 88% 67%;
    --info-light: 219 100% 90%;

    --ash: 0 5% 83%;
    --white: 0 0% 100%;
    --radius: 0.5rem;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    /* !!DO NOT USE THESE COLORS */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --ring: 240 5% 64.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

.scrollbar-hidden {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hidden::-webkit-scrollbar {
  display: none;
}

@font-face {
  font-family: 'PP Object Sans';
  font-style: normal;
  font-weight: 100 400 900;
  font-display: swap;
  src: url('../assets/fonts/ObjectSansRegular.otf') format('opentype');
}

.font-sans {
  font-family: 'PP Object Sans', sans-serif;
}

/* Fix for Vaul drawer margin issue */
.space-y-4 > [data-vaul-drawer],
.space-y-4 > [data-vaul-overlay] {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}
