'use client';

import type { ColumnDef } from '@tanstack/react-table';
import { useCallback, useMemo, useRef, useState } from 'react';
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { parseAsString, useQueryState } from 'nuqs';

import { AdminRole, Capability } from '@willow/auth';
import { cn } from '@willow/ui';
import { Avatar, AvatarFallback } from '@willow/ui/base/avatar';
import { Button } from '@willow/ui/base/button';
import { DrawerTitle } from '@willow/ui/base/drawer';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@willow/ui/base/table';
import {
  Drawer,
  DrawerContent,
  DrawerOverlay,
  DrawerPortal,
  DrawerTrigger,
} from '@willow/ui/base/vaul';
import { ColumnHeader } from '@willow/ui/column-header';
import { Loader } from '@willow/ui/loader';
import { Pagination } from '@willow/ui/pagination';
import { SearchInput } from '@willow/ui/search-input';
import {
  usePaginationSearchParams,
  useSortingQueryState,
} from '@willow/utils/table';

import type { ListedAdmin } from '~/hooks/admins';
import { WithCapability } from '~/components/capability';
import { useGetAdmins } from '~/hooks/admins';
import { AdminInfo } from './admin-info';

export function AdminsTable() {
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [query, setQuery] = useQueryState(
    'query',
    parseAsString.withDefault(''),
  );
  const [pagination, setPagination] = usePaginationSearchParams();
  const [sorting, setSorting] = useSortingQueryState();
  const [showInactive, setShowInactive] = useState(false);

  const [selectedAdminId, setSelectedAdminId] = useQueryState('adminId', {
    defaultValue: '',
  });

  const fetchParams = useMemo(
    () => ({
      sortBy: sorting.length > 0 ? sorting[0]?.id : undefined,
      direction:
        sorting.length > 0
          ? sorting[0]?.desc
            ? ('desc' as const)
            : ('asc' as const)
          : ('asc' as const),
      page: pagination.page,
      limit: pagination.pageSize,
      search: query ?? undefined,
      showInactive,
    }),
    [query, pagination, sorting, showInactive],
  );

  const { data, isPending, isError } = useGetAdmins(fetchParams);

  const columns: ColumnDef<ListedAdmin>[] = useMemo(
    () => [
      {
        accessorKey: 'firstName',
        id: 'name',
        header: () => <ColumnHeader label="Name" sortKey="firstName" />,
        cell: ({ row }) => (
          <div className="flex items-center gap-3">
            <Avatar className="h-10 w-10">
              <AvatarFallback className="font-bold uppercase text-denim-light">
                {row.original.firstName?.[0]}
                {row.original.lastName?.[0]}
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col gap-1">
              <div className="text-sm text-denim">
                {row.original.firstName} {row.original.lastName}
              </div>
              <div className="font-sans text-xs font-normal text-dark">
                {row.original.admin.role === AdminRole.superAdmin
                  ? 'Super Admin'
                  : row.original.admin.role === AdminRole.doctor
                    ? 'Doctor'
                    : 'Admin'}
              </div>
            </div>
          </div>
        ),
      },
      {
        accessorKey: 'status',
        header: () => <ColumnHeader label="Status" sortKey={''} />,
        cell: ({ row }) => {
          const isActive = !row.original.deletedAt;
          return (
            <div className="text-sm">
              {isActive ? (
                <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                  Active
                </span>
              ) : (
                <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                  Inactive
                </span>
              )}
            </div>
          );
        },
      },
      {
        accessorKey: 'email',
        header: () => <ColumnHeader label="Email" sortKey="email" />,
        cell: ({ row }) => (
          <div className="text-sm text-dark">{row.original.email}</div>
        ),
      },
      {
        accessorKey: 'createdAt',
        header: () => <ColumnHeader label="Created" sortKey="createdAt" />,
        cell: ({ row }) => (
          <div className="text-sm text-dark">
            {new Date(row.original.createdAt).toLocaleDateString()}
          </div>
        ),
      },
    ],
    [],
  );

  const table = useReactTable({
    data: data?.data || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: (sorting) => {
      void setSorting(sorting);
    },
    state: {
      sorting,
      pagination: {
        pageIndex: pagination.page - 1,
        pageSize: pagination.pageSize,
      },
    },
    manualPagination: true,
    pageCount: data?.meta?.pages || 1,
  });

  const handleSearchSubmit = useCallback(
    (query: string | null) => {
      void setQuery(query);
      void setPagination({ page: 1 });
    },
    [setPagination, setQuery],
  );

  return (
    <div className="grid h-[calc(100vh-96px)] w-full grid-rows-[auto_1fr_auto]">
      <div className="w-full">
        <div className="mb-5 text-2xl font-medium text-denim">
          Administrators
        </div>
        <SearchInput
          className="mb-5"
          ref={searchInputRef}
          onSearch={handleSearchSubmit}
          placeholder="Search administrators"
          defaultValue={query}
        />
      </div>

      <div className="relative flex-1 overflow-auto">
        <Drawer
          direction="right"
          open={selectedAdminId !== ''}
          onOpenChange={(value) => {
            if (!value) void setSelectedAdminId('');
          }}
        >
          <div className="h-full w-full">
            {isPending ? (
              <div className="flex h-40 w-full items-center justify-center">
                <Loader size="lg" />
              </div>
            ) : (
              <Table>
                <TableHeader>
                  {table.getHeaderGroups().map((headerGroup) => (
                    <TableRow key={headerGroup.id}>
                      {headerGroup.headers.map((header) => (
                        <TableHead
                          key={header.id}
                          className="font-bold text-dark"
                        >
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext(),
                              )}
                        </TableHead>
                      ))}
                    </TableRow>
                  ))}
                </TableHeader>
                <TableBody>
                  {table.getRowModel().rows.length > 0 ? (
                    table.getRowModel().rows.map((row) => (
                      <DrawerTrigger
                        key={row.id}
                        asChild
                        onClick={() => {
                          void setSelectedAdminId(row.original.id);
                        }}
                      >
                        <TableRow
                          data-state={row.getIsSelected() && 'selected'}
                          className="h-[60px] cursor-pointer hover:bg-muted/50"
                        >
                          {row.getVisibleCells().map((cell) => (
                            <TableCell key={cell.id} className="p-2">
                              {flexRender(
                                cell.column.columnDef.cell,
                                cell.getContext(),
                              )}
                            </TableCell>
                          ))}
                        </TableRow>
                      </DrawerTrigger>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell
                        colSpan={columns.length}
                        className="h-24 text-center"
                      >
                        {isError
                          ? 'Error loading administrators.'
                          : 'No administrators found.'}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            )}
          </div>
          {selectedAdminId && (
            <DrawerPortal>
              <DrawerOverlay className="fixed inset-0 bg-dark/40" />
              <DrawerContent className="fixed bottom-0 right-0 top-0 z-10 flex w-[800px] !touch-none !select-text">
                <DrawerTitle className="hidden">
                  Administrator Information
                </DrawerTitle>
                <AdminInfo
                  adminId={selectedAdminId}
                  handleClose={() => setSelectedAdminId('')}
                />
              </DrawerContent>
            </DrawerPortal>
          )}
        </Drawer>
      </div>

      <div className={cn('flex items-center justify-between py-4')}>
        <div className="flex gap-3">
          <WithCapability requiredCapabilities={Capability.CREATE_ADMINS}>
            <Button onClick={() => setSelectedAdminId('new')}>
              Add New Administrator
            </Button>
          </WithCapability>
          <Button
            onClick={() => {
              setShowInactive(!showInactive);
              setPagination({ page: 1 });
            }}
          >
            {showInactive
              ? 'Hide Inactive Administrators'
              : 'Show Inactive Administrators'}
          </Button>
        </div>
        {table.getRowModel().rows.length > 0 && (
          <Pagination
            currentPage={pagination.page}
            totalPages={table.getPageCount()}
            onPageChange={(page) => setPagination({ page })}
          />
        )}
      </div>
    </div>
  );
}
