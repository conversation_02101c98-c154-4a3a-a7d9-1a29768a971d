'use client';

import { memo } from 'react';
import { format } from 'date-fns';

import { AdminRole } from '@willow/auth';

import type { ListedAdmin } from '~/hooks/admins';
import { AdminMiscMenu } from './admin-misc-menu';
import { AdminStatusBadge } from './admin-status-badge';

interface AdminBasicInfoSidebarProps {
  admin: ListedAdmin;
  adminId: string;
  isActive: boolean;
  showMenu?: boolean;
  setIsEditMode?: (value: boolean) => void;
  handleDeactivate?: () => Promise<void>;
  handleReactivate?: () => Promise<void>;
  handleDelete?: () => Promise<void>;
}

export const AdminBasicInfoSidebar = memo(function AdminBasicInfoSidebar({
  admin,
  adminId,
  isActive,
  showMenu = true,
  setIsEditMode,
  handleDeactivate,
  handleReactivate,
  handleDelete,
}: AdminBasicInfoSidebarProps) {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex justify-between">
        <div className="flex flex-row items-center gap-4">
          <div className="relative flex h-[50px] w-[50px] items-center justify-center rounded-full bg-denim-light text-xl text-white">
            {admin.firstName.charAt(0)}
            {admin.lastName.charAt(0)}
          </div>
          <div>
            <div className="text-base font-medium text-dark">
              {admin.firstName} {admin.lastName}
            </div>
            <div className="flex flex-col text-[11px] font-medium text-stone/70">
              <span>
                {admin.admin.role === AdminRole.superAdmin
                  ? 'Super Admin'
                  : admin.admin.role === AdminRole.doctor
                    ? 'Doctor'
                    : 'Admin'}
              </span>
            </div>
          </div>
        </div>
        {showMenu &&
          setIsEditMode &&
          handleDeactivate &&
          handleReactivate &&
          handleDelete && (
            <AdminMiscMenu
              adminId={adminId}
              isActive={isActive}
              isSuperAdmin={admin.admin.role === AdminRole.superAdmin}
              setIsEditMode={setIsEditMode}
              handleDeactivate={handleDeactivate}
              handleReactivate={handleReactivate}
              handleDelete={handleDelete}
            />
          )}
      </div>

      <div className="flex flex-col gap-4">
        <div className="text-sm font-normal text-orange">Basic Information</div>
        <div className="flex flex-col gap-1">
          <div className="text-xs font-medium text-stone/70">Status</div>
          <div className="text-xs font-normal text-dark">
            <AdminStatusBadge isActive={isActive} />
          </div>
        </div>
        <div className="flex flex-col gap-1">
          <div className="text-xs font-medium text-stone/70">Email</div>
          <div className="text-xs font-normal text-dark">{admin.email}</div>
        </div>
        <div className="flex flex-col gap-1">
          <div className="text-xs font-medium text-stone/70">Role</div>
          <div className="text-xs font-normal text-dark">
            {admin.admin.role === AdminRole.superAdmin
              ? 'Super Admin'
              : admin.admin.role === AdminRole.doctor
                ? 'Doctor'
                : 'Admin'}
          </div>
        </div>
        <div className="flex flex-col gap-1">
          <div className="text-xs font-medium text-stone/70">Created At</div>
          <div className="text-xs font-normal text-dark">
            {format(new Date(admin.createdAt), 'MMM dd, yyyy')}
          </div>
        </div>
      </div>
    </div>
  );
});
