'use client';

import { memo } from 'react';

interface AdminStatusBadgeProps {
  isActive: boolean;
  className?: string;
}

export const AdminStatusBadge = memo(function AdminStatusBadge({
  isActive,
  className,
}: AdminStatusBadgeProps) {
  return isActive ? (
    <span
      className={`inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 ${className || ''}`}
    >
      Active
    </span>
  ) : (
    <span
      className={`inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800 ${className || ''}`}
    >
      Inactive
    </span>
  );
});
