'use client';

import { use<PERSON>allback, useEffect, useMemo, useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { XIcon } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { AdminRole, Capability } from '@willow/auth';
import { Button } from '@willow/ui/base/button';
import { Form } from '@willow/ui/base/form';
import { useToast } from '@willow/ui/base/use-toast';
import { Loader } from '@willow/ui/loader';

import { WithCapability } from '~/components/capability';
import {
  useCreateAdmin,
  useDeactivateAdmin,
  useDeleteAdmin,
  useGetAdmin,
  useReactivateAdmin,
  useUpdateAdmin,
} from '~/hooks/admins';
import { useAdminActionHandlers } from './admin-action-handlers';
import { AdminBasicInfoSidebar } from './admin-basic-info-sidebar';
import { AdminFormFields } from './admin-form-fields';
import { AdminStatusBadge } from './admin-status-badge';

interface AdminInfoProps {
  adminId: string;
  handleClose: () => void;
}

// Form validation schemas
const createAdminSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Invalid email address'),
  role: z.enum([AdminRole.admin, AdminRole.superAdmin, AdminRole.doctor]),
});

const updateAdminSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Invalid email address'),
  password: z
    .string()
    .optional() // Make password optional for updates
    .refine(
      (val) => {
        if (!val) return true; // Empty is valid (no password change)
        if (val.length < 10) return false; // At least 10 characters
        if (!/[a-z]/.test(val)) return false; // At least one lowercase letter
        if (!/[A-Z]/.test(val)) return false; // At least one uppercase letter
        if (!/\d/.test(val)) return false; // At least one number
        if (!/[^A-Za-z\d]/.test(val)) return false; // At least one special character
        return true;
      },
      {
        message:
          'Password must be at least 10 characters and include at least one uppercase letter, one lowercase letter, one number, and one special character',
      },
    ),
  role: z.enum([AdminRole.admin, AdminRole.superAdmin, AdminRole.doctor]),
});

export function AdminInfo({ adminId, handleClose }: AdminInfoProps) {
  const isNewAdmin = adminId === 'new';
  const {
    data: admin,
    isLoading,
    refetch,
  } = useGetAdmin(isNewAdmin ? '' : adminId);
  const { mutateAsync: createAdmin } = useCreateAdmin();
  const { mutateAsync: updateAdmin } = useUpdateAdmin();
  const { mutateAsync: deactivateAdmin } = useDeactivateAdmin();
  const { mutateAsync: reactivateAdmin } = useReactivateAdmin();
  const { mutateAsync: deleteAdmin } = useDeleteAdmin();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isEditMode, setIsEditMode] = useState(isNewAdmin);

  // Create form with proper validation schema based on mode
  const form = useForm<{
    firstName: string;
    lastName: string;
    email: string;
    password?: string;
    role: AdminRole;
  }>({
    resolver: zodResolver(isNewAdmin ? createAdminSchema : updateAdminSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      ...(isNewAdmin ? {} : { password: '' }),
      role: AdminRole.admin,
    },
  });

  // Determine if admin is a super admin
  const isSuperAdmin = useMemo(() => {
    return admin?.admin?.role === AdminRole.superAdmin;
  }, [admin]);

  // Watch the role field to determine if creating/editing a superadmin
  const selectedRole = form.watch('role');
  const isManagingSuperAdmin = useMemo(() => {
    return selectedRole === AdminRole.superAdmin || isSuperAdmin;
  }, [selectedRole, isSuperAdmin]);

  // Load admin data when viewing/editing existing admin
  useEffect(() => {
    if (admin && !isNewAdmin) {
      form.reset({
        firstName: admin.firstName,
        lastName: admin.lastName,
        email: admin.email,
        role: admin.admin.role,
      });
    }
  }, [admin, form, isNewAdmin]);

  // Use the extracted action handlers
  const { handleDeactivate, handleReactivate, handleDelete } =
    useAdminActionHandlers({
      adminId,
      isNewAdmin,
      isSuperAdmin,
      deactivateAdmin,
      reactivateAdmin,
      deleteAdmin,
      refetch,
      handleClose,
    });

  // Wrap the form submission handler in useCallback to prevent unnecessary re-renders
  const onSubmit = useCallback(
    async (formData: {
      firstName: string;
      lastName: string;
      email: string;
      password?: string;
      role: AdminRole;
    }) => {
      setIsSubmitting(true);
      try {
        // Check if user is trying to create/edit a superadmin
        const isSuperAdminOperation =
          formData.role === AdminRole.superAdmin ||
          (admin?.admin?.role === AdminRole.superAdmin && !isNewAdmin);

        if (isNewAdmin) {
          if (!formData.email) {
            throw new Error('Email is required for new administrators');
          }

          await createAdmin({
            firstName: formData.firstName,
            lastName: formData.lastName,
            email: formData.email,
            role: formData.role,
          });

          toast({
            title: 'Success',
            description:
              'Administrator created successfully. An email with login credentials has been sent to the administrator.',
          });
          handleClose();
        } else {
          await updateAdmin({
            id: adminId,
            adminData: {
              firstName: formData.firstName,
              lastName: formData.lastName,
              email: formData.email,
              password: formData.password,
              role: formData.role,
            },
          });

          await refetch();

          toast({
            title: 'Success',
            description: 'Administrator updated successfully',
          });
          setIsEditMode(false);
        }
      } catch (error) {
        console.error('Error saving administrator:', error);
        toast({
          title: 'Error',
          description:
            'Failed to save administrator. If you are downgrading the last Super Admin, the operation will fail.',
          variant: 'destructive',
        });
      } finally {
        setIsSubmitting(false);
      }
    },
    [
      adminId,
      admin,
      createAdmin,
      handleClose,
      isNewAdmin,
      refetch,
      toast,
      updateAdmin,
    ],
  );

  // Loading state
  if (isLoading && !isNewAdmin) {
    return (
      <div className="flex h-full w-full items-center justify-center bg-white">
        <Loader className="w-full bg-white" />
      </div>
    );
  }

  // Edit mode for existing admins or new admin form
  if (isEditMode) {
    // For existing admins, use split layout
    if (!isNewAdmin && admin) {
      const isActive = !admin.deletedAt;

      return (
        <div className="relative flex h-full w-full grow bg-white">
          {/* Left sidebar */}
          <div className="flex w-1/3 flex-col justify-between overflow-scroll bg-stone-light px-6 py-10">
            <AdminBasicInfoSidebar
              admin={admin}
              adminId={adminId}
              isActive={isActive}
              showMenu={false}
            />
          </div>

          {/* Right content area with form */}
          <div className="flex w-2/3 flex-col overflow-scroll">
            <div className="sticky top-0 z-10 bg-white px-10 pt-10">
              <div className="mb-6 flex flex-row justify-between">
                <h2 className="text-xl font-semibold text-denim">
                  Edit Administrator
                </h2>
                <XIcon
                  size={24}
                  className="cursor-pointer text-denim"
                  onClick={handleClose}
                />
              </div>
            </div>

            <div className="px-10 pb-10">
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit as any)}
                  className="space-y-5"
                >
                  <div className="grid grid-cols-1 gap-6">
                    <AdminFormFields
                      form={form}
                      isNewAdmin={false}
                      adminId={adminId}
                    />
                  </div>

                  <div className="flex justify-between border-t pt-6">
                    <div className="flex gap-2">
                      <Button
                        type="button"
                        variant="tertiary"
                        onClick={() => setIsEditMode(false)}
                        disabled={isSubmitting}
                      >
                        Cancel
                      </Button>
                      <WithCapability
                        requiredCapabilities={
                          isManagingSuperAdmin
                            ? Capability.MANAGE_SUPER_ADMINS
                            : Capability.EDIT_ADMINS
                        }
                      >
                        <Button type="submit" disabled={isSubmitting}>
                          {isSubmitting ? 'Saving...' : 'Update Administrator'}
                        </Button>
                      </WithCapability>
                    </div>
                  </div>
                </form>
              </Form>
            </div>
          </div>
        </div>
      );
    }

    // For new admins
    return (
      <div className="relative flex h-full w-full grow bg-white">
        {/* Thin left sidebar */}
        <div className="flex w-[50px] flex-col bg-stone-light">
          {/* Empty sidebar */}
        </div>

        {/* Right content area with form */}
        <div className="flex w-[calc(100%-50px)] flex-col overflow-scroll">
          <div className="sticky top-0 z-10 bg-white px-10 pt-10">
            <div className="mb-6 flex flex-row justify-between">
              <h2 className="text-xl font-semibold text-denim">
                Add New Administrator
              </h2>
              <XIcon
                size={24}
                className="cursor-pointer text-denim"
                onClick={handleClose}
              />
            </div>
          </div>

          <div className="px-10 pb-10">
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit as any)}
                className="space-y-5"
              >
                <div className="grid grid-cols-1 gap-6">
                  <AdminFormFields
                    form={form}
                    isNewAdmin={true}
                    adminId={adminId}
                  />
                </div>

                <div className="flex justify-between border-t pt-6">
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="tertiary"
                      onClick={handleClose}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                    <WithCapability
                      requiredCapabilities={
                        isManagingSuperAdmin
                          ? Capability.MANAGE_SUPER_ADMINS
                          : Capability.CREATE_ADMINS
                      }
                    >
                      <Button type="submit" disabled={isSubmitting}>
                        {isSubmitting ? 'Creating...' : 'Create Administrator'}
                      </Button>
                    </WithCapability>
                  </div>
                </div>
              </form>
            </Form>
          </div>
        </div>
      </div>
    );
  }

  // View mode for existing admins
  if (!isEditMode && !isNewAdmin && admin) {
    const isActive = !admin.deletedAt;

    return (
      <div className="relative flex h-full w-full grow bg-white">
        {/* Left sidebar with basic info */}
        <div className="flex w-1/3 flex-col justify-between overflow-scroll bg-stone-light px-6 py-10">
          <AdminBasicInfoSidebar
            admin={admin}
            adminId={adminId}
            isActive={isActive}
            setIsEditMode={setIsEditMode}
            handleDeactivate={handleDeactivate}
            handleReactivate={handleReactivate}
            handleDelete={handleDelete}
          />
        </div>

        {/* Right content area with admin details */}
        <div className="flex w-2/3 flex-col overflow-scroll">
          <div className="sticky top-0 z-10 bg-white px-10 pt-10">
            <div className="flex flex-row justify-end text-denim">
              <XIcon
                size={24}
                className="cursor-pointer"
                onClick={handleClose}
              />
            </div>
          </div>

          <div className="px-10">
            <div className="mb-5 mt-10 space-y-6">
              <div className="text-2xl font-medium text-denim">
                Administrator Details
              </div>

              <div className="rounded-lg border p-4 shadow-sm">
                <h3 className="mb-4 text-lg font-medium text-denim">
                  Account Information
                </h3>
                <div className="grid grid-cols-2 gap-x-4 gap-y-3">
                  <div>
                    <span className="text-sm font-medium text-dark">
                      First Name:
                    </span>
                    <div className="text-sm text-denim">{admin.firstName}</div>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-dark">
                      Last Name:
                    </span>
                    <div className="text-sm text-denim">{admin.lastName}</div>
                  </div>
                  <div className="col-span-2">
                    <span className="text-sm font-medium text-dark">
                      Email:
                    </span>
                    <div className="text-sm text-denim">{admin.email}</div>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-dark">Role:</span>
                    <div className="mt-1">
                      {admin.admin.role === AdminRole.superAdmin ? (
                        <span className="inline-flex items-center rounded-full bg-electric-light px-2.5 py-0.5 text-xs font-medium text-denim">
                          Super Admin
                        </span>
                      ) : (
                        <span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-dark">
                          Admin
                        </span>
                      )}
                    </div>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-dark">
                      Status:
                    </span>
                    <div className="mt-1">
                      <AdminStatusBadge isActive={isActive} />
                    </div>
                  </div>
                  <div className="col-span-2">
                    <span className="text-sm font-medium text-dark">
                      Created:
                    </span>
                    <div className="text-sm text-denim">
                      {format(new Date(admin.createdAt), 'MMM dd, yyyy')}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
}
