'use client';

import { useCallback } from 'react';

import { Capability } from '@willow/auth';
import { useToast } from '@willow/ui/base/use-toast';

import { useCapabilities } from '~/hooks/useCapabilities';

interface AdminActionHandlersProps {
  adminId: string;
  isNewAdmin: boolean;
  isSuperAdmin: boolean;
  deactivateAdmin: (id: string) => Promise<any>;
  reactivateAdmin: (id: string) => Promise<any>;
  deleteAdmin: (id: string) => Promise<any>;
  refetch: () => Promise<any>;
  handleClose: () => void;
}

export const useAdminActionHandlers = ({
  adminId,
  isNewAdmin,
  isSuperAdmin,
  deactivateAdmin,
  reactivateAdmin,
  deleteAdmin,
  refetch,
  handleClose,
}: AdminActionHandlersProps) => {
  const { toast } = useToast();
  const { hasCapability } = useCapabilities();

  // Function to handle admin deactivation
  const handleDeactivate = useCallback(async () => {
    // Check if user has permission to deactivate a superadmin
    if (isSuperAdmin && !hasCapability(Capability.MANAGE_SUPER_ADMINS)) {
      toast({
        title: 'Permission Denied',
        description:
          'You do not have permission to deactivate superadmin users',
        variant: 'destructive',
      });
      return;
    }

    const confirmMessage = isSuperAdmin
      ? 'Are you sure you want to deactivate this administrator? This action can be undone later.'
      : 'Are you sure you want to deactivate this administrator?';

    if (!isNewAdmin && window.confirm(confirmMessage)) {
      try {
        await deactivateAdmin(adminId);
        await refetch();
        toast({
          title: 'Success',
          description: 'Administrator deactivated successfully',
        });
      } catch (error) {
        console.error('Error deactivating admin:', error);
        toast({
          title: 'Error',
          description: isSuperAdmin
            ? 'Failed to deactivate administrator. This may be the last Super Admin account which cannot be deactivated.'
            : 'Failed to deactivate administrator',
          variant: 'destructive',
        });
      }
    }
  }, [
    adminId,
    isNewAdmin,
    isSuperAdmin,
    deactivateAdmin,
    refetch,
    toast,
    hasCapability,
  ]);

  // Function to handle admin reactivation
  const handleReactivate = useCallback(async () => {
    // Check if user has permission to reactivate a superadmin
    if (isSuperAdmin && !hasCapability(Capability.MANAGE_SUPER_ADMINS)) {
      toast({
        title: 'Permission Denied',
        description:
          'You do not have permission to reactivate superadmin users',
        variant: 'destructive',
      });
      return;
    }

    if (
      !isNewAdmin &&
      window.confirm('Are you sure you want to reactivate this administrator?')
    ) {
      try {
        await reactivateAdmin(adminId);
        await refetch();
        toast({
          title: 'Success',
          description: 'Administrator reactivated successfully',
        });
      } catch (error) {
        console.error('Error reactivating admin:', error);
        toast({
          title: 'Error',
          description:
            'Failed to reactivate administrator. The email may now be in use by another account.',
          variant: 'destructive',
        });
      }
    }
  }, [
    adminId,
    isNewAdmin,
    isSuperAdmin,
    reactivateAdmin,
    refetch,
    toast,
    hasCapability,
  ]);

  // Function to handle admin permanent deletion
  const handleDelete = useCallback(async () => {
    // Check if user has permission to delete a superadmin
    if (isSuperAdmin && !hasCapability(Capability.MANAGE_SUPER_ADMINS)) {
      toast({
        title: 'Permission Denied',
        description: 'You do not have permission to delete superadmin users',
        variant: 'destructive',
      });
      return;
    }

    const confirmMessage =
      'WARNING: This will permanently delete the administrator account and cannot be undone. Are you absolutely sure?';

    if (!isNewAdmin && window.confirm(confirmMessage)) {
      try {
        await deleteAdmin(adminId);
        toast({
          title: 'Success',
          description: 'Administrator permanently deleted',
        });
        handleClose(); // Close the modal after successful deletion
      } catch (error) {
        console.error('Error deleting admin:', error);
        toast({
          title: 'Error',
          description: isSuperAdmin
            ? 'Failed to delete administrator. This may be the last Super Admin account or your own account which cannot be deleted.'
            : 'Failed to delete administrator. This may be your own account which cannot be deleted.',
          variant: 'destructive',
        });
      }
    }
  }, [
    adminId,
    isNewAdmin,
    isSuperAdmin,
    deleteAdmin,
    handleClose,
    toast,
    hasCapability,
  ]);

  return {
    handleDeactivate,
    handleReactivate,
    handleDelete,
  };
};
