'use client';

import { useMemo } from 'react';
import { EllipsisIcon } from 'lucide-react';

import { Capability } from '@willow/auth';
import { cn } from '@willow/ui';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@willow/ui/base/dropdown-menu';

import { useCapabilities } from '~/hooks/useCapabilities';
import { useProfileQuery } from '~/hooks/useProfile';

interface ActionType {
  name: string;
  handleAction?: () => void;
  className?: string;
  requiredCapability?: Capability;
}

interface AdminMiscMenuProps {
  adminId: string;
  isActive: boolean;
  isSuperAdmin: boolean;
  setIsEditMode: (value: boolean) => void;
  handleDeactivate: () => Promise<void>;
  handleReactivate: () => Promise<void>;
  handleDelete: () => Promise<void>;
}

export function AdminMiscMenu({
  adminId,
  isActive,
  isSuperAdmin,
  setIsEditMode,
  handleDeactivate,
  handleReactivate,
  handleDelete,
}: AdminMiscMenuProps) {
  const { hasCapability } = useCapabilities();
  const { data: currentUser } = useProfileQuery();

  // Check if this is the current user's own admin record
  const isSelfEditing = currentUser?.id === adminId;

  const actions: ActionType[] = useMemo(
    () => [
      {
        name: 'Edit Admin',
        handleAction: () => setIsEditMode(true),
        // If editing a superadmin, require MANAGE_SUPER_ADMINS capability
        requiredCapability: isSuperAdmin
          ? Capability.MANAGE_SUPER_ADMINS
          : Capability.EDIT_ADMINS,
      },
      { name: 'separator-1' },
      {
        name: isActive ? 'Deactivate Admin' : 'Reactivate Admin',
        className: isActive ? 'text-red-500' : undefined,
        // If deactivating/reactivating a superadmin, require MANAGE_SUPER_ADMINS capability
        requiredCapability: isSuperAdmin
          ? Capability.MANAGE_SUPER_ADMINS
          : isActive
            ? Capability.DEACTIVATE_ADMINS
            : Capability.REACTIVATE_ADMINS,
        handleAction: () => {
          if (isActive) {
            void handleDeactivate();
          } else {
            void handleReactivate();
          }
        },
      },
      { name: 'separator-2' },
      {
        name: 'Delete Admin',
        className: 'text-red-700 font-semibold',
        // If deleting a superadmin, require MANAGE_SUPER_ADMINS capability
        requiredCapability: isSuperAdmin
          ? Capability.MANAGE_SUPER_ADMINS
          : Capability.DELETE_ADMINS,
        handleAction: () => {
          void handleDelete();
        },
      },
    ],
    [
      isActive,
      isSuperAdmin,
      setIsEditMode,
      handleDeactivate,
      handleReactivate,
      handleDelete,
    ],
  );

  // Filter actions to only include those the user has permission for
  const visibleActions = useMemo(() => {
    return actions.filter((action) => {
      // Keep separators and actions without capability requirements
      if (!action.requiredCapability || action.name.includes('separator')) {
        return true;
      }
      // Filter out actions the user doesn't have capability for
      if (!hasCapability(action.requiredCapability)) {
        return false;
      }
      // Prevent self-deactivation
      if (isSelfEditing && isActive && action.name === 'Deactivate Admin') {
        return false;
      }
      return true;
    });
  }, [actions, hasCapability, isSelfEditing, isActive]);

  // Don't render the menu if there are no visible actions
  // or if the only visible actions are separators
  const hasVisibleNonSeparatorActions = visibleActions.some(
    (action) => !action.name.includes('separator'),
  );

  if (!hasVisibleNonSeparatorActions) {
    return null;
  }

  return (
    <div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <div className="cursor-pointer">
            <EllipsisIcon size={24} color="#2F4C78" />
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-44 px-0" align="end">
          {visibleActions.map((action) => {
            if (action.name.includes('separator'))
              return (
                <DropdownMenuSeparator
                  key={action.name}
                  className="mx-auto w-10/12"
                />
              );
            return (
              <DropdownMenuItem
                key={action.name}
                onSelect={() => {
                  if (action.handleAction) {
                    action.handleAction();
                  }
                }}
                className={cn(
                  'cursor-pointer px-4 py-3 text-xs font-normal focus:bg-[#ffecca] focus:text-[#2f4c78]',
                  action.className,
                )}
              >
                {action.name}
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
