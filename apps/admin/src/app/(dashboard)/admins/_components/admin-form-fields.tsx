'use client';

import { memo } from 'react';

import { AdminRole, Capability } from '@willow/auth';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@willow/ui/base/form';
import { Input } from '@willow/ui/base/input';

import { WithCapability } from '~/components/capability';
import { useProfileQuery } from '~/hooks/useProfile';

interface AdminFormFieldsProps {
  form: any;
  isNewAdmin: boolean;
  adminId?: string;
}

export const AdminFormFields = memo(function AdminFormFields({
  form,
  isNewAdmin,
  adminId,
}: AdminFormFieldsProps) {
  const { data: currentUser } = useProfileQuery();

  // Check if this is the current user's own admin record
  const isSelfEditing = !isNewAdmin && currentUser?.id === adminId;

  return (
    <>
      <div className="grid grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="firstName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>First Name</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="lastName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Last Name</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <FormField
        control={form.control}
        name="email"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Email</FormLabel>
            <FormControl>
              <Input {...field} type="email" />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {!isNewAdmin && (
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Password</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  type="password"
                  placeholder="Leave blank to keep current password"
                />
              </FormControl>
              <FormMessage />
              <p className="text-xs text-gray-500">
                Password must be at least 10 characters and include at least one
                uppercase letter, one lowercase letter, one number, and one
                special character. Leave blank to keep the current password.
              </p>
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="role"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Role</FormLabel>
            <FormControl>
              <select
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                {...field}
                disabled={isSelfEditing}
              >
                <option value={AdminRole.admin}>Admin</option>
                <option value={AdminRole.doctor}>Doctor</option>
                {/* Only show superAdmin option if the current user can manage superAdmins */}
                <WithCapability
                  requiredCapabilities={Capability.MANAGE_SUPER_ADMINS}
                >
                  <option value={AdminRole.superAdmin}>Super Admin</option>
                </WithCapability>
              </select>
            </FormControl>
            <FormMessage />
            <p className="mt-1 text-xs text-gray-500">
              {isSelfEditing && (
                <span className="font-medium text-orange-600">
                  You cannot change your own role for security reasons.
                </span>
              )}
              {!isSelfEditing && field.value === AdminRole.superAdmin && (
                <span className="font-medium text-error">
                  Super Admin has full access to all system capabilities.
                </span>
              )}
              {!isSelfEditing && field.value === AdminRole.admin && (
                <span>
                  Admin has access to most features except full admin
                  management.
                </span>
              )}
              {!isSelfEditing && field.value === AdminRole.doctor && (
                <span>
                  Doctor role has limited access to product and pharmacy
                  management only.
                </span>
              )}
            </p>
          </FormItem>
        )}
      />
    </>
  );
});
