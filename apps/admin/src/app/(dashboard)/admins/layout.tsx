'use client';

import { Capability } from '@willow/auth';

import { WithCapability } from '~/components/capability';

export default function AdminsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <WithCapability
      requiredCapabilities={Capability.VIEW_ADMINS}
      fallback={
        <div className="flex h-full w-full items-center justify-center">
          <div className="text-xl font-medium text-denim">
            You don&apos;t have permission to access this page.
          </div>
        </div>
      }
    >
      <div className="h-full w-full p-12">
        <div className="h-full w-full overflow-y-auto">{children}</div>
      </div>
    </WithCapability>
  );
}
