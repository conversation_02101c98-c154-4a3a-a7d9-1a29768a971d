'use client';

import React from 'react';
import { redirect } from 'next/navigation';

import { DoctorsTable } from '../_components/doctors-table';

const DoctorGroupsList = ['all'];
type DoctorGroup = 'all';

export default function Page({
  params: { group },
}: {
  params: { group: DoctorGroup };
}) {
  if (!DoctorGroupsList.includes(group)) return redirect('/doctors/all');
  return <DoctorsTable />;
}
