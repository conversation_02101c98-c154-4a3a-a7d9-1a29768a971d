'use client';

import React, {
  forwardRef,
  useCallback,
  useImperativeHandle,
  useState,
} from 'react';
import NextImage from 'next/image';
import { useDropzone } from 'react-dropzone';

import { useProfilePictureUpload } from '~/hooks/useProfilePictureUpload';

interface DoctorProfilePictureProps {
  onImageKeyChange: (key: string | null) => void;
  initialImageUrl?: string;
}

export interface ProfilePictureHandle {
  handleFormSubmit: () => Promise<string | null>;
}

export const DoctorProfilePicture = forwardRef<
  ProfilePictureHandle,
  DoctorProfilePictureProps
>(({ onImageKeyChange, initialImageUrl }, ref) => {
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(
    initialImageUrl || null,
  );
  const { uploadImage } = useProfilePictureUpload();

  const processImage = async (file: File): Promise<File> => {
    return new Promise((resolve, reject) => {
      const img = new globalThis.Image();
      img.onload = () => {
        // Create canvas with fixed dimensions
        const canvas = document.createElement('canvas');
        canvas.width = 800;
        canvas.height = 800;
        const ctx = canvas.getContext('2d');

        if (!ctx) {
          reject(new Error('No 2d context'));
          return;
        }

        // Calculate dimensions to crop center of image
        let sourceX = 0;
        let sourceY = 0;
        let sourceWidth = img.width;
        let sourceHeight = img.height;

        // If the image is wider than tall, crop the sides
        if (img.width > img.height) {
          sourceWidth = img.height;
          sourceX = (img.width - img.height) / 2;
        }
        // If the image is taller than wide, crop the top and bottom
        else if (img.height > img.width) {
          sourceHeight = img.width;
          sourceY = (img.height - img.width) / 2;
        }

        // Draw the centered, cropped image to the canvas
        // Convert values to proper types to handle TypeScript warnings
        const imgElement = img;
        const sX = Number(sourceX);
        const sY = Number(sourceY);
        const sWidth = Number(sourceWidth);
        const sHeight = Number(sourceHeight);

        ctx.drawImage(imgElement, sX, sY, sWidth, sHeight, 0, 0, 800, 800);

        // Convert to blob
        canvas.toBlob(
          (blob) => {
            if (!blob) {
              reject(new Error('Canvas is empty'));
              return;
            }

            // Create a new file from the blob
            const croppedFile = new File([blob], 'profile-picture.jpg', {
              type: 'image/jpeg',
              lastModified: Date.now(),
            });

            resolve(croppedFile);
          },
          'image/jpeg',
          0.85, // Quality
        );
      };

      img.onerror = () => {
        reject(new Error('Error loading image'));
      };

      img.src = URL.createObjectURL(file);
    });
  };

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      if (acceptedFiles && acceptedFiles.length > 0) {
        // We know this is a File because we've checked the array has elements
        const file = acceptedFiles[0];

        try {
          // Process the image (center crop to 800x800)
          const croppedFile = await processImage(file!);

          // Clean up the previous preview URL to avoid memory leaks
          if (previewUrl && previewUrl !== initialImageUrl) {
            URL.revokeObjectURL(previewUrl);
          }

          // Create a preview URL for the cropped image
          const newPreviewUrl = URL.createObjectURL(croppedFile);
          setPreviewUrl(newPreviewUrl);
          setImageFile(croppedFile);
        } catch (error) {
          console.error('Error processing image:', error);
        }
      }
    },
    [previewUrl, initialImageUrl],
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: (acceptedFiles) => {
      void onDrop(acceptedFiles);
    },
    accept: {
      'image/jpeg': [],
      'image/png': [],
      'image/gif': [],
      'image/webp': [],
    },
    maxFiles: 1,
  });

  // Expose the handleFormSubmit function to the parent component
  useImperativeHandle(
    ref,
    () => ({
      handleFormSubmit: async () => {
        console.log('DoctorProfilePicture: handleFormSubmit called');
        if (imageFile) {
          console.log('DoctorProfilePicture: imageFile exists, uploading...');
          const key = await uploadImage(imageFile);
          console.log('DoctorProfilePicture: upload complete, key:', key);
          onImageKeyChange(key);
          return key;
        }
        console.log('DoctorProfilePicture: no imageFile to upload');
        return null;
      },
    }),
    [imageFile, onImageKeyChange, uploadImage],
  );

  return (
    <div className="w-full">
      <div
        {...getRootProps()}
        className={`relative flex aspect-square cursor-pointer flex-col items-center justify-center rounded-md border-2 bg-gray-100 ${
          isDragActive ? 'border-dashed border-blue-500' : 'border-gray-200'
        }`}
        style={{ height: '200px', width: '200px' }} // Fixed dimensions for the container
      >
        <input {...getInputProps()} />

        {previewUrl ? (
          <div className="relative h-full w-full">
            <NextImage
              src={previewUrl}
              alt="Doctor profile"
              className="h-full w-full rounded-md object-cover"
              width={200}
              height={200}
            />
            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-0 transition-all duration-200 hover:bg-opacity-20">
              <span className="font-medium text-transparent hover:text-white">
                Change Photo
              </span>
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center p-6">
            <div className="mb-4 flex h-24 w-24 items-center justify-center rounded-full bg-blue-500">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-12 w-12 text-white"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                />
              </svg>
            </div>
            <p className="text-center text-sm text-gray-500">
              {isDragActive
                ? 'Drop the image here'
                : 'Drag profile picture here or click to select'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
});

// This is the component that should be used in the parent form
export const DoctorProfilePictureField = forwardRef<
  ProfilePictureHandle,
  {
    name: string;
    initialImageUrl?: string;
  }
>(({ name, initialImageUrl }, ref) => {
  const [imageKey, setImageKey] = useState<string | null>(null);
  const innerRef = React.useRef<ProfilePictureHandle>(null);

  // Forward the inner ref methods to the outer ref
  useImperativeHandle(
    ref,
    () => ({
      handleFormSubmit: async () => {
        console.log('DoctorProfilePictureField: handleFormSubmit called');
        if (innerRef.current) {
          console.log('DoctorProfilePictureField: Forwarding to inner ref');
          return await innerRef.current.handleFormSubmit();
        }
        console.log('DoctorProfilePictureField: No inner ref');
        return null;
      },
    }),
    [innerRef],
  );

  return (
    <div>
      <DoctorProfilePicture
        ref={innerRef}
        onImageKeyChange={setImageKey}
        initialImageUrl={initialImageUrl}
      />
      <input type="hidden" name={name} value={imageKey || ''} />
    </div>
  );
});
