import { useState } from 'react';

// Hook to manage the out-of-office drawer
export function useOutOfOfficeDrawer() {
  // State for drawer visibility
  const [isOpen, setIsOpen] = useState(false);

  // State to track if we're editing an existing transfer
  const [activeBulkTransfer, setActiveBulkTransfer] = useState<any>(null);

  const handleOpen = (existingTransfer?: any) => {
    if (existingTransfer) {
      setActiveBulkTransfer(existingTransfer);
    } else {
      setActiveBulkTransfer(null);
    }
    setIsOpen(true);
  };

  const handleClose = () => {
    setIsOpen(false);
    setActiveBulkTransfer(null);
  };

  return {
    isOpen,
    activeBulkTransfer,
    handleOpen,
    handleClose,
    drawer: { isOpen, handleOpen, handleClose, activeBulkTransfer },
  };
}

// Hook to manage the transfer patients drawer
export function useTransferDrawer() {
  // State for drawer visibility
  const [isOpen, setIsOpen] = useState(false);

  // State to track if we're editing an existing transfer
  const [activeBulkTransfer, setActiveBulkTransfer] = useState<any>(null);

  const handleOpen = (existingTransfer?: any) => {
    if (existingTransfer) {
      setActiveBulkTransfer(existingTransfer);
    } else {
      setActiveBulkTransfer(null);
    }
    setIsOpen(true);
  };

  const handleClose = () => {
    setIsOpen(false);
    setActiveBulkTransfer(null);
  };

  return {
    isOpen,
    activeBulkTransfer,
    handleOpen,
    handleClose,
    drawer: { isOpen, handleOpen, handleClose, activeBulkTransfer },
  };
}
// Hook to manage the reset password drawer
export function useDoctorPasswordResetDrawer() {
  // State for drawer visibility
  const [isOpen, setIsOpen] = useState(false);

  const handleOpen = () => {
    setIsOpen(true);
  };

  const handleClose = () => {
    setIsOpen(false);
  };

  return {
    isOpen,
    handleOpen,
    handleClose,
    drawer: { isOpen, handleOpen, handleClose },
  };
}
