import { useMemo } from 'react';
import { EllipsisIcon } from 'lucide-react';

import { cn } from '@willow/ui';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@willow/ui/base/dropdown-menu';

interface ActionType {
  name: string;
  handleAction?: () => void;
  className?: string;
}

export const DoctorMiscMenu = ({
  isActive,
  setIsEditMode,
  handleDeactivate,
  handleReactivate,
  handleOpenTransferDrawer,
  handleOpenOutOfOfficeDrawer,
  handleOpenDoctorPasswordResetDrawer,
  hasPatients = false,
  hasPendingTransfer = false,
  hasPendingOutOfOffice = false,
  handleEditPendingTransfer = undefined,
  handleEditPendingOutOfOffice = undefined,
}: {
  isActive: boolean;
  setIsEditMode: (value: string) => void;
  handleDeactivate: () => void;
  handleReactivate: () => void;
  handleOpenTransferDrawer: () => void;
  handleOpenDoctorPasswordResetDrawer: () => void;
  handleOpenOutOfOfficeDrawer: () => void;
  hasPatients?: boolean;
  hasPendingTransfer?: boolean;
  hasPendingOutOfOffice?: boolean;
  handleEditPendingTransfer?: () => void;
  handleEditPendingOutOfOffice?: () => void;
}) => {
  const actions: ActionType[] = useMemo(() => {
    const baseActions = [
      {
        name: 'Edit Doctor',
        handleAction: () => setIsEditMode('true'),
      },
      {
        name: 'Trigger Password Reset',
        handleAction: handleOpenDoctorPasswordResetDrawer,
      },
      { name: 'separator-1' },
    ];

    // Transfer and Out of Office actions based on state
    const transferActions = [];

    // Only add one option based on pending transfers
    if (hasPendingOutOfOffice) {
      // Only show the out of office scheduled item
      transferActions.push({
        name: 'Out of Office Scheduled',
        handleAction: handleEditPendingOutOfOffice,
        className: 'text-blue-600',
      });
    } else if (hasPendingTransfer) {
      // Only show the transfer patients scheduled item
      transferActions.push({
        name: 'Transfer Patients Scheduled',
        handleAction: handleEditPendingTransfer,
        className: 'text-blue-600',
      });
    } else {
      // No pending transfers, show both original options
      transferActions.push({
        name: 'Set Out of Office',
        handleAction: hasPatients
          ? () => handleOpenOutOfOfficeDrawer()
          : undefined,
        className: !hasPatients ? 'opacity-50 cursor-not-allowed' : undefined,
      });

      transferActions.push({
        name: 'Transfer Patients',
        handleAction: hasPatients
          ? () => handleOpenTransferDrawer()
          : undefined,
        className: !hasPatients ? 'opacity-50 cursor-not-allowed' : undefined,
      });
    }

    return [
      ...baseActions,
      ...transferActions,
      { name: 'separator-2' },
      {
        name: isActive ? 'Deactivate Doctor' : 'Reactivate Doctor',
        className: isActive ? 'text-red-500' : undefined,
        handleAction: () => {
          if (isActive) {
            handleDeactivate();
          } else {
            handleReactivate();
          }
        },
      },
    ];
  }, [
    isActive,
    setIsEditMode,
    handleDeactivate,
    handleReactivate,
    handleOpenTransferDrawer,
    handleOpenOutOfOfficeDrawer,
    handleEditPendingTransfer,
    handleEditPendingOutOfOffice,
    handleOpenDoctorPasswordResetDrawer,
    hasPatients,
    hasPendingTransfer,
    hasPendingOutOfOffice,
  ]);

  return (
    <div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <div className="cursor-pointer">
            <EllipsisIcon size={24} color="#2F4C78" />
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56 px-0" align="end">
          {actions.map((action) => {
            if (action.name.includes('separator'))
              return (
                <DropdownMenuSeparator
                  key={action.name}
                  className="mx-auto w-10/12"
                />
              );
            return (
              <DropdownMenuItem
                key={action.name}
                onSelect={(e) => {
                  if (!action.handleAction) {
                    // Prevent closing menu if item is disabled
                    e.preventDefault();
                    return;
                  }
                  action.handleAction();
                }}
                className={cn(
                  'px-4 py-3 text-xs font-normal focus:bg-[#ffecca] focus:text-[#2f4c78]',
                  action.handleAction ? 'cursor-pointer' : 'cursor-not-allowed',
                  action.className,
                )}
              >
                {action.name}
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
