'use client';

import type { ColumnDef } from '@tanstack/react-table';
import { useCallback, useMemo, useRef, useState } from 'react';
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { ListIcon, MapPinIcon, XIcon } from 'lucide-react';
import { parseAsString, useQueryState } from 'nuqs';

import { cn } from '@willow/ui';
import { Avatar, AvatarFallback, AvatarImage } from '@willow/ui/base/avatar';
import { Button } from '@willow/ui/base/button';
import { DrawerTitle } from '@willow/ui/base/drawer';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@willow/ui/base/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@willow/ui/base/tabs';
import { useToast } from '@willow/ui/base/use-toast';
import {
  Drawer,
  DrawerContent,
  DrawerO<PERSON>lay,
  DrawerPortal,
  DrawerTrigger,
} from '@willow/ui/base/vaul';
import { ColumnHeader } from '@willow/ui/column-header';
import { Loader } from '@willow/ui/loader';
import { Pagination } from '@willow/ui/pagination';
import { SearchInput } from '@willow/ui/search-input';
import { formatBirthDate } from '@willow/utils/format';
import {
  usePaginationSearchParams,
  useSortingQueryState,
} from '@willow/utils/table';

import type { ListedDoctor } from '~/hooks/doctors';
import {
  useDeactivateDoctor,
  useGetDoctor,
  useGetDoctors,
  useReactivateDoctor,
} from '~/hooks/doctors';
import { useDoctorAssetLink } from '~/hooks/links';
import {
  useDoctorPasswordResetDrawer,
  useOutOfOfficeDrawer,
  useTransferDrawer,
} from './doctor-hooks';
import { DoctorInfoEdit } from './doctor-info';
import { DoctorMiscMenu } from './doctor-misc-menu';
import { DoctorResetPasswordDrawer } from './doctor-trigger-password-reset';
import { PatientTransferDrawer } from './patient-transfer-drawer';

export function DoctorsTable() {
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [query, setQuery] = useQueryState(
    'query',
    parseAsString.withDefault(''),
  );
  const [pagination, setPagination] = usePaginationSearchParams();
  const [sorting, setSorting] = useSortingQueryState([
    { id: 'totalPatientCount', desc: true },
  ]);

  const [selectedDoctorId, setSelectedDoctorId] = useQueryState('doctorId', {
    defaultValue: '',
  });

  const [selectedTab, setSelectedTab] = useQueryState('tab', {
    defaultValue: 'states',
    parse: (value) => {
      return ['states', 'patients'].includes(value) ? value : 'states';
    },
  });

  const [isEditMode, setIsEditMode] = useQueryState('edit', {
    defaultValue: 'false',
  });

  // State for showing inactive doctors
  const [showInactive, setShowInactive] = useState(false);

  const fetchParams = useMemo(
    () => ({
      sortBy: sorting.length > 0 ? sorting[0]?.id : undefined,
      direction:
        sorting.length > 0
          ? sorting[0]?.desc
            ? ('desc' as const)
            : ('asc' as const)
          : ('asc' as const),
      page: pagination.page,
      limit: pagination.pageSize,
      search: query ?? undefined,
      showInactive: showInactive,
    }),
    [query, pagination, sorting, showInactive],
  );

  const { data, isPending, isError } = useGetDoctors(fetchParams);
  const { data: selectedDoctor, isPending: isLoadingDoctor } = useGetDoctor(
    selectedDoctorId === 'new' ? '' : selectedDoctorId,
  );

  // Use custom hooks for transfer drawers
  const { drawer: transferDrawer, handleOpen: openTransferDrawer } =
    useTransferDrawer();
  const { drawer: outOfOfficeDrawer, handleOpen: openOutOfOfficeDrawer } =
    useOutOfOfficeDrawer();
  const {
    drawer: doctorResetPasswordDrawer,
    handleOpen: openDoctorPasswordResetDrawer,
  } = useDoctorPasswordResetDrawer();
  const { mutateAsync: deactivateDoctor } = useDeactivateDoctor();
  const { mutateAsync: reactivateDoctor } = useReactivateDoctor();
  const [_isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const { genAssetLink } = useDoctorAssetLink();

  const columns: ColumnDef<ListedDoctor>[] = useMemo(
    () => [
      {
        accessorKey: 'user.firstName',
        id: 'name',
        header: () => <ColumnHeader label="Name" sortKey="name" />,
        cell: ({ row }) => (
          <div className="flex items-center gap-3">
            <Avatar className="h-10 w-10">
              <AvatarImage src={genAssetLink(row.original.image)} />
              <AvatarFallback className="font-bold uppercase text-denim-light">
                {row.original.user?.firstName?.[0]}
                {row.original.user?.lastName?.[0]}
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col gap-1">
              <div className="text-sm text-denim">
                {row.original.user?.firstName} {row.original.user?.lastName}
              </div>
              <div className="font-sans text-xs font-normal text-dark">
                {row.original.role === 'superDoctor'
                  ? 'Super Doctor'
                  : 'Doctor'}
              </div>
            </div>
          </div>
        ),
      },
      {
        accessorKey: 'active',
        header: () => <ColumnHeader label="Status" sortKey="active" />,
        cell: ({ row }) => {
          const isActive = row.original.active;
          return (
            <div className="text-sm">
              {isActive ? (
                <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                  Active
                </span>
              ) : (
                <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                  Inactive
                </span>
              )}
            </div>
          );
        },
      },
      {
        accessorKey: 'totalPatientCount',
        header: () => (
          <ColumnHeader label="Patients" sortKey="totalPatientCount" />
        ),
        cell: ({ row }) => {
          const count = row.original.totalPatientCount ?? 0;
          return (
            <div className="text-sm text-denim">
              {count > 0 ? (
                <span className="inline-flex items-center rounded-full bg-denim-light/20 px-2.5 py-0.5 text-sm font-medium text-denim">
                  {count}
                </span>
              ) : (
                <span className="text-stone">0</span>
              )}
            </div>
          );
        },
      },
      {
        accessorKey: 'user.email',
        header: () => <ColumnHeader label="Email" sortKey="email" />,
        cell: ({ row }) => (
          <div className="text-sm text-denim">{row.original.user?.email}</div>
        ),
      },
      {
        accessorKey: 'doseSpotClinicianId',
        header: 'DoseSpot ID',
        cell: ({ row }) => (
          <div className="text-sm text-dark">
            {row.original.doseSpotClinicianId || 'Not Set'}
          </div>
        ),
      },
    ],
    [genAssetLink],
  );

  // Process the doctors data - filter by active status
  const doctors = useMemo(() => {
    const allDoctors = data?.doctors || [];

    // If showInactive is true, show all doctors
    // If showInactive is false, show only active doctors
    if (!showInactive) {
      return allDoctors.filter((doctor) => doctor.active);
    }

    return allDoctors;
  }, [data, showInactive]);

  const table = useReactTable({
    data: doctors,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: (sorting) => {
      void setSorting(sorting);
    },
    state: {
      sorting,
      pagination: {
        pageIndex: pagination.page - 1,
        pageSize: pagination.pageSize,
      },
    },
    manualPagination: true,
    pageCount: data?.pagination?.totalPages || 1,
  });

  const handleSearchSubmit = useCallback(
    (query: string | null) => {
      void setQuery(query);
      void setPagination({ page: 1 });
    },
    [setPagination, setQuery],
  );

  const handleCloseDrawer = () => {
    void setSelectedDoctorId('');
    void setIsEditMode('false');
  };

  const handleDeactivate = async () => {
    if (
      selectedDoctorId &&
      selectedDoctorId !== 'new' &&
      window.confirm('Are you sure you want to deactivate this doctor?')
    ) {
      setIsSubmitting(true);
      try {
        await deactivateDoctor(selectedDoctorId);
        // The cache is already invalidated in the hook
        // No need to reload the page
        toast({
          title: 'Success',
          description: 'Doctor deactivated successfully',
        });
      } catch (error) {
        console.error('Error deactivating doctor:', error);
        toast({
          title: 'Error',
          description: 'Failed to deactivate doctor',
          variant: 'destructive',
        });
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const handleReactivate = async () => {
    if (
      selectedDoctorId &&
      selectedDoctorId !== 'new' &&
      window.confirm('Are you sure you want to reactivate this doctor?')
    ) {
      setIsSubmitting(true);
      try {
        await reactivateDoctor(selectedDoctorId);
        // The cache is already invalidated in the hook
        // No need to reload the page
        toast({
          title: 'Success',
          description: 'Doctor reactivated successfully',
        });
      } catch (error) {
        console.error('Error reactivating doctor:', error);
        toast({
          title: 'Error',
          description: 'Failed to reactivate doctor',
          variant: 'destructive',
        });
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const handleOpenTransferDrawer = (existingTransfer?: any) => {
    openTransferDrawer(existingTransfer);
  };

  const handleOpenOutOfOfficeDrawer = (existingTransfer?: any) => {
    openOutOfOfficeDrawer(existingTransfer);
  };
  const handleOpenDoctorPasswordResetDrawer = () => {
    openDoctorPasswordResetDrawer();
  };

  // States list is now provided directly by the doctor API response in statePatientCounts

  return (
    <div className="grid h-[calc(100vh-96px)] w-full grid-rows-[auto_1fr_auto]">
      <div className="w-full">
        <div className="mb-5 text-2xl font-medium text-denim">Doctors</div>
        <SearchInput
          className="mb-5"
          ref={searchInputRef}
          onSearch={handleSearchSubmit}
          placeholder="Search doctors"
          defaultValue={query}
        />
      </div>

      <div className="relative flex-1 overflow-auto">
        <Drawer
          direction="right"
          open={selectedDoctorId !== ''}
          onOpenChange={(value) => {
            if (!value) handleCloseDrawer();
          }}
          shouldScaleBackground={false}
        >
          <div className="h-full w-full">
            {isPending ? (
              <div className="flex h-40 w-full items-center justify-center">
                <Loader size="lg" />
              </div>
            ) : (
              <Table>
                <TableHeader>
                  {table.getHeaderGroups().map((headerGroup) => (
                    <TableRow key={headerGroup.id}>
                      {headerGroup.headers.map((header) => (
                        <TableHead
                          key={header.id}
                          className="font-bold text-dark"
                        >
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext(),
                              )}
                        </TableHead>
                      ))}
                    </TableRow>
                  ))}
                </TableHeader>
                <TableBody>
                  {table.getRowModel().rows.length > 0 ? (
                    table.getRowModel().rows.map((row) => (
                      <DrawerTrigger
                        key={row.id}
                        asChild
                        onClick={() => {
                          void setSelectedDoctorId(row.original.id);
                          void setIsEditMode('false');
                        }}
                      >
                        <TableRow
                          data-state={
                            row.original.id === selectedDoctorId
                              ? 'selected'
                              : undefined
                          }
                          className="h-[60px] cursor-pointer hover:bg-muted/50"
                        >
                          {row.getVisibleCells().map((cell) => (
                            <TableCell key={cell.id} className="p-2">
                              {flexRender(
                                cell.column.columnDef.cell,
                                cell.getContext(),
                              )}
                            </TableCell>
                          ))}
                        </TableRow>
                      </DrawerTrigger>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell
                        colSpan={columns.length}
                        className="h-24 text-center"
                      >
                        {isError ? 'Error loading doctors.' : 'No results.'}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            )}
          </div>

          {selectedDoctorId && (
            <DrawerPortal>
              <DrawerOverlay className="fixed inset-0 bg-dark/40" />
              <DrawerContent className="fixed bottom-0 right-0 top-0 z-10 flex w-[800px] !touch-none !select-text overflow-hidden">
                <DrawerTitle className="hidden">Doctor Information</DrawerTitle>

                {selectedDoctorId === 'new' ? (
                  <DoctorInfoEdit
                    doctorId={selectedDoctorId}
                    handleClose={handleCloseDrawer}
                  />
                ) : (
                  <div className="relative flex h-full w-full grow bg-white">
                    {isLoadingDoctor ? (
                      <Loader className="w-full bg-white" />
                    ) : (
                      <>
                        <div className="flex w-1/3 flex-col justify-between overflow-auto bg-stone-light px-6 py-10">
                          <div className="flex flex-col gap-6">
                            <div className="flex justify-between">
                              <div className="flex flex-row items-center gap-4">
                                <div className="relative flex h-[50px] w-[50px] items-center justify-center rounded-full">
                                  <Avatar className="h-full w-full">
                                    <AvatarImage
                                      src={genAssetLink(selectedDoctor?.image)}
                                    />
                                    <AvatarFallback className="bg-denim-light font-bold uppercase text-white">
                                      {selectedDoctor?.user?.firstName?.[0]}
                                      {selectedDoctor?.user?.lastName?.[0]}
                                    </AvatarFallback>
                                  </Avatar>
                                </div>
                                <div>
                                  <div className="text-base font-medium text-dark">
                                    Dr. {selectedDoctor?.user?.lastName}
                                  </div>
                                  <div className="text-[11px] font-medium text-stone/70">
                                    {selectedDoctor?.role === 'superDoctor'
                                      ? 'Super Doctor'
                                      : 'Doctor'}
                                  </div>
                                </div>
                              </div>
                              {isEditMode !== 'true' && (
                                <DoctorMiscMenu
                                  isActive={selectedDoctor?.active ?? false}
                                  setIsEditMode={setIsEditMode}
                                  handleDeactivate={handleDeactivate}
                                  handleReactivate={handleReactivate}
                                  handleOpenTransferDrawer={
                                    handleOpenTransferDrawer
                                  }
                                  handleOpenOutOfOfficeDrawer={
                                    handleOpenOutOfOfficeDrawer
                                  }
                                  handleOpenDoctorPasswordResetDrawer={
                                    handleOpenDoctorPasswordResetDrawer
                                  }
                                  hasPatients={
                                    (selectedDoctor?.totalPatientCount ?? 0) > 0
                                  }
                                  hasPendingTransfer={selectedDoctor?.activeBulkTransfers?.some(
                                    (transfer) =>
                                      transfer.status === 'pending' &&
                                      !transfer.revertAt,
                                  )}
                                  hasPendingOutOfOffice={selectedDoctor?.activeBulkTransfers?.some(
                                    (transfer) =>
                                      transfer.status === 'pending' &&
                                      !!transfer.revertAt,
                                  )}
                                  handleEditPendingTransfer={() => {
                                    const pendingTransfer =
                                      selectedDoctor?.activeBulkTransfers?.find(
                                        (transfer) =>
                                          transfer.status === 'pending' &&
                                          !transfer.revertAt,
                                      );
                                    if (pendingTransfer) {
                                      handleOpenTransferDrawer(pendingTransfer);
                                    }
                                  }}
                                  handleEditPendingOutOfOffice={() => {
                                    const pendingOutOfOffice =
                                      selectedDoctor?.activeBulkTransfers?.find(
                                        (transfer) =>
                                          transfer.status === 'pending' &&
                                          !!transfer.revertAt,
                                      );
                                    if (pendingOutOfOffice) {
                                      handleOpenOutOfOfficeDrawer(
                                        pendingOutOfOffice,
                                      );
                                    }
                                  }}
                                />
                              )}
                            </div>
                            <div className="flex flex-col gap-4">
                              <div className="text-sm font-normal text-orange">
                                Personal Information
                              </div>
                              <div className="flex flex-col gap-1">
                                <div className="text-xs font-medium text-stone/70">
                                  Status
                                </div>
                                <div className="text-xs font-normal text-dark">
                                  {selectedDoctor?.active ? (
                                    <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                                      Active
                                    </span>
                                  ) : (
                                    <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                                      Inactive
                                    </span>
                                  )}
                                </div>
                              </div>
                              <div className="flex flex-col gap-1">
                                <div className="text-xs font-medium text-stone/70">
                                  DoseSpot Registration Status
                                </div>
                                <div className="text-xs font-normal">
                                  {selectedDoctor?.doseSpotClinicianId ? (
                                    selectedDoctor?.doseSpotRegistrationStatus ? (
                                      <span
                                        className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                          selectedDoctor?.doseSpotRegistrationStatus ===
                                          'IDPSuccess'
                                            ? 'bg-green-100'
                                            : 'bg-orange-light'
                                        }`}
                                      >
                                        {selectedDoctor?.doseSpotRegistrationStatus
                                          .replace(
                                            /(?<=[a-z0-9])([A-Z])|(?<=[A-Z])([A-Z])(?=[a-z])/g,
                                            ' $&',
                                          )
                                          .trim()}
                                      </span>
                                    ) : (
                                      <span className="inline-flex items-center rounded-full bg-orange-100 px-2.5 py-0.5 text-xs font-medium text-orange-800">
                                        Pending
                                      </span>
                                    )
                                  ) : (
                                    <span className="text-gray-500">
                                      Not Set
                                    </span>
                                  )}
                                </div>
                              </div>
                              <div className="flex flex-col gap-1">
                                <div className="text-xs font-medium text-stone/70">
                                  Email Address
                                </div>
                                <div className="text-xs font-normal text-dark">
                                  {selectedDoctor?.user?.email}
                                </div>
                              </div>
                              <div className="flex flex-col gap-1">
                                <div className="text-xs font-medium text-stone/70">
                                  Date of Birth
                                </div>
                                <div className="text-xs font-normal text-dark">
                                  {selectedDoctor?.dateOfBirth
                                    ? formatBirthDate(
                                        new Date(selectedDoctor.dateOfBirth),
                                      )
                                    : '-'}
                                </div>
                              </div>
                              <div className="flex flex-col gap-1">
                                <div className="text-xs font-medium text-stone/70">
                                  Phone
                                </div>
                                <div className="text-xs font-normal text-dark">
                                  {selectedDoctor?.primaryPhone || '-'}
                                </div>
                              </div>
                              <div className="flex flex-col gap-1">
                                <div className="text-xs font-medium text-stone/70">
                                  Fax
                                </div>
                                <div className="text-xs font-normal text-dark">
                                  {selectedDoctor?.primaryFax || '-'}
                                </div>
                              </div>
                            </div>
                            <div className="flex flex-col gap-4">
                              <div className="text-sm font-normal text-orange">
                                Professional Information
                              </div>
                              <div className="flex flex-col gap-1">
                                <div className="text-xs font-medium text-stone/70">
                                  NPI Number
                                </div>
                                <div className="text-xs font-normal text-dark">
                                  {selectedDoctor?.npiNumber || '-'}
                                </div>
                              </div>
                              <div className="flex flex-col gap-1">
                                <div className="text-xs font-medium text-stone/70">
                                  DoseSpot ID
                                </div>
                                <div className="text-xs font-normal text-dark">
                                  {selectedDoctor?.doseSpotClinicianId || '-'}
                                </div>
                              </div>
                            </div>
                            <div className="flex flex-col gap-4">
                              <div className="text-sm font-normal text-orange">
                                Address
                              </div>
                              <div className="flex flex-col gap-1">
                                <div className="text-xs font-medium text-stone/70">
                                  Street Address
                                </div>
                                <div className="text-xs font-normal text-dark">
                                  {selectedDoctor?.address1}
                                  {selectedDoctor?.address2 && (
                                    <span>, {selectedDoctor.address2}</span>
                                  )}
                                </div>
                              </div>
                              <div className="flex flex-col gap-1">
                                <div className="text-xs font-medium text-stone/70">
                                  City, State, ZIP
                                </div>
                                <div className="text-xs font-normal text-dark">
                                  {selectedDoctor?.city},{' '}
                                  {selectedDoctor?.state?.code}{' '}
                                  {selectedDoctor?.zip}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="h-full w-2/3 overflow-hidden">
                          {isEditMode === 'true' ? (
                            <div className="flex h-full flex-col">
                              <div className="sticky top-0 z-10 flex shrink-0 justify-end bg-white px-10 pt-10">
                                <XIcon
                                  size={24}
                                  className="cursor-pointer"
                                  onClick={() => setIsEditMode('false')}
                                />
                              </div>
                              <div className="h-[calc(100%-64px)] overflow-auto">
                                <DoctorInfoEdit
                                  doctorId={selectedDoctorId}
                                  handleClose={() => setIsEditMode('false')}
                                />
                              </div>
                            </div>
                          ) : (
                            <Tabs
                              defaultValue={selectedTab}
                              onValueChange={setSelectedTab}
                              className="relative grid w-full grid-rows-[auto_1fr]"
                            >
                              <div className="sticky top-0 z-10 bg-white px-10 pt-10">
                                <div className="flex flex-row justify-end text-denim">
                                  <XIcon
                                    size={24}
                                    className="cursor-pointer"
                                    onClick={handleCloseDrawer}
                                  />
                                </div>
                                <TabsList className="h-fit w-full justify-start gap-5 rounded-none border-b-[1px] border-denim-muted bg-white p-0 text-denim">
                                  <TabsTrigger
                                    className="rounded-none text-xs font-normal text-muted-foreground data-[state=active]:border-b-[3px] data-[state=active]:border-denim data-[state=active]:text-denim data-[state=active]:shadow-none"
                                    value="states"
                                  >
                                    <MapPinIcon size={16} className="mr-1" />
                                    <div>Licensed States</div>
                                  </TabsTrigger>
                                  <TabsTrigger
                                    className="rounded-none text-xs font-normal text-muted-foreground data-[state=active]:border-b-[3px] data-[state=active]:border-denim data-[state=active]:text-denim data-[state=active]:shadow-none"
                                    value="patients"
                                  >
                                    <ListIcon size={16} className="mr-1" />
                                    <div>Patients by State</div>
                                  </TabsTrigger>
                                </TabsList>
                              </div>
                              <TabsContent className="px-10" value="states">
                                {/* Licensed States (enabled) */}
                                <div className="mb-8 mt-10">
                                  <div className="mb-5 text-2xl font-medium text-denim">
                                    Licensed States
                                  </div>
                                  <div className="flex flex-wrap gap-2">
                                    {selectedDoctor?.statePatientCounts &&
                                    selectedDoctor.statePatientCounts.filter(
                                      (state) => state.enabled,
                                    ).length > 0 ? (
                                      selectedDoctor.statePatientCounts
                                        .filter((state) => state.enabled)
                                        .sort((a, b) =>
                                          a.stateName.localeCompare(
                                            b.stateName,
                                          ),
                                        )
                                        .map((state) => (
                                          <div
                                            key={state.stateCode}
                                            className="inline-flex items-center rounded-full bg-green-100 px-3 py-1 text-sm font-medium text-green-800"
                                          >
                                            {state.stateName}
                                          </div>
                                        ))
                                    ) : (
                                      <div className="text-sm text-gray-500">
                                        No licensed states found
                                      </div>
                                    )}
                                  </div>
                                </div>

                                {/* Supported States (disabled) */}
                                {selectedDoctor?.statePatientCounts &&
                                  selectedDoctor.statePatientCounts.filter(
                                    (state) => !state.enabled,
                                  ).length > 0 && (
                                    <div className="mb-5 text-stone">
                                      <div className="text-2xl font-medium">
                                        Disabled States
                                      </div>
                                      <div className="mb-5 text-sm">
                                        These states are supported by this
                                        doctor, but are not currently enabled.
                                      </div>
                                      <div className="flex flex-wrap gap-2">
                                        {selectedDoctor.statePatientCounts
                                          .filter((state) => !state.enabled)
                                          .sort((a, b) =>
                                            a.stateName.localeCompare(
                                              b.stateName,
                                            ),
                                          )
                                          .map((state) => (
                                            <div
                                              key={state.stateCode}
                                              className="inline-flex items-center rounded-full bg-gray-100 px-3 py-1 text-sm font-medium text-gray-800"
                                            >
                                              {state.stateName}
                                            </div>
                                          ))}
                                      </div>
                                    </div>
                                  )}
                              </TabsContent>
                              <TabsContent className="px-10" value="patients">
                                <div className="mb-5 mt-10 text-2xl font-medium text-denim">
                                  Patients by State
                                </div>
                                <p className="mb-3 text-sm text-gray-500">
                                  View patients from this doctor filtered by
                                  state.
                                </p>

                                <p className="mb-3 text-sm font-medium text-denim">
                                  Total Patients:{' '}
                                  {selectedDoctor?.totalPatientCount || 0}
                                </p>

                                {/* States List */}
                                <div>
                                  {selectedDoctor?.statePatientCounts &&
                                  selectedDoctor.statePatientCounts.length >
                                    0 ? (
                                    <ul className="space-y-3">
                                      {selectedDoctor.statePatientCounts
                                        .sort((a, b) =>
                                          a.stateName.localeCompare(
                                            b.stateName,
                                          ),
                                        )
                                        .map((state) => (
                                          <li
                                            key={state.stateCode}
                                            className="border-b pb-3"
                                          >
                                            <a
                                              href={`/patients/all?doctorId=${selectedDoctorId}&stateCode=${state.stateCode}`}
                                              className="flex items-center justify-between rounded-md p-2 hover:bg-gray-50"
                                            >
                                              <div className="flex items-center">
                                                <span className="text-sm font-medium">
                                                  {state.stateName}
                                                </span>
                                              </div>

                                              <div className="flex items-center gap-2">
                                                <span className="rounded-full bg-denim-light/20 px-2 py-1 text-xs font-medium text-denim">
                                                  {state.patientCount} patient
                                                  {state.patientCount !== 1
                                                    ? 's'
                                                    : ''}
                                                </span>
                                                <svg
                                                  xmlns="http://www.w3.org/2000/svg"
                                                  viewBox="0 0 20 20"
                                                  fill="currentColor"
                                                  className="h-5 w-5 text-denim"
                                                >
                                                  <path
                                                    fillRule="evenodd"
                                                    d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z"
                                                    clipRule="evenodd"
                                                  />
                                                </svg>
                                              </div>
                                            </a>
                                          </li>
                                        ))}
                                    </ul>
                                  ) : (
                                    <p className="text-sm text-gray-500">
                                      No states found for this doctor
                                    </p>
                                  )}
                                </div>
                              </TabsContent>
                            </Tabs>
                          )}
                        </div>
                      </>
                    )}
                  </div>
                )}
              </DrawerContent>
            </DrawerPortal>
          )}
        </Drawer>
      </div>

      <div className={cn('flex items-center justify-between py-4')}>
        <div className="flex gap-3">
          <Button
            onClick={() => {
              void setSelectedDoctorId('new');
              void setIsEditMode('true');
            }}
          >
            Add New Doctor
          </Button>
          <Button
            variant="electric"
            onClick={() => {
              setShowInactive(!showInactive);
              void setPagination({ page: 1 }); // Reset to first page when toggling
            }}
          >
            {showInactive ? 'Hide Inactive Doctors' : 'Show Inactive Doctors'}
          </Button>
        </div>
        {table.getRowModel().rows.length > 0 && (
          <Pagination
            currentPage={pagination.page}
            totalPages={table.getPageCount()}
            onPageChange={(page) => setPagination({ page })}
          />
        )}
      </div>

      {/* Transfer Patients Drawer */}
      {selectedDoctor && (
        <PatientTransferDrawer
          isOpen={transferDrawer.isOpen}
          onClose={() => transferDrawer.handleClose()}
          sourceDoctor={selectedDoctor}
          mode="transfer"
          existingTransfer={transferDrawer.activeBulkTransfer}
        />
      )}

      {/* Out of Office Drawer */}
      {selectedDoctor && (
        <PatientTransferDrawer
          isOpen={outOfOfficeDrawer.isOpen}
          onClose={() => outOfOfficeDrawer.handleClose()}
          sourceDoctor={selectedDoctor}
          mode="outOfOffice"
          existingTransfer={outOfOfficeDrawer.activeBulkTransfer}
        />
      )}

      <DoctorResetPasswordDrawer
        isOpen={doctorResetPasswordDrawer.isOpen}
        onClose={() => doctorResetPasswordDrawer.handleClose()}
        sourceDoctor={selectedDoctor}
      />
    </div>
  );
}
