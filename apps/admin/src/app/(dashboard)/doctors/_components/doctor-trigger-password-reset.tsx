'use client';

import { XIcon } from 'lucide-react';

import { But<PERSON> } from '@willow/ui/base/button';
import { toast } from '@willow/ui/base/use-toast';
import { Drawer, DrawerContent, DrawerOverlay } from '@willow/ui/base/vaul';
import { apiClient } from '@willow/utils/api/client';

interface DoctorResetPasswordDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  sourceDoctor: any;
}
export function DoctorResetPasswordDrawer({
  isOpen,
  onClose,
  sourceDoctor,
}: DoctorResetPasswordDrawerProps) {
  return (
    <Drawer
      open={isOpen}
      onOpenChange={onClose}
      direction="right"
      modal={true}
      dismissible={true}
    >
      <DrawerOverlay className="fixed inset-0 z-50 bg-black/25" />
      <DrawerContent className="fixed right-0 top-0 z-50 m-0 flex h-full w-[550px] max-w-full flex-col overflow-hidden border-l border-border bg-white p-0 shadow-lg">
        {/* Header */}
        <div className="flex h-[62px] shrink-0 items-center justify-between border-b px-6">
          <div>
            <h2 className="text-lg font-medium">Trigger Password Reset</h2>
            {sourceDoctor && (
              <p className="text-sm text-gray-500">
                for Dr. {sourceDoctor.user.firstName}{' '}
                {sourceDoctor.user.lastName}
              </p>
            )}
          </div>
          <XIcon size={24} className="cursor-pointer" onClick={onClose} />
        </div>

        <div className="mb-8 mt-10 flex justify-center">
          <span
            id="pass-reset-boton"
            className="text-xl font-medium leading-tight text-slate-600 underline md:text-denim md:no-underline"
          >
            <Button
              onClick={async () => {
                try {
                  await apiClient.post('/admin/doctors/forgot-password', {
                    email: sourceDoctor.user.email,
                  });
                  toast({
                    title: 'Password reset link sent',
                    description: `A password reset link has been sent to ${sourceDoctor.user.email}.`,
                    duration: 4000,
                    variant: 'default',
                  });
                } catch (e: any) {
                  toast({
                    title: 'Error during Password reset link sent',
                    description: `An error occurred while sending the password reset link to ${sourceDoctor.user.email}.`,
                    duration: 4000,
                    variant: 'default',
                  });
                }
              }}
            >
              Send reset password email
            </Button>
          </span>
        </div>
      </DrawerContent>
    </Drawer>
  );
}
