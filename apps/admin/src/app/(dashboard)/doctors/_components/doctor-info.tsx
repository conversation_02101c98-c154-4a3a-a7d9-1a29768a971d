'use client';

import { useEffect, useMemo, useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { toZonedTime } from 'date-fns-tz';
import { XIcon } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@willow/ui/base/button';
import { Checkbox } from '@willow/ui/base/checkbox';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@willow/ui/base/form';
import { Input } from '@willow/ui/base/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@willow/ui/base/select';
import { useToast } from '@willow/ui/base/use-toast';

import type { CreateDoctorDto, UpdateDoctorDto } from '~/hooks/doctors';
import { env } from '~/env';
import {
  useCreateDoctor,
  useGetDoctor,
  useUpdateDoctor,
} from '~/hooks/doctors';
import { useGetStates } from '~/hooks/states';
import { useDoctorForm } from '~/hooks/useDoctorForm';
import { DoctorProfilePictureField } from './doctor-profile-picture';

interface DoctorInfoProps {
  doctorId: string;
  handleClose: () => void;
}

// Function to convert from ISO format to MM/DD/YYYY
const formatDateForDisplay = (isoDate: string) => {
  try {
    // Convert the ISO string to a zoned time in UTC to ensure consistent display
    const utcDate = toZonedTime(new Date(isoDate), 'UTC');
    return format(utcDate, 'MM/dd/yyyy');
  } catch (_e) {
    return '';
  }
};
// Regular expression for MM/DD/YYYY format
const dateRegex = /^(0[1-9]|1[0-2])\/(0[1-9]|[12][0-9]|3[01])\/\d{4}$/;
// Validates if a date is in MM/DD/YYYY format and is a valid date
const isValidDateString = (value: string) => {
  if (!dateRegex.test(value)) return false;

  const parts = value.split('/');
  if (parts.length !== 3) return false;

  // Ensure parts are defined before parsing
  if (!parts[0] || !parts[1] || !parts[2]) return false;

  const month = parseInt(parts[0], 10);
  const day = parseInt(parts[1], 10);
  const year = parseInt(parts[2], 10);

  // Check if all parts are valid numbers
  if (isNaN(month) || isNaN(day) || isNaN(year)) return false;

  const date = new Date(year, month - 1, day);
  return (
    date.getFullYear() === year &&
    date.getMonth() === month - 1 &&
    date.getDate() === day
  );
};

// NPI validation function
const validateNpi = (npi: string): boolean => {
  if (!npi) return true; // Allow empty NPI
  if (!/^\d{10}$/.test(npi)) return false;

  const npiDigits = npi.slice(0, 9);

  // Make sure we can safely access the check digit
  if (npi.length < 10) return false;
  const providedCheckDigit = parseInt(npi.charAt(9), 10);
  if (isNaN(providedCheckDigit)) return false;

  let sum = 0;
  for (let i = 0; i < npiDigits.length; i++) {
    // Ensure we safely parse each digit
    const digitChar = npiDigits.charAt(i);
    const digit = parseInt(digitChar, 10);
    if (isNaN(digit)) return false;

    if (i % 2 === 0) {
      const doubled = digit * 2;
      sum += Math.floor(doubled / 10) + (doubled % 10);
    } else {
      sum += digit;
    }
  }

  sum += 24;

  const checkDigit = Math.ceil(sum / 10) * 10 - sum;
  return checkDigit === providedCheckDigit;
};
// Schema to validate date format
const dateStringSchema = z
  .string()
  .refine((val) => val === '' || isValidDateString(val), {
    message: 'Date must be in MM/DD/YYYY format and be a valid date',
  });
const createDoctorSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Invalid email address'),
  // Password is now optional as it will be generated if not provided
  dateOfBirth: dateStringSchema,
  states: z.array(z.string()).min(1, 'At least one state is required'),
  npiNumber: z
    .string()
    .optional()
    .refine((val) => !val || validateNpi(val), {
      message: 'Invalid NPI number format',
    }),
  primaryPhone: z.string().optional(),
  primaryFax: z.string().optional(),
  address1: z.string().min(1, 'Address line 1 is required'),
  address2: z.string().optional(),
  city: z.string().min(1, 'City is required'),
  state: z.string().min(1, 'State is required'),
  zip: z.string().min(5, 'Valid ZIP code is required'),
  doseSpotClinicianId: z.string().optional(),
  role: z.enum(['doctor', 'superDoctor']).default('doctor'),
});
const updateDoctorSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Invalid email address'),
  dateOfBirth: dateStringSchema,
  states: z.array(z.string()).min(1, 'At least one state is required'),
  npiNumber: z
    .string()
    .optional()
    .refine((val) => !val || validateNpi(val), {
      message: 'Invalid NPI number format',
    }),
  // Optional password field with strong password validation
  password: z
    .string()
    .optional() // Make password optional for updates
    .refine(
      (val) => {
        if (!val) return true; // Empty is valid (no password change)
        if (val.length < 10) return false; // At least 10 characters
        if (!/[a-z]/.test(val)) return false; // At least one lowercase letter
        if (!/[A-Z]/.test(val)) return false; // At least one uppercase letter
        if (!/\d/.test(val)) return false; // At least one number
        if (!/[^A-Za-z\d]/.test(val)) return false; // At least one special character
        return true;
      },
      {
        message:
          'Password must be at least 10 characters and include at least one uppercase letter, one lowercase letter, one number, and one special character',
      },
    ),
  primaryPhone: z.string().optional(),
  primaryFax: z.string().optional(),
  address1: z.string().min(1, 'Address line 1 is required'),
  address2: z.string().optional(),
  city: z.string().min(1, 'City is required'),
  state: z.string().min(1, 'State is required'),
  zip: z.string().min(5, 'Valid ZIP code is required'),
  doseSpotClinicianId: z.string().optional(),
  role: z.enum(['doctor', 'superDoctor']),
});

export function DoctorInfoEdit({ doctorId, handleClose }: DoctorInfoProps) {
  const isNewDoctor = doctorId === 'new';
  const {
    data: doctor,
    isLoading,
    refetch,
  } = useGetDoctor(isNewDoctor ? '' : doctorId);
  const { data: statesData, isLoading: statesLoading } = useGetStates();
  const { mutateAsync: createDoctor } = useCreateDoctor();
  const { mutateAsync: updateDoctor } = useUpdateDoctor();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { profilePictureRef } = useDoctorForm(doctorId);
  const [stateLicenses, setStateLicenses] = useState<
    Record<
      string,
      {
        licenseNumber?: string;
      }
    >
  >({});

  // Process the states data to create enabled and disabled groups
  const { enabledStates, disabledStates, allStates } = useMemo(() => {
    const allStates = statesData?.states || [];

    // Sort all states alphabetically by name first
    const sortedAllStates = [...allStates].sort((a, b) =>
      a.name.localeCompare(b.name),
    );

    // Split into enabled and disabled groups
    return {
      enabledStates: sortedAllStates.filter((state) => state.enabled),
      disabledStates: sortedAllStates.filter((state) => !state.enabled),
      allStates,
    };
  }, [statesData]);
  const form = useForm<CreateDoctorDto | UpdateDoctorDto>({
    resolver: zodResolver(
      isNewDoctor ? createDoctorSchema : updateDoctorSchema,
    ),
    defaultValues: isNewDoctor
      ? {
          firstName: '',
          lastName: '',
          email: '',
          // Password field removed as it's now generated automatically
          dateOfBirth: '', // Changed to empty string for new doctors
          states: [''],
          npiNumber: '',
          primaryPhone: '',
          primaryFax: '',
          address1: '',
          address2: '',
          city: '',
          state: '',
          zip: '',
          role: 'doctor',
        }
      : undefined,
  });

  useEffect(() => {
    if (doctor && !isNewDoctor && statesData?.states) {
      console.log('Doctor data for form:', doctor);
      // Extract all states the doctor prescribes in
      const prescribingStates: string[] = [];
      // Add primary state
      if (doctor.state?.code) {
        prescribingStates.push(doctor.state.code);
      }
      // Add additional states from prescribesIn
      if (doctor.prescribesIn && Array.isArray(doctor.prescribesIn)) {
        doctor.prescribesIn.forEach((relation) => {
          if (
            relation.state?.code &&
            !prescribingStates.includes(relation.state.code)
          ) {
            prescribingStates.push(relation.state.code);
          }
        });
      }

      // Extract license information for each state if available
      const licenses: Record<
        string,
        {
          licenseNumber?: string;
        }
      > = {};

      // Populate license information from API data
      if (doctor.prescribesIn && Array.isArray(doctor.prescribesIn)) {
        doctor.prescribesIn.forEach((relation) => {
          if (relation.state?.code) {
            licenses[relation.state.code] = {
              licenseNumber: relation.licenseNumber || '',
            };
          }
        });
      }

      // Set the license information to state
      setStateLicenses(licenses);

      // Format date of birth to MM/DD/YYYY if it exists
      const formattedDateOfBirth = doctor.dateOfBirth
        ? formatDateForDisplay(doctor.dateOfBirth)
        : '';

      // Make sure we have valid values for all form fields, especially selects
      const stateValue = doctor.state?.code || '';
      const roleValue = doctor.role || 'doctor';

      // Use setTimeout to allow the form to fully initialize before resetting
      setTimeout(() => {
        form.reset({
          firstName: doctor.user?.firstName || '',
          lastName: doctor.user?.lastName || '',
          email: doctor.user?.email || '',
          dateOfBirth: formattedDateOfBirth,
          address1: doctor.address1 || '',
          address2: doctor.address2 || '',
          city: doctor.city || '',
          state: stateValue,
          zip: doctor.zip || '',
          states: prescribingStates.length > 0 ? prescribingStates : [''],
          role: roleValue,
          // Additional DoseSpot fields
          npiNumber: doctor.npiNumber || '',
          primaryPhone: doctor.primaryPhone || '',
          primaryFax: doctor.primaryFax || '',
          // We don't set stateLicenses in the form, we handle it separately with our React state
        });
      }, 0);
    }
  }, [doctor, form, isNewDoctor, statesData]);

  // Convert MM/DD/YYYY format to ISO-8601 DateTime for API
  const convertDateFormat = (mmddyyyy: string) => {
    if (!mmddyyyy || !dateRegex.test(mmddyyyy)) return '';

    const parts = mmddyyyy.split('/');
    // Ensure we have all parts before trying to use them
    if (parts.length < 3) return '';

    const month = parts[0]?.padStart(2, '0') || '';
    const day = parts[1]?.padStart(2, '0') || '';
    const year = parts[2] || '';

    // Double-check that all parts exist
    if (!month || !day || !year) return '';

    // Create a date object with explicit UTC time to avoid timezone issues
    // Using UTC ensures the date is interpreted correctly regardless of user's timezone
    const date = new Date(
      Date.UTC(Number(year), Number(month) - 1, Number(day), 0, 0, 0),
    );
    if (isNaN(date.getTime())) return '';

    // Return full ISO string that Prisma can handle
    return date.toISOString();
  };

  // Handlers for state license information
  const handleLicenseNumberChange = (stateCode: string, value: string) => {
    setStateLicenses((prev) => ({
      ...prev,
      [stateCode]: {
        ...prev[stateCode],
        licenseNumber: value,
      },
    }));
  };

  const onSubmit = async (data: CreateDoctorDto | UpdateDoctorDto) => {
    setIsSubmitting(true);
    try {
      // Convert date format for API
      const formattedDateOfBirth = data.dateOfBirth
        ? convertDateFormat(data.dateOfBirth)
        : '';

      // Prepare state license information for submission
      const stateLicensesArray = Object.entries(stateLicenses)
        .filter(([stateCode]) => data.states?.includes(stateCode))
        .map(([stateCode, licenseInfo]) => ({
          stateCode,
          licenseNumber: licenseInfo.licenseNumber,
        }));

      // Upload profile picture if there is one
      let temporaryImageKey = null;
      console.log('DoctorInfoEdit: Attempting to upload profile picture');
      console.log(
        'DoctorInfoEdit: profilePictureRef.current exists:',
        !!profilePictureRef.current,
      );
      if (profilePictureRef.current) {
        console.log(
          'DoctorInfoEdit: Calling handleFormSubmit on profilePictureRef',
        );
        temporaryImageKey = await profilePictureRef.current.handleFormSubmit();
        console.log(
          'DoctorInfoEdit: Got temporaryImageKey:',
          temporaryImageKey,
        );
      }

      const submissionData = {
        ...data,
        dateOfBirth: formattedDateOfBirth,
        stateLicenses: stateLicensesArray,
        temporaryImageKey,
      };

      if (isNewDoctor) {
        await createDoctor(submissionData as CreateDoctorDto);
        toast({
          title: 'Success',
          description:
            'Doctor created successfully. An email with login credentials has been sent to the doctor.',
        });
        handleClose();
      } else {
        await updateDoctor({
          id: doctorId,
          doctorData: submissionData as UpdateDoctorDto,
        });

        // Explicitly refetch the doctor data to ensure we have the latest information
        const result = await refetch();

        if (result.data) {
          // Extract the doctor data from the refetch result
          const updatedDoctor = result.data;

          // Extract prescribing states after refetch
          const prescribingStates: string[] = [];
          if (updatedDoctor.state?.code) {
            prescribingStates.push(updatedDoctor.state.code);
          }
          if (
            updatedDoctor.prescribesIn &&
            Array.isArray(updatedDoctor.prescribesIn)
          ) {
            updatedDoctor.prescribesIn.forEach((relation) => {
              if (
                relation.state?.code &&
                !prescribingStates.includes(relation.state.code)
              ) {
                prescribingStates.push(relation.state.code);
              }
            });
          }

          // Extract and update license information
          const updatedLicenses: Record<
            string,
            {
              licenseNumber?: string;
            }
          > = {};

          if (
            updatedDoctor.prescribesIn &&
            Array.isArray(updatedDoctor.prescribesIn)
          ) {
            updatedDoctor.prescribesIn.forEach((relation) => {
              if (relation.state?.code) {
                updatedLicenses[relation.state.code] = {
                  licenseNumber: relation.licenseNumber || '',
                };
              }
            });
          }

          // Update the state licenses state
          setStateLicenses(updatedLicenses);

          // Format date of birth
          const formattedDateOfBirth = updatedDoctor.dateOfBirth
            ? formatDateForDisplay(updatedDoctor.dateOfBirth)
            : '';

          console.log('After update, resetting form with:', {
            state: updatedDoctor.state?.code,
            role: updatedDoctor.role,
          });

          // Reset the form with the freshly fetched values
          form.reset({
            firstName: updatedDoctor.user?.firstName || '',
            lastName: updatedDoctor.user?.lastName || '',
            email: updatedDoctor.user?.email || '',
            dateOfBirth: formattedDateOfBirth,
            address1: updatedDoctor.address1 || '',
            address2: updatedDoctor.address2 || '',
            city: updatedDoctor.city || '',
            state: updatedDoctor.state?.code || '',
            zip: updatedDoctor.zip || '',
            states: prescribingStates.length > 0 ? prescribingStates : [''],
            role: updatedDoctor.role || 'doctor',
            npiNumber: updatedDoctor.npiNumber || '',
            primaryPhone: updatedDoctor.primaryPhone || '',
            primaryFax: updatedDoctor.primaryFax || '',
          });
        }

        toast({
          title: 'Success',
          description: 'Doctor updated successfully',
        });
        handleClose(); // Close the edit mode instead of switching to view mode
      }
    } catch (error) {
      console.error('Error saving doctor:', error);

      // Extract error message from API response
      let errorMessage = 'Failed to save doctor';
      if (error && typeof error === 'object' && 'response' in error) {
        const apiError = error as {
          response?: { data?: { message?: string } };
        };
        if (apiError.response?.data?.message) {
          errorMessage = apiError.response.data.message;
        }
      } else if (error && typeof error === 'object' && 'message' in error) {
        const genericError = error as { message: string };
        errorMessage = genericError.message;
      }

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  if (isLoading && !isNewDoctor) {
    return (
      <div className="flex h-full w-full items-center justify-center bg-white">
        <div>Loading...</div>
      </div>
    );
  }

  // For new doctor, use the split layout drawer like admin form
  if (isNewDoctor) {
    return (
      <div className="relative flex h-full w-full grow bg-white">
        {/* Left sidebar */}
        <div className="flex w-[50px] flex-col bg-stone-light">
          {/* Empty sidebar */}
        </div>

        {/* Right content area with form */}
        <div className="flex w-[calc(100%-50px)] flex-col overflow-auto">
          <div className="sticky top-0 z-10 bg-white px-10 pt-10">
            <div className="mb-6 flex flex-row justify-between">
              <h2 className="text-xl font-semibold text-denim">
                Add New Doctor
              </h2>
              <XIcon
                size={24}
                className="cursor-pointer text-denim"
                onClick={handleClose}
              />
            </div>
          </div>

          <div className="px-10 pb-10">
            <Form {...form} key={`doctor-form-new`}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-5"
              >
                <FormField
                  control={form.control}
                  name="role"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Doctor Type</FormLabel>
                      <Select
                        onValueChange={(value) => {
                          console.log('Role select changed to:', value);
                          field.onChange(value);
                        }}
                        value={field.value}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger
                            variant={'outline'}
                            size={'small'}
                            className="h-8 w-full items-center justify-between rounded-md border py-2 pl-3 text-sm text-black"
                          >
                            <SelectValue placeholder="Select doctor type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="doctor" className="text-sm">
                            Regular Doctor
                          </SelectItem>
                          <SelectItem value="superDoctor" className="text-sm">
                            Super Doctor
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {/* Profile Picture */}
                <FormItem>
                  <FormLabel>Profile Picture</FormLabel>
                  <FormControl>
                    <DoctorProfilePictureField
                      ref={profilePictureRef}
                      name="profilePicture"
                      initialImageUrl={
                        doctor?.image
                          ? `${env.NEXT_PUBLIC_API_S3_URL}/${doctor.image}`
                          : undefined
                      }
                    />
                  </FormControl>
                </FormItem>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="firstName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>First Name</FormLabel>
                        <FormControl>
                          <Input size="sm" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="lastName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Last Name</FormLabel>
                        <FormControl>
                          <Input size="sm" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input size="sm" {...field} type="email" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  {/* Date of Birth moved next to email */}
                  <FormField
                    control={form.control}
                    name="dateOfBirth"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Date of Birth (MM/DD/YYYY)</FormLabel>
                        <FormControl>
                          <Input
                            size="sm"
                            {...field}
                            placeholder="MM/DD/YYYY"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                {/* License Information section moved to the bottom */}
                {/* Professional Information */}
                <div className="grid grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="npiNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>NPI Number</FormLabel>
                        <FormControl>
                          <Input
                            size="sm"
                            {...field}
                            placeholder="Enter 10-digit NPI"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="primaryPhone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Primary Phone</FormLabel>
                        <FormControl>
                          <Input
                            size="sm"
                            {...field}
                            type="tel"
                            placeholder="(XXX) XXX-XXXX"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="primaryFax"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Primary Fax</FormLabel>
                        <FormControl>
                          <Input
                            size="sm"
                            {...field}
                            placeholder="(XXX) XXX-XXXX"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                {/* Address Information */}
                <h3 className="mb-2 mt-4 text-base font-medium text-denim">
                  Address Information
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="address1"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Address Line 1</FormLabel>
                        <FormControl>
                          <Input
                            size="sm"
                            {...field}
                            placeholder="Street address"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="address2"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Address Line 2</FormLabel>
                        <FormControl>
                          <Input size="sm" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="grid grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="city"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>City</FormLabel>
                        <FormControl>
                          <Input size="sm" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="state"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>State</FormLabel>
                        <Select
                          onValueChange={(value) => {
                            console.log('State select changed to:', value);
                            field.onChange(value);
                          }}
                          value={field.value}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger
                              variant={'outline'}
                              size={'small'}
                              className="h-8 w-full items-center justify-between rounded-md border py-2 pl-3 text-sm text-black"
                            >
                              <SelectValue placeholder="State" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {allStates.map((state) => (
                              <SelectItem
                                key={state.code}
                                value={state.code}
                                className="text-sm"
                              >
                                {state.name} ({state.code})
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="zip"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>ZIP Code</FormLabel>
                        <FormControl>
                          <Input size="sm" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                {/* Role field moved to the top */}
                {/* License Information - Moved to the bottom of the form */}
                <h3 className="mb-2 mt-4 text-base font-medium text-denim">
                  License Information
                </h3>
                <div className="mb-4">
                  <FormLabel className="text-base">Licensed States</FormLabel>
                  <div className="mt-2 space-y-4">
                    {statesLoading ? (
                      <div className="text-sm text-gray-500">
                        Loading states...
                      </div>
                    ) : (
                      <>
                        {/* Enabled states section */}
                        {enabledStates.length > 0 && (
                          <div>
                            <h4 className="mb-2 text-sm font-medium text-green-600">
                              Enabled States
                            </h4>
                            <div className="grid grid-cols-3 gap-2">
                              {enabledStates.map((state) => (
                                <FormField
                                  key={state.code}
                                  control={form.control}
                                  name="states"
                                  render={({ field }) => {
                                    const stateSelected =
                                      field.value?.includes(state.code) ||
                                      false;
                                    return (
                                      <FormItem
                                        key={state.code}
                                        className="flex flex-row items-start space-x-2 space-y-0"
                                      >
                                        <FormControl>
                                          <Checkbox
                                            className="border-denim"
                                            checked={stateSelected}
                                            onCheckedChange={(checked) => {
                                              // Create a copy of the current array
                                              const updatedValue = [
                                                ...(field.value || []),
                                              ];
                                              if (checked) {
                                                // Add if not already included
                                                if (
                                                  !updatedValue.includes(
                                                    state.code,
                                                  )
                                                ) {
                                                  updatedValue.push(state.code);
                                                }

                                                // Initialize state license info if not exists
                                                if (
                                                  !stateLicenses[state.code]
                                                ) {
                                                  setStateLicenses((prev) => ({
                                                    ...prev,
                                                    [state.code]: {
                                                      licenseNumber: '',
                                                      licenseExpirationDate: '',
                                                      empowerEnabled: false,
                                                    },
                                                  }));
                                                }
                                              } else {
                                                // Remove if included
                                                const index =
                                                  updatedValue.indexOf(
                                                    state.code,
                                                  );
                                                if (index !== -1) {
                                                  updatedValue.splice(index, 1);
                                                }
                                                // We keep the license info in the stateLicenses state
                                                // so it will reappear if the state is checked again
                                              }
                                              field.onChange(updatedValue);
                                            }}
                                          />
                                        </FormControl>
                                        <FormLabel className="text-sm font-normal">
                                          {state.name} ({state.code})
                                        </FormLabel>
                                      </FormItem>
                                    );
                                  }}
                                />
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Disabled states section */}
                        {disabledStates.length > 0 && (
                          <div className="mt-4">
                            <h4 className="mb-2 text-sm font-medium text-gray-500">
                              Disabled States
                            </h4>
                            <div className="grid grid-cols-3 gap-2">
                              {disabledStates.map((state) => (
                                <FormField
                                  key={state.code}
                                  control={form.control}
                                  name="states"
                                  render={({ field }) => {
                                    const stateSelected =
                                      field.value?.includes(state.code) ||
                                      false;
                                    return (
                                      <FormItem
                                        key={state.code}
                                        className="flex flex-row items-start space-x-2 space-y-0"
                                      >
                                        <FormControl>
                                          <Checkbox
                                            className="border-gray-400"
                                            checked={stateSelected}
                                            onCheckedChange={(checked) => {
                                              // Create a copy of the current array
                                              const updatedValue = [
                                                ...(field.value || []),
                                              ];
                                              if (checked) {
                                                // Add if not already included
                                                if (
                                                  !updatedValue.includes(
                                                    state.code,
                                                  )
                                                ) {
                                                  updatedValue.push(state.code);
                                                }

                                                // Initialize state license info if not exists
                                                if (
                                                  !stateLicenses[state.code]
                                                ) {
                                                  setStateLicenses((prev) => ({
                                                    ...prev,
                                                    [state.code]: {
                                                      licenseNumber: '',
                                                    },
                                                  }));
                                                }
                                              } else {
                                                // Remove if included
                                                const index =
                                                  updatedValue.indexOf(
                                                    state.code,
                                                  );
                                                if (index !== -1) {
                                                  updatedValue.splice(index, 1);
                                                }
                                                // We keep the license info in the stateLicenses state
                                                // so it will reappear if the state is checked again
                                              }
                                              field.onChange(updatedValue);
                                            }}
                                          />
                                        </FormControl>
                                        <FormLabel className="text-sm font-normal text-gray-500">
                                          {state.name} ({state.code})
                                        </FormLabel>
                                      </FormItem>
                                    );
                                  }}
                                />
                              ))}
                            </div>
                          </div>
                        )}

                        {enabledStates.length === 0 &&
                          disabledStates.length === 0 && (
                            <div className="text-sm text-gray-500">
                              No states available
                            </div>
                          )}
                      </>
                    )}
                    <FormMessage />
                  </div>
                </div>

                {/* State License Details */}
                {(form.watch('states') ?? []).filter(Boolean).length > 0 && (
                  <div className="mt-4 rounded-md border border-gray-200 p-4">
                    <h4 className="mb-4 font-medium text-denim">
                      State License Details
                    </h4>
                    <div className="space-y-4">
                      {form
                        .watch('states')
                        ?.filter(Boolean)
                        .sort((a, b) => {
                          // Get state info for both codes for alphabetical sorting
                          const stateA = enabledStates
                            .concat(disabledStates)
                            .find((s) => s.code === a);
                          const stateB = enabledStates
                            .concat(disabledStates)
                            .find((s) => s.code === b);
                          return (
                            stateA?.name.localeCompare(stateB?.name || '') || 0
                          );
                        })
                        .map((stateCode) => {
                          return (
                            <div
                              key={`license-${stateCode}`}
                              className="rounded-md border border-gray-100 bg-gray-50 p-3"
                            >
                              <div className="flex items-center gap-3">
                                <FormLabel
                                  htmlFor={`license-number-${stateCode}`}
                                  className="whitespace-nowrap font-medium"
                                >
                                  License Number for {stateCode}:
                                </FormLabel>
                                <Input
                                  id={`license-number-${stateCode}`}
                                  size="sm"
                                  value={
                                    stateLicenses[stateCode]?.licenseNumber ||
                                    ''
                                  }
                                  onChange={(e) =>
                                    handleLicenseNumberChange(
                                      stateCode,
                                      e.target.value,
                                    )
                                  }
                                  placeholder="Enter license number"
                                  className="flex-1"
                                />
                              </div>
                            </div>
                          );
                        })}
                    </div>
                  </div>
                )}

                <div className="flex justify-between pt-4">
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="tertiary"
                      onClick={handleClose}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting ? 'Saving...' : 'Create Doctor'}
                    </Button>
                  </div>
                </div>
              </form>
            </Form>
          </div>
        </div>
      </div>
    );
  }

  // For existing doctor edits, just render the form without all the view details
  return (
    <div className="w-full px-10 py-6">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-denim">Edit Doctor</h2>
      </div>
      <Form {...form} key={`doctor-form-${doctor?.id}`}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
          <FormField
            control={form.control}
            name="role"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Doctor Type</FormLabel>
                <Select
                  onValueChange={(value) => {
                    console.log('Role select changed to:', value);
                    field.onChange(value);
                  }}
                  value={field.value}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger
                      variant={'outline'}
                      className="h-8 w-full items-center justify-between rounded-md border py-2 pl-3 text-sm text-black"
                    >
                      <SelectValue placeholder="Select doctor type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="doctor" className="text-sm">
                      Regular Doctor
                    </SelectItem>
                    <SelectItem value="superDoctor" className="text-sm">
                      Super Doctor
                    </SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          {/* Profile Picture */}
          <FormItem>
            <FormLabel>Profile Picture</FormLabel>
            <FormControl>
              <DoctorProfilePictureField
                ref={profilePictureRef}
                name="profilePicture"
                initialImageUrl={
                  doctor?.image
                    ? `${env.NEXT_PUBLIC_API_S3_URL}/${doctor.image}`
                    : undefined
                }
              />
            </FormControl>
          </FormItem>

          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>First Name</FormLabel>
                  <FormControl>
                    <Input size="sm" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Last Name</FormLabel>
                  <FormControl>
                    <Input size="sm" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          {/* Email and Date of Birth */}
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input size="sm" {...field} type="email" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="dateOfBirth"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Date of Birth (MM/DD/YYYY)</FormLabel>
                  <FormControl>
                    <Input size="sm" {...field} placeholder="MM/DD/YYYY" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Password field for edit mode */}
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Password</FormLabel>
                <FormControl>
                  <Input
                    size="sm"
                    {...field}
                    type="password"
                    placeholder="Leave blank to keep current password"
                  />
                </FormControl>
                <FormMessage />
                <p className="text-xs text-gray-500">
                  Password must be at least 10 characters and include uppercase,
                  lowercase, numbers, and symbols.
                </p>
              </FormItem>
            )}
          />
          {/* License Information - Moved to bottom */}
          {/* Professional Information */}
          <div className="grid grid-cols-3 gap-4">
            <FormField
              control={form.control}
              name="npiNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>NPI Number</FormLabel>
                  <FormControl>
                    <Input
                      size="sm"
                      {...field}
                      placeholder="Enter 10-digit NPI"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="primaryPhone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Primary Phone</FormLabel>
                  <FormControl>
                    <Input
                      size="sm"
                      {...field}
                      type="tel"
                      placeholder="(XXX) XXX-XXXX"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="primaryFax"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Primary Fax</FormLabel>
                  <FormControl>
                    <Input size="sm" {...field} placeholder="(XXX) XXX-XXXX" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          {/* Address Information */}
          <h3 className="mb-2 mt-4 text-base font-medium text-denim">
            Address Information
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="address1"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address Line 1</FormLabel>
                  <FormControl>
                    <Input size="sm" {...field} placeholder="Street address" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="address2"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address Line 2</FormLabel>
                  <FormControl>
                    <Input size="sm" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div className="grid grid-cols-3 gap-4">
            <FormField
              control={form.control}
              name="city"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>City</FormLabel>
                  <FormControl>
                    <Input size="sm" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="state"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>State</FormLabel>
                  <Select
                    onValueChange={(value) => {
                      console.log('State select changed to:', value);
                      field.onChange(value);
                    }}
                    value={field.value}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger
                        variant={'outline'}
                        size={'small'}
                        className="h-8 w-full items-center justify-between rounded-md border py-2 pl-3 text-sm text-black"
                      >
                        <SelectValue placeholder="State" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {allStates.map((state) => (
                        <SelectItem
                          key={state.code}
                          value={state.code}
                          className="text-sm"
                        >
                          {state.name} ({state.code})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="zip"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>ZIP Code</FormLabel>
                  <FormControl>
                    <Input size="sm" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          {/* Role field moved to the top */}
          {/* License Information - Moved to the bottom of the form */}
          <h3 className="mb-2 mt-4 text-base font-medium text-denim">
            License Information
          </h3>
          <div className="mb-4">
            <FormLabel className="text-base">Licensed States</FormLabel>
            <div className="mt-2 space-y-4">
              {statesLoading ? (
                <div className="text-sm text-gray-500">Loading states...</div>
              ) : (
                <>
                  {/* Enabled states section */}
                  {enabledStates.length > 0 && (
                    <div>
                      <h4 className="mb-2 text-sm font-medium text-green-600">
                        Enabled States
                      </h4>
                      <div className="grid grid-cols-3 gap-2">
                        {enabledStates.map((state) => (
                          <FormField
                            key={state.code}
                            control={form.control}
                            name="states"
                            render={({ field }) => {
                              const stateSelected =
                                field.value?.includes(state.code) || false;
                              return (
                                <FormItem
                                  key={state.code}
                                  className="flex flex-row items-start space-x-2 space-y-0"
                                >
                                  <FormControl>
                                    <Checkbox
                                      className="border-denim"
                                      checked={stateSelected}
                                      onCheckedChange={(checked) => {
                                        // Create a copy of the current array
                                        const updatedValue = [
                                          ...(field.value || []),
                                        ];
                                        if (checked) {
                                          // Add if not already included
                                          if (
                                            !updatedValue.includes(state.code)
                                          ) {
                                            updatedValue.push(state.code);
                                          }

                                          // Initialize state license info if not exists
                                          if (!stateLicenses[state.code]) {
                                            setStateLicenses((prev) => ({
                                              ...prev,
                                              [state.code]: {
                                                licenseNumber: '',
                                              },
                                            }));
                                          }
                                        } else {
                                          // Remove if included
                                          const index = updatedValue.indexOf(
                                            state.code,
                                          );
                                          if (index !== -1) {
                                            updatedValue.splice(index, 1);
                                          }
                                          // We keep the license info in the stateLicenses state
                                          // so it will reappear if the state is checked again
                                        }
                                        field.onChange(updatedValue);
                                      }}
                                    />
                                  </FormControl>
                                  <FormLabel className="text-sm font-normal">
                                    {state.name} ({state.code})
                                  </FormLabel>
                                </FormItem>
                              );
                            }}
                          />
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Disabled states section */}
                  {disabledStates.length > 0 && (
                    <div className="mt-4">
                      <h4 className="mb-2 text-sm font-medium text-gray-500">
                        Disabled States
                      </h4>
                      <div className="grid grid-cols-3 gap-2">
                        {disabledStates.map((state) => (
                          <FormField
                            key={state.code}
                            control={form.control}
                            name="states"
                            render={({ field }) => {
                              const stateSelected =
                                field.value?.includes(state.code) || false;
                              return (
                                <FormItem
                                  key={state.code}
                                  className="flex flex-row items-start space-x-2 space-y-0"
                                >
                                  <FormControl>
                                    <Checkbox
                                      className="border-gray-400"
                                      checked={stateSelected}
                                      onCheckedChange={(checked) => {
                                        // Create a copy of the current array
                                        const updatedValue = [
                                          ...(field.value || []),
                                        ];
                                        if (checked) {
                                          // Add if not already included
                                          if (
                                            !updatedValue.includes(state.code)
                                          ) {
                                            updatedValue.push(state.code);
                                          }

                                          // Initialize state license info if not exists
                                          if (!stateLicenses[state.code]) {
                                            setStateLicenses((prev) => ({
                                              ...prev,
                                              [state.code]: {
                                                licenseNumber: '',
                                              },
                                            }));
                                          }
                                        } else {
                                          // Remove if included
                                          const index = updatedValue.indexOf(
                                            state.code,
                                          );
                                          if (index !== -1) {
                                            updatedValue.splice(index, 1);
                                          }
                                          // We keep the license info in the stateLicenses state
                                          // so it will reappear if the state is checked again
                                        }
                                        field.onChange(updatedValue);
                                      }}
                                    />
                                  </FormControl>
                                  <FormLabel className="text-sm font-normal text-gray-500">
                                    {state.name} ({state.code})
                                  </FormLabel>
                                </FormItem>
                              );
                            }}
                          />
                        ))}
                      </div>
                    </div>
                  )}

                  {enabledStates.length === 0 &&
                    disabledStates.length === 0 && (
                      <div className="text-sm text-gray-500">
                        No states available
                      </div>
                    )}
                </>
              )}
              <FormMessage />
            </div>
          </div>

          {/* State License Details */}
          {(form.watch('states') ?? []).filter(Boolean).length > 0 && (
            <div className="mt-4 rounded-md border border-gray-200 p-4">
              <h4 className="mb-4 font-medium text-denim">
                State License Details
              </h4>
              <div className="space-y-4">
                {form
                  .watch('states')
                  ?.filter(Boolean)
                  .sort((a, b) => {
                    // Get state info for both codes for alphabetical sorting
                    const stateA = enabledStates
                      .concat(disabledStates)
                      .find((s) => s.code === a);
                    const stateB = enabledStates
                      .concat(disabledStates)
                      .find((s) => s.code === b);
                    return stateA?.name.localeCompare(stateB?.name || '') || 0;
                  })
                  .map((stateCode) => {
                    return (
                      <div
                        key={`license-${stateCode}`}
                        className="rounded-md border border-gray-100 bg-gray-50 p-3"
                      >
                        <div className="flex items-center gap-3">
                          <FormLabel
                            htmlFor={`license-number-${stateCode}`}
                            className="whitespace-nowrap font-medium"
                          >
                            License Number for {stateCode}:
                          </FormLabel>
                          <Input
                            id={`license-number-${stateCode}`}
                            size="sm"
                            value={
                              stateLicenses[stateCode]?.licenseNumber || ''
                            }
                            onChange={(e) =>
                              handleLicenseNumberChange(
                                stateCode,
                                e.target.value,
                              )
                            }
                            placeholder="Enter license number"
                            className="flex-1"
                          />
                        </div>
                      </div>
                    );
                  })}
              </div>
            </div>
          )}

          <div className="flex justify-between pt-4">
            <div className="flex gap-2">
              <Button
                type="button"
                variant="tertiary"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Saving...' : 'Update Doctor'}
              </Button>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
}
