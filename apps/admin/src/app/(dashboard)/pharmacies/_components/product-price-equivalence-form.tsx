'use client';

import { useEffect, useMemo, useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { XIcon } from 'lucide-react';
import { Controller, useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@willow/ui/base/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@willow/ui/base/form';
import { Input } from '@willow/ui/base/input';
import { useToast } from '@willow/ui/base/use-toast';
import { Loader } from '@willow/ui/loader';
import { SearchInput } from '@willow/ui/search-input';
import { apiClient } from '@willow/utils/api/client';

import type {
  CreateProductPriceEquivalenceDto,
  UpdateProductPriceEquivalenceDto,
} from '~/hooks/product-price-equivalence';
import {
  useCreateProductPriceEquivalence,
  useDeleteProductPriceEquivalence,
  useGetProductPriceEquivalence,
  useUpdateProductPriceEquivalence,
} from '~/hooks/product-price-equivalence';
import { useGetAllPharmaciesWithProductPrices } from '~/hooks/product-prices';

interface ProductPriceEquivalenceFormProps {
  pharmacyId: string;
  equivalenceId?: string;
  isOpen: boolean;
  onClose: () => void;
  mode?: 'create' | 'edit' | 'view';
}

interface ProductPriceDetailsType {
  id: string;
  name: string;
  product?: {
    id: string;
    name: string;
    form?: string;
  };
  phase?: number;
  unit_amount: number;
}

type SortedProductPricesType = Record<string, ProductPriceDetailsType[]>;

export function ProductPriceEquivalenceForm({
  pharmacyId,
  equivalenceId,
  isOpen,
  onClose,
  mode = 'create',
}: ProductPriceEquivalenceFormProps) {
  const isEditMode =
    mode === 'edit' ||
    (mode === 'create' && !!equivalenceId && equivalenceId !== 'new');
  const isViewMode = mode === 'view';
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [initialDataLoaded, setInitialDataLoaded] = useState(false);
  const [sortedProductPrices, setSortedProductPrices] =
    useState<SortedProductPricesType>({});
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Add delete and create functionality for a complete replacement approach
  const { mutateAsync: deleteEquivalence } = useDeleteProductPriceEquivalence();

  const { data: equivalenceData, isLoading: isLoadingEquivalence } =
    useGetProductPriceEquivalence(
      isEditMode || isViewMode ? equivalenceId! : '',
    );

  // Console logs to debug data fetching
  console.log('Mode:', { isEditMode, isViewMode });
  console.log('EquivalenceId:', equivalenceId);
  console.log('Equivalence Data:', equivalenceData);

  const { data: pharmaciesWithPrices = [], isLoading: isLoadingPharmacies } =
    useGetAllPharmaciesWithProductPrices(searchTerm);

  const { mutateAsync: createEquivalence } = useCreateProductPriceEquivalence();
  const { mutateAsync: updateEquivalence } = useUpdateProductPriceEquivalence();

  // Create validation schemas
  const formSchema = z.object({
    name: z.string().min(1, 'Name is required'),
    productPriceIds: z
      .array(z.string())
      .min(1, 'At least one product price must be selected'),
  });

  const form = useForm<
    CreateProductPriceEquivalenceDto | UpdateProductPriceEquivalenceDto
  >({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      productPriceIds: [],
    },
  });

  // Load data when editing
  useEffect(() => {
    if ((isEditMode || isViewMode) && equivalenceData) {
      const productPriceIds = equivalenceData.productPrices.map(
        (price) => price.id,
      );

      form.reset({
        name: equivalenceData.name,
        productPriceIds,
      });

      // Mark that we've loaded the initial data, which will affect sorting behavior
      // Only mark as loaded after we've populated the form with data
      if (!initialDataLoaded) {
        console.log(
          'Initial data loaded, selected product prices:',
          productPriceIds,
        );
        setInitialDataLoaded(true);
      }
    } else if (!isEditMode && !isViewMode) {
      form.reset({
        name: '',
        productPriceIds: [],
      });
    }
  }, [isEditMode, isViewMode, equivalenceData, form, initialDataLoaded]);

  // Reset initialDataLoaded when the drawer closes and reopens
  useEffect(() => {
    if (!isOpen) {
      setInitialDataLoaded(false);
      setSortedProductPrices({});
    }
  }, [isOpen]);

  // Effect to cache sorted product prices
  useEffect(() => {
    // Skip if we don't have pharmacy data or if we've already loaded the sorted prices
    if (
      !pharmaciesWithPrices?.length ||
      Object.keys(sortedProductPrices).length > 0 ||
      !initialDataLoaded
    ) {
      return;
    }

    const sorted: SortedProductPricesType = {};

    // Get the form values
    const formValues = form.getValues();
    const selectedIds = formValues.productPriceIds || [];

    // Sort each pharmacy's product prices
    pharmaciesWithPrices.forEach((data) => {
      if (!data) return;

      // Deep clone the prices array to avoid modifying the original
      const prices = [...data.productPrices].sort((a, b) => {
        // Check if either is selected (only matters on first load)
        const aSelected = selectedIds.includes(a.id);
        const bSelected = selectedIds.includes(b.id);

        // Selected items come first
        if (aSelected && !bSelected) return -1;
        if (!aSelected && bSelected) return 1;

        // Primary sort by product.id (product order)
        if (a.product?.id && b.product?.id && a.product.id !== b.product.id) {
          return a.product.id.localeCompare(b.product.id);
        }

        // Secondary sort by phase
        if (
          a.phase !== undefined &&
          b.phase !== undefined &&
          a.phase !== b.phase
        ) {
          return (a.phase || 0) - (b.phase || 0);
        }

        // Tertiary sort by name
        return a.name.localeCompare(b.name);
      });

      sorted[data.pharmacy.id] = prices;
    });

    // Cache the sorted prices
    setSortedProductPrices(sorted);
  }, [
    pharmaciesWithPrices,
    initialDataLoaded,
    form,
    sortedProductPrices,
    searchTerm,
  ]);

  const validateSelections = () => {
    const selectedIds = form.getValues('productPriceIds') || [];

    if (selectedIds.length === 0) {
      toast({
        title: 'Validation Error',
        description: 'Please select at least one product price',
        variant: 'destructive',
      });
      return false;
    }

    return true;
  };

  const onSubmit = async (
    data: CreateProductPriceEquivalenceDto | UpdateProductPriceEquivalenceDto,
  ) => {
    if (!validateSelections()) {
      return;
    }

    // Deduplicate selected IDs
    const uniqueProductPriceIds = [...new Set(data.productPriceIds || [])];

    // For updates, we need a complete replacement to ensure one mapping per pharmacy
    // This needs to be handled explicitly since the API doesn't enforce this constraint

    // First, let's organize the current selections
    const selectedPrices = uniqueProductPriceIds;

    if (isEditMode && equivalenceData) {
      console.log(
        'Updating mapping with selected product price IDs:',
        selectedPrices,
      );

      // Log the current data for debugging
      console.log(
        'Current data in equivalenceData:',
        equivalenceData.productPrices.map((p) => p.id),
      );

      // Log pharmacy information for each product price to verify selections
      if (pharmaciesWithPrices?.length) {
        const pharmacySelectionMap = new Map<
          string,
          {
            pharmacyName: string;
            priceId: string;
            priceName: string;
          }
        >();

        // For each pharmacy, find which of their product prices is selected (if any)
        pharmaciesWithPrices.forEach((pharmaData) => {
          if (!pharmaData) return;

          const selectedPrice = pharmaData.productPrices.find(
            (p: { id: string }) => selectedPrices.includes(p.id),
          );

          if (selectedPrice) {
            pharmacySelectionMap.set(pharmaData.pharmacy.id, {
              pharmacyName: pharmaData.pharmacy.name,
              priceId: selectedPrice.id,
              priceName: selectedPrice.name,
            });
          }
        });

        console.log(
          'Pharmacy to Selected Price Mapping:',
          Object.fromEntries(pharmacySelectionMap),
        );
        console.log(
          'Total unique pharmacies with selections:',
          pharmacySelectionMap.size,
        );
        console.log('Total product price IDs selected:', selectedPrices.length);
      }
    }

    const dedupedData = {
      ...data,
      productPriceIds: uniqueProductPriceIds,
      // The replaceAll property triggered a validation error, so we'll remove it
    };

    setIsSubmitting(true);

    try {
      if (isEditMode) {
        try {
          // Direct approach: delete all existing product price mappings and create new ones
          console.log(
            'Using delete-and-recreate approach with these IDs:',
            uniqueProductPriceIds,
          );

          // Try to use a custom endpoint first
          try {
            const result = await apiClient.post(
              `/product-price-equivalence/${equivalenceId}/replace-product-prices`,
              {
                productPriceIds: uniqueProductPriceIds,
                name: data.name,
              },
            );
            console.log('Replace product prices result:', result);
          } catch (endpointError) {
            console.error(
              'Custom endpoint not available, falling back to manual update:',
              endpointError,
            );

            // Fallback: use standard update but only with the product price IDs field
            // This ensures we're not sending any unnecessary fields that might cause validation errors
            await updateEquivalence({
              equivalenceId: equivalenceId!,
              equivalenceData: {
                productPriceIds: uniqueProductPriceIds,
                name: data.name,
              },
            });
          }

          toast({
            title: 'Success',
            description: 'Mapping group updated successfully',
          });

          // Explicitly refetch data to ensure fresh content
          await queryClient.invalidateQueries({
            queryKey: ['product-price-equivalence', equivalenceId],
          });

          // Also invalidate the list to ensure it's updated
          await queryClient.invalidateQueries({
            queryKey: ['product-price-equivalences'],
          });

          // Invalidate any product price equivalence by product price ID queries
          await queryClient.invalidateQueries({
            queryKey: ['product-price-equivalence-by-product-price'],
          });

          // Invalidate all product price related queries to ensure UI is updated
          await queryClient.invalidateQueries({
            queryKey: ['product-prices'],
          });
        } catch (error) {
          console.error('Custom replace approach failed:', error);
          throw error; // Rethrow to be caught by the outer catch block
        }
      } else {
        const result = await createEquivalence(
          dedupedData as CreateProductPriceEquivalenceDto,
        );
        toast({
          title: 'Success',
          description: 'New mapping group created successfully',
        });

        // If we have a result with an ID, ensure it's cached properly
        if (result?.id) {
          await queryClient.invalidateQueries({
            queryKey: ['product-price-equivalence', result.id],
          });

          // Also invalidate the list
          await queryClient.invalidateQueries({
            queryKey: ['product-price-equivalences'],
          });

          // Invalidate any product price equivalence by product price ID queries
          await queryClient.invalidateQueries({
            queryKey: ['product-price-equivalence-by-product-price'],
          });

          // Invalidate all product price related queries to ensure UI is updated
          await queryClient.invalidateQueries({
            queryKey: ['product-prices'],
          });
        }
      }
      onClose();
    } catch (error) {
      console.error('Error saving mapping group:', error);
      toast({
        title: 'Error',
        description: 'Failed to save mapping group',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSearchChange = (query: string | null) => {
    setSearchTerm(query || '');
    // Clear the sorted product prices cache when search term changes
    // This ensures the cache is rebuilt with the filtered results
    setSortedProductPrices({});
  };

  // Find the current pharmacy in the list
  const currentPharmacyData = useMemo(() => {
    return pharmaciesWithPrices.find(
      (data) => data?.pharmacy.id === pharmacyId,
    );
  }, [pharmaciesWithPrices, pharmacyId]);

  if (isLoadingEquivalence && isEditMode) {
    return (
      <div className="flex h-full w-full flex-col items-center justify-center bg-white p-6">
        <Loader size="lg" />
        <p className="mt-4 text-muted-foreground">Loading mapping details...</p>
      </div>
    );
  }

  return (
    <div className="flex h-full w-full flex-col bg-white">
      <div className="sticky top-0 z-10 flex items-center justify-between border-b bg-gray-50/80 p-6">
        <h2 className="text-xl font-semibold text-denim">
          {isViewMode
            ? 'View Mapping Group'
            : isEditMode
              ? 'Edit Mapping Group'
              : 'Create New Mapping Group'}
        </h2>
        <XIcon
          className="h-6 w-6 cursor-pointer text-gray-500 hover:text-gray-700"
          onClick={onClose}
        />
      </div>

      <div className="flex-1 overflow-y-auto p-6">
        <p className="mb-6 text-sm text-muted-foreground">
          {isViewMode
            ? 'View the mapping group details below.'
            : isEditMode
              ? 'Update the mapping group details below.'
              : 'A mapping group connects equivalent product prices across different pharmacies.'}
        </p>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {isViewMode ? (
              <div className="mb-6 space-y-2">
                <div className="text-sm font-medium">Group Name</div>
                <div className="rounded-md border bg-gray-50 p-2.5 text-base">
                  {equivalenceData?.name || ''}
                </div>
              </div>
            ) : (
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Group Name</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Enter a name for this group"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <div className="mb-6">
              <div className="mb-3 flex items-center justify-between">
                <FormLabel>Product Prices</FormLabel>
                {!isViewMode && (
                  <SearchInput
                    className="w-1/2"
                    onSearch={handleSearchChange}
                    placeholder="Filter by name"
                    defaultValue={searchTerm}
                  />
                )}
              </div>
              {!isViewMode && (
                <p className="mb-4 text-xs text-muted-foreground">
                  Select one product price from each pharmacy to create a
                  mapping group. This defines which product prices are
                  considered equivalent across pharmacies.
                </p>
              )}

              {isLoadingPharmacies ? (
                <div className="flex h-40 items-center justify-center">
                  <Loader size="lg" />
                  <p className="ml-3 text-muted-foreground">
                    Loading pharmacies and product prices...
                  </p>
                </div>
              ) : (
                <div className="space-y-6">
                  <Controller
                    control={form.control}
                    name="productPriceIds"
                    render={({ field }) => (
                      <>
                        {isViewMode ? (
                          // View mode - show only the mapped prices
                          <div className="rounded-lg border border-gray-200 bg-gray-50 p-4">
                            <h3 className="mb-4 text-base font-medium">
                              Mapped Product Prices
                            </h3>
                            <div className="flex flex-col gap-3">
                              {pharmaciesWithPrices.map((data) => {
                                if (!data) return null;

                                // Find selected price for this pharmacy
                                const selectedPrice = data.productPrices.find(
                                  (price: { id: string }) =>
                                    field.value?.includes(price.id),
                                );

                                if (!selectedPrice) return null;

                                return (
                                  <div
                                    key={data.pharmacy.id}
                                    className="rounded-md border border-gray-200 bg-white p-3"
                                  >
                                    <div className="flex items-center justify-between">
                                      <div className="text-sm font-medium text-gray-700">
                                        {data.pharmacy.name}
                                      </div>
                                      <div className="text-sm text-gray-600">
                                        {selectedPrice.name}
                                      </div>
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        ) : (
                          <>
                            {/* Current pharmacy first */}
                            {currentPharmacyData && (
                              <div className="rounded-lg border-2 border-denim bg-blue-50 p-4">
                                <h3 className="mb-2 flex items-center font-semibold text-denim">
                                  <span className="mr-2">
                                    {currentPharmacyData.pharmacy.name}
                                  </span>
                                  <span className="mr-1 rounded-full bg-denim px-2 py-0.5 text-xs text-white">
                                    Current Pharmacy
                                  </span>
                                  {currentPharmacyData.pharmacy.enabled ===
                                    false && (
                                    <span className="rounded-full bg-red-500 px-2 py-0.5 text-xs text-white">
                                      Disabled
                                    </span>
                                  )}
                                </h3>
                                <div className="max-h-[200px] overflow-y-auto">
                                  {currentPharmacyData.productPrices.length ===
                                  0 ? (
                                    <p className="text-sm text-gray-500">
                                      No product prices available
                                    </p>
                                  ) : (
                                    <div className="space-y-2">
                                      {/* Use cached sorted prices if available, otherwise sort on the fly */}
                                      {(
                                        sortedProductPrices[
                                          currentPharmacyData.pharmacy.id
                                        ] ||
                                        currentPharmacyData.productPrices.sort(
                                          (
                                            a: ProductPriceDetailsType,
                                            b: ProductPriceDetailsType,
                                          ) => {
                                            // Only in edit mode with selected prices, show them first
                                            if (
                                              isEditMode &&
                                              field.value?.length
                                            ) {
                                              const aSelected =
                                                field.value?.includes(a.id) ||
                                                false;
                                              const bSelected =
                                                field.value?.includes(b.id) ||
                                                false;

                                              if (aSelected && !bSelected)
                                                return -1;
                                              if (!aSelected && bSelected)
                                                return 1;
                                            }

                                            // Primary sort by product.id (product order)
                                            if (
                                              a.product?.id &&
                                              b.product?.id &&
                                              a.product.id !== b.product.id
                                            ) {
                                              return a.product.id.localeCompare(
                                                b.product.id,
                                              );
                                            }

                                            // Secondary sort by phase
                                            if (
                                              a.phase !== undefined &&
                                              b.phase !== undefined &&
                                              a.phase !== b.phase
                                            ) {
                                              return (
                                                (a.phase || 0) - (b.phase || 0)
                                              );
                                            }

                                            // Tertiary sort by name
                                            return a.name.localeCompare(b.name);
                                          },
                                        )
                                      ).map(
                                        (price: ProductPriceDetailsType) => (
                                          <div
                                            key={price.id}
                                            className="flex items-start space-x-2 py-1"
                                          >
                                            <input
                                              type="radio"
                                              id={`current-${price.id}`}
                                              name={`pharmacy-${currentPharmacyData.pharmacy.id}`}
                                              value={price.id}
                                              className="mt-1"
                                              checked={field.value?.includes(
                                                price.id,
                                              )}
                                              disabled={isViewMode}
                                              onChange={() => {
                                                if (isViewMode) return;
                                                // Filter out any product prices from the current pharmacy
                                                // This ensures we maintain only one selection per pharmacy
                                                const otherSelections =
                                                  field.value?.filter(
                                                    (id: string) =>
                                                      !currentPharmacyData.productPrices.find(
                                                        (p: { id: string }) =>
                                                          p.id === id,
                                                      ),
                                                  ) || [];

                                                field.onChange([
                                                  ...otherSelections,
                                                  price.id,
                                                ]);
                                              }}
                                            />
                                            <label
                                              htmlFor={`current-${price.id}`}
                                              className="cursor-pointer text-sm leading-none"
                                            >
                                              <div className="font-medium">
                                                {price.name}
                                              </div>
                                              <div className="mt-1">
                                                <span className="text-xs text-gray-600">
                                                  $
                                                  {(
                                                    price.unit_amount / 100
                                                  ).toFixed(2)}
                                                </span>
                                                <span className="ml-1 text-xs text-gray-500">
                                                  • {price.product?.form}
                                                </span>
                                                <span className="ml-1 text-xs text-gray-400">
                                                  • Phase {price.phase || ''}
                                                </span>
                                              </div>
                                            </label>
                                          </div>
                                        ),
                                      )}
                                    </div>
                                  )}
                                </div>
                              </div>
                            )}

                            {/* Other pharmacies */}
                            {pharmaciesWithPrices
                              .filter(
                                (data) => data?.pharmacy.id !== pharmacyId,
                              )
                              .sort((a, b) => {
                                if (!a || !b) return 0;
                                return a.pharmacy.name.localeCompare(
                                  b.pharmacy.name,
                                );
                              })
                              .map((data) => {
                                if (!data) return null;

                                return (
                                  <div
                                    key={data.pharmacy.id}
                                    className="rounded-lg border border-gray-300 p-4 transition-colors hover:border-gray-400"
                                  >
                                    <h3 className="mb-2 flex items-center font-semibold">
                                      <span className="mr-2">
                                        {data.pharmacy.name}
                                      </span>
                                      {data.pharmacy.enabled === false && (
                                        <span className="rounded-full bg-red-500 px-2 py-0.5 text-xs text-white">
                                          Disabled
                                        </span>
                                      )}
                                    </h3>
                                    <div className="max-h-[200px] overflow-y-auto">
                                      {data.productPrices.length === 0 ? (
                                        <p className="text-sm text-gray-500">
                                          No product prices available
                                        </p>
                                      ) : (
                                        <div className="space-y-2">
                                          {/* Use cached sorted prices if available, otherwise sort on the fly */}
                                          {(
                                            sortedProductPrices[
                                              data.pharmacy.id
                                            ] ||
                                            data.productPrices.sort(
                                              (
                                                a: ProductPriceDetailsType,
                                                b: ProductPriceDetailsType,
                                              ) => {
                                                // Only in edit mode with selected prices, show them first
                                                if (
                                                  isEditMode &&
                                                  field.value?.length
                                                ) {
                                                  const aSelected =
                                                    field.value?.includes(
                                                      a.id,
                                                    ) || false;
                                                  const bSelected =
                                                    field.value?.includes(
                                                      b.id,
                                                    ) || false;

                                                  if (aSelected && !bSelected)
                                                    return -1;
                                                  if (!aSelected && bSelected)
                                                    return 1;
                                                }

                                                // Primary sort by product.id (product order)
                                                if (
                                                  a.product?.id &&
                                                  b.product?.id &&
                                                  a.product.id !== b.product.id
                                                ) {
                                                  return a.product.id.localeCompare(
                                                    b.product.id,
                                                  );
                                                }

                                                // Secondary sort by phase
                                                if (
                                                  a.phase !== undefined &&
                                                  b.phase !== undefined &&
                                                  a.phase !== b.phase
                                                ) {
                                                  return (
                                                    (a.phase || 0) -
                                                    (b.phase || 0)
                                                  );
                                                }

                                                // Tertiary sort by name
                                                return a.name.localeCompare(
                                                  b.name,
                                                );
                                              },
                                            )
                                          ).map(
                                            (
                                              price: ProductPriceDetailsType,
                                            ) => (
                                              <div
                                                key={price.id}
                                                className="flex items-start space-x-2 py-1"
                                              >
                                                <input
                                                  type="radio"
                                                  id={`${data.pharmacy.id}-${price.id}`}
                                                  name={`pharmacy-${data.pharmacy.id}`}
                                                  value={price.id}
                                                  className="mt-1"
                                                  checked={field.value?.includes(
                                                    price.id,
                                                  )}
                                                  disabled={isViewMode}
                                                  onChange={() => {
                                                    if (isViewMode) return;
                                                    // Filter out any product prices from this pharmacy
                                                    // This ensures we maintain only one selection per pharmacy
                                                    const otherSelections =
                                                      field.value?.filter(
                                                        (id: string) =>
                                                          !data.productPrices.find(
                                                            (p: {
                                                              id: string;
                                                            }) => p.id === id,
                                                          ),
                                                      ) || [];

                                                    field.onChange([
                                                      ...otherSelections,
                                                      price.id,
                                                    ]);
                                                  }}
                                                />
                                                <label
                                                  htmlFor={`${data.pharmacy.id}-${price.id}`}
                                                  className="cursor-pointer text-sm leading-none"
                                                >
                                                  <div className="font-medium">
                                                    {price.name}
                                                  </div>
                                                  <div className="mt-1">
                                                    <span className="text-xs text-gray-600">
                                                      $
                                                      {(
                                                        price.unit_amount / 100
                                                      ).toFixed(2)}
                                                    </span>
                                                    <span className="ml-1 text-xs text-gray-500">
                                                      • {price.product?.form}
                                                    </span>
                                                    <span className="ml-1 text-xs text-gray-400">
                                                      • Phase{' '}
                                                      {price.phase || ''}
                                                    </span>
                                                  </div>
                                                </label>
                                              </div>
                                            ),
                                          )}
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                );
                              })}
                          </>
                        )}
                      </>
                    )}
                  />
                </div>
              )}
              {form.formState.errors.productPriceIds && (
                <p className="mt-3 text-sm text-red-500">
                  Please select at least one product price
                </p>
              )}
            </div>
          </form>
        </Form>
      </div>

      <div className="sticky bottom-0 z-10 flex items-center justify-end gap-4 border-t bg-gray-50/80 p-6">
        {isViewMode ? (
          <>
            <Button
              variant="destructive"
              onClick={() => {
                if (
                  window.confirm(
                    'Are you sure you want to delete this mapping?',
                  )
                ) {
                  void (async () => {
                    try {
                      await deleteEquivalence({
                        equivalenceId: equivalenceId!,
                      });
                      toast({
                        title: 'Success',
                        description: 'Mapping group deleted successfully',
                      });
                      onClose();
                    } catch (error) {
                      console.error('Error deleting mapping group:', error);
                      toast({
                        title: 'Error',
                        description: 'Failed to delete mapping group',
                        variant: 'destructive',
                      });
                    }
                  })();
                }
              }}
            >
              Delete Group
            </Button>
            <Button
              onClick={() => {
                // Instead of closing, we want to switch to edit mode
                // We need to update the parent component's state
                onClose(); // Close first to trigger the form reset

                // Ensure cache is invalidated before reopening
                if (equivalenceId) {
                  void queryClient.invalidateQueries({
                    queryKey: ['product-price-equivalence', equivalenceId],
                  });

                  setTimeout(() => {
                    // Setting a timeout ensures the drawer reopens with edit mode
                    const event = new CustomEvent('open-mapping-edit', {
                      detail: { mappingId: equivalenceId },
                    });
                    window.dispatchEvent(event);
                  }, 0);
                }
              }}
            >
              Edit Group
            </Button>
          </>
        ) : (
          <>
            <Button
              type="button"
              variant="tertiaryOutline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              onClick={form.handleSubmit(onSubmit)}
              disabled={isSubmitting}
            >
              {isSubmitting
                ? 'Saving...'
                : isEditMode
                  ? 'Update Group'
                  : 'Create Group'}
            </Button>
          </>
        )}
      </div>
    </div>
  );
}
