import { useMemo } from 'react';
import { EllipsisIcon } from 'lucide-react';

import { cn } from '@willow/ui';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@willow/ui/base/dropdown-menu';

interface ActionType {
  name: string;
  handleAction?: () => void;
  className?: string;
}

export const PharmacyMiscMenu = ({
  isActive,
  setIsEditMode,
  handleDeactivate,
  handleReactivate,
  handleDelete,
  handleOpenTransferDrawer,
  hasPatients = false,
}: {
  isActive: boolean;
  setIsEditMode: (value: string) => void;
  handleDeactivate: () => void;
  handleReactivate: () => void;
  handleDelete: () => void;
  handleOpenTransferDrawer: () => void;
  hasPatients?: boolean;
}) => {
  const actions: ActionType[] = useMemo(
    () => [
      {
        name: 'Edit Pharmacy',
        handleAction: () => setIsEditMode('true'),
      },
      { name: 'separator-1' },
      {
        name: 'Transfer Patients',
        handleAction: hasPatients
          ? () => handleOpenTransferDrawer()
          : undefined,
        className: !hasPatients ? 'opacity-50 cursor-not-allowed' : undefined,
      },
      { name: 'separator-2' },
      {
        name: isActive ? 'Disable Pharmacy' : 'Enable Pharmacy',
        className: isActive ? 'text-red-500' : 'text-green-500',
        handleAction: () => {
          if (isActive) {
            handleDeactivate();
          } else {
            handleReactivate();
          }
        },
      },
      { name: 'separator-3' },
      {
        name: 'Delete Pharmacy',
        className: 'text-red-700 font-semibold',
        handleAction: handleDelete,
      },
    ],
    [
      isActive,
      setIsEditMode,
      handleDeactivate,
      handleReactivate,
      handleDelete,
      handleOpenTransferDrawer,
      hasPatients,
    ],
  );

  return (
    <div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <div className="cursor-pointer">
            <EllipsisIcon size={24} color="#2F4C78" />
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-44 px-0" align="end">
          {actions.map((action) => {
            if (action.name.includes('separator'))
              return (
                <DropdownMenuSeparator
                  key={action.name}
                  className="mx-auto w-10/12"
                />
              );
            return (
              <DropdownMenuItem
                key={action.name}
                onSelect={() => {
                  if (action.handleAction) {
                    action.handleAction();
                  }
                }}
                className={cn(
                  'cursor-pointer px-4 py-3 text-xs font-normal focus:bg-[#ffecca] focus:text-[#2f4c78]',
                  action.className,
                )}
              >
                {action.name}
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
