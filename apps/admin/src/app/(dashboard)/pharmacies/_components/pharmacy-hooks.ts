import { useState } from 'react';

// Hook to manage the pharmacy transfer drawer
export function usePharmacyTransferDrawer() {
  // State for drawer visibility
  const [isOpen, setIsOpen] = useState(false);

  // State to track the source pharmacy
  const [sourcePharmacy, setSourcePharmacy] = useState<any>(null);

  const handleOpen = (pharmacy: any) => {
    setSourcePharmacy(pharmacy);
    setIsOpen(true);
  };

  const handleClose = () => {
    setIsOpen(false);
    setSourcePharmacy(null);
  };

  return {
    isOpen,
    sourcePharmacy,
    handleOpen,
    handleClose,
    drawer: { isOpen, handleOpen, handleClose, sourcePharmacy },
  };
}
