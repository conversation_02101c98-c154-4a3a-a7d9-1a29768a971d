'use client';

import { useEffect, useMemo } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { Edit2Icon, PlusIcon, XIcon } from 'lucide-react';
import { useQueryState } from 'nuqs';

import { Button } from '@willow/ui/base/button';
import { DrawerTitle } from '@willow/ui/base/drawer';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@willow/ui/base/table';
import { useToast } from '@willow/ui/base/use-toast';
import {
  Drawer,
  DrawerContent,
  DrawerOverlay,
  DrawerPortal,
  DrawerTrigger,
} from '@willow/ui/base/vaul';
import { Loader } from '@willow/ui/loader';
import { SearchInput } from '@willow/ui/search-input';

import type { ProductPriceEquivalence } from '~/hooks/product-price-equivalence';
import {
  useDeleteProductPriceEquivalence,
  useGetProductPriceEquivalences,
} from '~/hooks/product-price-equivalence';
import { ProductPriceEquivalenceForm } from './product-price-equivalence-form';

interface PharmacyMappingsProps {
  pharmacyId: string;
}

export function PharmacyMappings({ pharmacyId }: PharmacyMappingsProps) {
  const [search, setSearch] = useQueryState('search', {
    defaultValue: '',
  });
  const [selectedMappingId, setSelectedMappingId] = useQueryState('mappingId', {
    defaultValue: '',
    parse: (value) => value || '',
  });
  const [formMode, setFormMode] = useQueryState<'create' | 'edit' | 'view'>(
    'formMode',
    {
      defaultValue: 'create',
      parse: (value) => {
        return ['create', 'edit', 'view'].includes(value)
          ? (value as 'create' | 'edit' | 'view')
          : 'create';
      },
    },
  );
  const { toast } = useToast();
  const { mutateAsync: deleteMapping } = useDeleteProductPriceEquivalence();
  const queryClient = useQueryClient();

  // Listen for edit events from the form component
  useEffect(() => {
    const handleOpenMappingEdit = (event: Event) => {
      const customEvent = event as CustomEvent<{ mappingId: string }>;
      if (customEvent.detail?.mappingId) {
        void setSelectedMappingId(customEvent.detail.mappingId);
        void setFormMode('edit');
      }
    };

    window.addEventListener('open-mapping-edit', handleOpenMappingEdit);

    return () => {
      window.removeEventListener('open-mapping-edit', handleOpenMappingEdit);
    };
  }, []);

  const { data, isPending, isError } = useGetProductPriceEquivalences({
    pharmacyId,
    search: search || undefined,
  });

  const mappings = useMemo(() => {
    return data?.equivalenceGroups || [];
  }, [data]);

  const handleSearchChange = (query: string | null) => {
    void setSearch(query || '');
  };

  const handleDeleteMapping = async (id: string) => {
    try {
      await deleteMapping({ equivalenceId: id });
      toast({
        title: 'Success',
        description: 'Mapping group deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting mapping group:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete mapping group',
        variant: 'destructive',
      });
    }
  };

  const renderMappingContent = (mapping: ProductPriceEquivalence) => {
    return (
      <TableRow key={mapping.id} className="cursor-pointer hover:bg-muted/50">
        <TableCell className="font-medium">
          <div className="text-sm text-denim">{mapping.name}</div>
        </TableCell>
        <TableCell className="flex justify-end space-x-2">
          <Button
            size="icon"
            onClick={(e) => {
              e.stopPropagation();

              // Invalidate cache to ensure fresh data
              void queryClient.invalidateQueries({
                queryKey: ['product-price-equivalence', mapping.id],
              });

              void setSelectedMappingId(mapping.id);
              void setFormMode('edit');
            }}
          >
            <Edit2Icon size={16} className="text-denim" />
          </Button>
          <Button
            size="icon"
            onClick={(e) => {
              e.stopPropagation();
              if (
                window.confirm('Are you sure you want to delete this mapping?')
              ) {
                void handleDeleteMapping(mapping.id);
              }
            }}
          >
            <XIcon size={16} className="text-red-500" />
          </Button>
        </TableCell>
      </TableRow>
    );
  };

  if (isPending) {
    return (
      <div className="flex h-40 items-center justify-center">
        <Loader />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex h-40 items-center justify-center rounded-md border border-dashed p-4 text-center text-sm text-red-500">
        Error loading mappings. Please try again.
      </div>
    );
  }

  return (
    <div className="w-full px-10">
      <div className="mb-5 mt-10 space-y-6">
        <div className="text-2xl font-medium text-denim">Mappings</div>
      </div>
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <SearchInput
            className="w-1/2"
            onSearch={handleSearchChange}
            placeholder="Search mappings"
            defaultValue={search}
          />
        </div>
        <div className="flex justify-between">
          <h3 className="text-lg font-medium text-denim">Pharmacy Mappings</h3>
          <Button
            size="sm"
            className="flex items-center gap-1"
            onClick={() => {
              void setSelectedMappingId('new');
              void setFormMode('create');
            }}
          >
            <PlusIcon size={16} />
            <span>Add Mapping</span>
          </Button>
        </div>

        <Drawer
          direction="right"
          open={selectedMappingId !== ''}
          onOpenChange={(value) => {
            if (!value) void setSelectedMappingId('');
          }}
          modal={true}
        >
          <div className="h-full w-full">
            {mappings.length > 0 ? (
              <div className="overflow-hidden rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead className="w-20 text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {mappings.map((mapping) => (
                      <DrawerTrigger
                        key={mapping.id}
                        asChild
                        onClick={() => {
                          // Invalidate cache to ensure fresh data
                          void queryClient.invalidateQueries({
                            queryKey: ['product-price-equivalence', mapping.id],
                          });

                          void setSelectedMappingId(mapping.id);
                          void setFormMode('view');
                        }}
                      >
                        {renderMappingContent(mapping)}
                      </DrawerTrigger>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="flex h-40 items-center justify-center rounded-md border border-dashed p-4 text-center text-sm text-gray-500">
                No mappings found for this pharmacy.
              </div>
            )}
          </div>

          {selectedMappingId !== '' && (
            <DrawerPortal>
              <DrawerOverlay className="fixed inset-0 z-50 bg-black/25" />
              <DrawerContent className="fixed right-0 top-0 z-50 m-0 h-full w-[600px] max-w-full !touch-none !select-text overflow-y-auto overflow-x-hidden border-l border-border bg-white p-0 shadow-lg">
                <DrawerTitle className="hidden">
                  Product Price Equivalence Group
                </DrawerTitle>
                <ProductPriceEquivalenceForm
                  pharmacyId={pharmacyId}
                  equivalenceId={
                    selectedMappingId === 'new' ? undefined : selectedMappingId
                  }
                  isOpen={true}
                  mode={formMode}
                  onClose={() => {
                    void setSelectedMappingId('');
                    void setFormMode('create');
                  }}
                />
              </DrawerContent>
            </DrawerPortal>
          )}
        </Drawer>
      </div>
    </div>
  );
}
