'use client';

import { useEffect, useMemo, useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { XIcon } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { But<PERSON> } from '@willow/ui/base/button';
import { Checkbox } from '@willow/ui/base/checkbox';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@willow/ui/base/form';
import { Input } from '@willow/ui/base/input';
import { useToast } from '@willow/ui/base/use-toast';

import type { CreatePharmacyDto, UpdatePharmacyDto } from '~/hooks/pharmacies';
import {
  useCreatePharmacy,
  useGetPharmacy,
  useUpdatePharmacy,
} from '~/hooks/pharmacies';
import { useGetStates } from '~/hooks/states';

interface PharmacyInfoProps {
  pharmacyId: string;
  handleClose: () => void;
}

const createPharmacySchema = z.object({
  name: z.string().min(1, 'Name is required'),
  doseSpotPharmacyId: z.string().min(1, 'DoseSpot Pharmacy ID is required'),
  enabled: z.boolean().optional().default(false),
  enableApi: z.union([z.boolean(), z.null()]).optional().default(null),
  regularPriority: z.number().optional().default(0),
  usingGLP1Priority: z.number().optional().default(0),
  color: z.string().optional(),
  stateIds: z.array(z.string()).min(1, 'At least one state is required'),
  metadata: z.any().optional(),
});

const updatePharmacySchema = z.object({
  name: z.string().min(1, 'Name is required'),
  doseSpotPharmacyId: z.string().min(1, 'DoseSpot Pharmacy ID is required'),
  enabled: z.boolean().optional(),
  enableApi: z.union([z.boolean(), z.null()]).optional(),
  regularPriority: z.number().optional(),
  usingGLP1Priority: z.number().optional(),
  color: z.string().optional(),
  stateIds: z.array(z.string()).min(1, 'At least one state is required'),
  metadata: z.any().optional(),
});

export function PharmacyInfoEdit({
  pharmacyId,
  handleClose,
}: PharmacyInfoProps) {
  const isNewPharmacy = pharmacyId === 'new';
  const {
    data: pharmacy,
    isLoading,
    refetch,
  } = useGetPharmacy(isNewPharmacy ? '' : pharmacyId);
  const { data: statesData, isLoading: statesLoading } = useGetStates();
  const { mutateAsync: createPharmacy } = useCreatePharmacy();
  const { mutateAsync: updatePharmacy } = useUpdatePharmacy();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Process the states data to create enabled and disabled groups
  const { enabledStates, disabledStates } = useMemo(() => {
    const allStates = statesData?.states || [];

    // Sort all states alphabetically by name first
    const sortedAllStates = [...allStates].sort((a, b) =>
      a.name.localeCompare(b.name),
    );

    // Split into enabled and disabled groups
    return {
      enabledStates: sortedAllStates.filter((state) => state.enabled),
      disabledStates: sortedAllStates.filter((state) => !state.enabled),
    };
  }, [statesData]);

  const form = useForm<CreatePharmacyDto | UpdatePharmacyDto>({
    resolver: zodResolver(
      isNewPharmacy ? createPharmacySchema : updatePharmacySchema,
    ),
    defaultValues: isNewPharmacy
      ? {
          name: '',
          doseSpotPharmacyId: '',
          enabled: false,
          enableApi: null,
          regularPriority: 0,
          usingGLP1Priority: 0,
          color: '',
          stateIds: [],
          metadata: {},
        }
      : undefined,
  });

  useEffect(() => {
    if (pharmacy && !isNewPharmacy) {
      // Extract all states the pharmacy serves
      const stateIds: string[] = [];

      // Add states from PharmacyOnState
      if (pharmacy.PharmacyOnState && Array.isArray(pharmacy.PharmacyOnState)) {
        pharmacy.PharmacyOnState.forEach((relation) => {
          if (relation.state?.id && !stateIds.includes(relation.state.id)) {
            stateIds.push(relation.state.id);
          }
        });
      }

      form.reset({
        name: pharmacy.name || '',
        doseSpotPharmacyId: pharmacy.doseSpotPharmacyId || '',
        enabled: pharmacy.enabled,
        enableApi: pharmacy.enableApi,
        regularPriority: pharmacy.regularPriority || 0,
        usingGLP1Priority: pharmacy.usingGLP1Priority || 0,
        color: pharmacy.color || '',
        stateIds: stateIds.length > 0 ? stateIds : [],
        metadata: pharmacy.metadata || {},
      });
    }
  }, [pharmacy, form, isNewPharmacy]);

  const onSubmit = async (data: CreatePharmacyDto | UpdatePharmacyDto) => {
    setIsSubmitting(true);
    try {
      if (isNewPharmacy) {
        await createPharmacy(data as CreatePharmacyDto);
        toast({
          title: 'Success',
          description: 'Pharmacy created successfully',
        });

        // After creating a new pharmacy, navigate back to the list with showInactive=true
        // to help users see the newly created pharmacy (which is disabled by default)
        if (typeof window !== 'undefined') {
          window.location.href = '/pharmacies/all?showInactive=true';
        } else {
          handleClose();
        }
      } else {
        await updatePharmacy({
          id: pharmacyId,
          pharmacyData: data as UpdatePharmacyDto,
        });

        // Explicitly refetch the pharmacy data to ensure we have the latest information
        await refetch();

        toast({
          title: 'Success',
          description: 'Pharmacy updated successfully',
        });
        handleClose(); // Close the edit mode instead of switching to view mode
      }
    } catch (error) {
      console.error('Error saving pharmacy:', error);
      toast({
        title: 'Error',
        description: 'Failed to save pharmacy',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading && !isNewPharmacy) {
    return (
      <div className="flex h-full w-full items-center justify-center bg-white">
        <div>Loading...</div>
      </div>
    );
  }

  // Function to toggle all enabled states
  const toggleAllEnabledStates = () => {
    // Get current state IDs from the form
    const currentStateIds = form.getValues('stateIds') || [];

    // Get all enabled state IDs
    const enabledStateIds = enabledStates.map((state) => state.id);

    // Check if all enabled states are already selected
    const allEnabledSelected = enabledStateIds.every((id) =>
      currentStateIds.includes(id),
    );

    let newStateIds: string[];

    if (allEnabledSelected) {
      // If all are selected, unselect all enabled states
      newStateIds = currentStateIds.filter(
        (id) => !enabledStateIds.includes(id),
      );
    } else {
      // If not all are selected, select all enabled states
      newStateIds = [
        ...currentStateIds.filter((id) => !enabledStateIds.includes(id)), // Keep non-enabled states
        ...enabledStateIds, // Add all enabled states
      ];
    }

    // Update the form value
    form.setValue('stateIds', newStateIds);
  };

  // Render the states section based on if we're in view mode or edit mode
  const renderStatesSection = () => {
    return (
      <div className="mb-4">
        <div className="mb-2 flex items-center justify-between">
          <FormLabel className="block text-sm font-medium text-gray-700">
            Serving States
          </FormLabel>
          <Button type="button" size="sm" onClick={toggleAllEnabledStates}>
            {enabledStates.every((state) =>
              (form.getValues('stateIds') || []).includes(state.id),
            )
              ? 'Deselect All'
              : 'Select All Available'}
          </Button>
        </div>
        <div className="mt-2 space-y-4">
          {statesLoading ? (
            <div className="text-sm text-gray-500">Loading states...</div>
          ) : (
            <>
              {/* Enabled states section */}
              {enabledStates.length > 0 && (
                <div>
                  <h4 className="mb-2 text-sm font-medium text-green-600">
                    Enabled States
                  </h4>
                  <div className="grid grid-cols-3 gap-2">
                    {enabledStates.map((state) => (
                      <FormField
                        key={state.id}
                        control={form.control}
                        name="stateIds"
                        render={({ field }) => {
                          const stateSelected =
                            field.value?.includes(state.id) || false;
                          return (
                            <FormItem
                              key={state.id}
                              className="flex flex-row items-start space-x-2 space-y-0"
                            >
                              <FormControl>
                                <Checkbox
                                  className="border-denim data-[state=unchecked]:border-denim"
                                  checked={stateSelected}
                                  onCheckedChange={(checked) => {
                                    // Create a copy of the current array
                                    const updatedValue = [
                                      ...(field.value || []),
                                    ];
                                    if (checked) {
                                      // Add if not already included
                                      if (!updatedValue.includes(state.id)) {
                                        updatedValue.push(state.id);
                                      }
                                    } else {
                                      // Remove if included
                                      const index = updatedValue.indexOf(
                                        state.id,
                                      );
                                      if (index !== -1) {
                                        updatedValue.splice(index, 1);
                                      }
                                    }
                                    field.onChange(updatedValue);
                                  }}
                                />
                              </FormControl>
                              <FormLabel className="text-sm font-normal">
                                {state.name} ({state.code})
                              </FormLabel>
                            </FormItem>
                          );
                        }}
                      />
                    ))}
                  </div>
                </div>
              )}

              {/* Disabled states section */}
              {disabledStates.length > 0 && (
                <div className="mt-4">
                  <h4 className="mb-2 text-sm font-medium text-gray-500">
                    Disabled States
                  </h4>
                  <div className="grid grid-cols-3 gap-2">
                    {disabledStates.map((state) => (
                      <FormField
                        key={state.id}
                        control={form.control}
                        name="stateIds"
                        render={({ field }) => {
                          const stateSelected =
                            field.value?.includes(state.id) || false;
                          return (
                            <FormItem
                              key={state.id}
                              className="flex flex-row items-start space-x-2 space-y-0"
                            >
                              <FormControl>
                                <Checkbox
                                  className="border-gray-400 data-[state=unchecked]:border-gray-400"
                                  checked={stateSelected}
                                  onCheckedChange={(checked) => {
                                    // Create a copy of the current array
                                    const updatedValue = [
                                      ...(field.value || []),
                                    ];
                                    if (checked) {
                                      // Add if not already included
                                      if (!updatedValue.includes(state.id)) {
                                        updatedValue.push(state.id);
                                      }
                                    } else {
                                      // Remove if included
                                      const index = updatedValue.indexOf(
                                        state.id,
                                      );
                                      if (index !== -1) {
                                        updatedValue.splice(index, 1);
                                      }
                                    }
                                    field.onChange(updatedValue);
                                  }}
                                />
                              </FormControl>
                              <FormLabel className="text-sm font-normal text-gray-500">
                                {state.name} ({state.code})
                              </FormLabel>
                            </FormItem>
                          );
                        }}
                      />
                    ))}
                  </div>
                </div>
              )}

              {enabledStates.length === 0 && disabledStates.length === 0 && (
                <div className="text-sm text-gray-500">No states available</div>
              )}
            </>
          )}
          <FormMessage />
        </div>
      </div>
    );
  };

  // For new pharmacy, use the split layout drawer like admin form
  if (isNewPharmacy) {
    return (
      <div className="relative flex h-full w-full grow bg-white">
        {/* Left sidebar */}
        <div className="flex w-[50px] flex-col bg-stone-light">
          {/* Empty sidebar */}
        </div>

        {/* Right content area with form */}
        <div className="flex w-[calc(100%-50px)] flex-col overflow-scroll">
          <div className="sticky top-0 z-10 bg-white px-10 pt-10">
            <div className="mb-6 flex flex-row justify-between">
              <h2 className="text-xl font-semibold text-denim">
                Add New Pharmacy
              </h2>
              <XIcon
                size={24}
                className="cursor-pointer text-denim"
                onClick={handleClose}
              />
            </div>
          </div>

          <div className="px-10 pb-10">
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-5"
              >
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Pharmacy Name</FormLabel>
                        <FormControl>
                          <Input size="sm" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="doseSpotPharmacyId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>DoseSpot Pharmacy ID</FormLabel>
                        <FormControl>
                          <Input size="sm" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="regularPriority"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Regular Priority</FormLabel>
                        <FormControl>
                          <Input
                            size="sm"
                            type="number"
                            min="0"
                            value={field.value?.toString() || '0'}
                            onChange={(e) =>
                              field.onChange(Number(e.target.value))
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="usingGLP1Priority"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>GLP1 Priority</FormLabel>
                        <FormControl>
                          <Input
                            size="sm"
                            type="number"
                            min="0"
                            value={field.value?.toString() || '0'}
                            onChange={(e) =>
                              field.onChange(Number(e.target.value))
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="color"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Color</FormLabel>
                        <FormControl>
                          <div className="flex items-center">
                            <Input
                              size="sm"
                              type="color"
                              className="mr-2 h-10 w-12"
                              value={field.value || '#ffffff'}
                              onChange={field.onChange}
                            />
                            <Input
                              size="sm"
                              type="text"
                              value={field.value || ''}
                              onChange={field.onChange}
                              placeholder="#RRGGBB"
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="enableApi"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>API Integration</FormLabel>
                        <FormControl>
                          <select
                            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                            value={
                              field.value === null
                                ? 'null'
                                : field.value === true
                                  ? 'true'
                                  : 'false'
                            }
                            onChange={(e) => {
                              const value = e.target.value;
                              if (value === 'null') {
                                field.onChange(null);
                              } else if (value === 'true') {
                                field.onChange(true);
                              } else if (value === 'false') {
                                field.onChange(false);
                              }
                            }}
                          >
                            <option value="null">Not Applicable</option>
                            <option value="true">Enabled</option>
                            <option value="false">Disabled</option>
                          </select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                {/* Enabled checkbox removed - new pharmacies are created with enabled=false by default */}

                {renderStatesSection()}

                {/* Products section removed as products will be associated directly from their creation/edit forms */}
                {/* Metadata field could be added here if needed */}
                <div className="flex justify-between pt-4">
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="tertiary"
                      onClick={handleClose}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting ? 'Saving...' : 'Create Pharmacy'}
                    </Button>
                  </div>
                </div>
              </form>
            </Form>
          </div>
        </div>
      </div>
    );
  }

  // For existing pharmacy edits, just render the form without all the view details
  return (
    <div className="h-full w-full overflow-y-auto px-10 py-6">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-denim">Edit Pharmacy</h2>
      </div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Pharmacy Name</FormLabel>
                  <FormControl>
                    <Input size="sm" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="doseSpotPharmacyId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>DoseSpot Pharmacy ID</FormLabel>
                  <FormControl>
                    <Input size="sm" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="regularPriority"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Regular Priority</FormLabel>
                  <FormControl>
                    <Input
                      size="sm"
                      type="number"
                      min="0"
                      value={field.value?.toString() || '0'}
                      onChange={(e) => field.onChange(Number(e.target.value))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="usingGLP1Priority"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>GLP1 Priority</FormLabel>
                  <FormControl>
                    <Input
                      size="sm"
                      type="number"
                      min="0"
                      value={field.value?.toString() || '0'}
                      onChange={(e) => field.onChange(Number(e.target.value))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="color"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Color</FormLabel>
                  <FormControl>
                    <div className="flex items-center">
                      <Input
                        size="sm"
                        type="color"
                        className="mr-2 h-10 w-12"
                        value={field.value || '#ffffff'}
                        onChange={field.onChange}
                      />
                      <Input
                        size="sm"
                        type="text"
                        value={field.value || ''}
                        onChange={field.onChange}
                        placeholder="#RRGGBB"
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="enableApi"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>API Integration</FormLabel>
                  <FormControl>
                    <select
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      value={
                        field.value === null
                          ? 'null'
                          : field.value === true
                            ? 'true'
                            : 'false'
                      }
                      onChange={(e) => {
                        const value = e.target.value;
                        if (value === 'null') {
                          field.onChange(null);
                        } else if (value === 'true') {
                          field.onChange(true);
                        } else if (value === 'false') {
                          field.onChange(false);
                        }
                      }}
                    >
                      <option value="null">Not Applicable</option>
                      <option value="true">Enabled</option>
                      <option value="false">Disabled</option>
                    </select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div className="grid grid-cols-1 gap-4">
            <FormField
              control={form.control}
              name="enabled"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-2 space-y-0">
                  <FormControl>
                    <Checkbox
                      className="border-denim"
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <FormLabel>Enabled</FormLabel>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {renderStatesSection()}

          {/* Products section removed as products will be associated directly from their creation/edit forms */}
          {/* Metadata field could be added here if needed */}
          <div className="flex justify-between pt-4">
            <div className="flex gap-2">
              <Button
                type="button"
                variant="tertiary"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Saving...' : 'Update Pharmacy'}
              </Button>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
}
