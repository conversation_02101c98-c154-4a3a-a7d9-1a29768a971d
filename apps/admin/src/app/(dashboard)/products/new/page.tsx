'use client';

import { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';

export default function NewProductPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const duplicateId = searchParams.get('duplicate');

  useEffect(() => {
    // Redirect to products/all with the productId query parameter set to 'new'
    // and pass along the duplicate id if present
    const queryParams = new URLSearchParams();
    queryParams.set('productId', 'new');

    if (duplicateId) {
      queryParams.set('duplicate', duplicateId);
    }

    router.replace(`/products/all?${queryParams.toString()}`);
  }, [router, duplicateId]);

  return null;
}
