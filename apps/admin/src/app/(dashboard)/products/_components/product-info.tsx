'use client';

import { useEffect, useMemo, useRef, useState } from 'react';
import Link from 'next/link';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { ChevronDown, XIcon } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@willow/ui/base/button';
import { Checkbox } from '@willow/ui/base/checkbox';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@willow/ui/base/form';
import { Input } from '@willow/ui/base/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@willow/ui/base/popover';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@willow/ui/base/tabs';
import { Textarea } from '@willow/ui/base/textarea';
import { useToast } from '@willow/ui/base/use-toast';
import { Loader } from '@willow/ui/loader';

import { env } from '~/env';
import { useGetPharmacies } from '~/hooks/pharmacies';
import { useGetProductCategories } from '~/hooks/product-categories';
import {
  ProductForms,
  ProductGenericName,
  ProductTypes,
  useCreateProduct,
  useDeactivateProduct,
  useDeleteProduct,
  useGetProduct,
  useReactivateProduct,
  useUpdateProduct,
  useUploadProductImage,
} from '~/hooks/products';
import { ProductMiscMenu } from './product-misc-menu';

interface ProductInfoProps {
  productId: string;
  handleClose: () => void;
}

// Create schema with enum values that match ProductForms, ProductTypes, and ProductGenericName
const formValues = Object.values(ProductForms) as [string, ...string[]];
const typeValues = Object.values(ProductTypes) as [string, ...string[]];
const genericNameValues = Object.values(ProductGenericName) as [
  string,
  ...string[],
];

const createProductSchema = z.object({
  name: z.string().min(1, 'Product name is required'),
  description: z.string().nullable(),
  image: z.string().optional(),
  genericName: z.enum(genericNameValues).optional(),
  form: z.enum(formValues).optional(),
  tags: z.string().optional(),
  type: z.enum(typeValues).optional(),
  label: z.string().optional(),
  onboardingLabel: z.string().optional(),
  order: z.coerce.number().optional(),
  notice: z.string().optional(),
  supplyLength: z.string().optional(),
  weightLossMultiplier: z.coerce.number().optional(),
  customCard: z.string().optional(),
  isAvailableInOnboarding: z.boolean().optional(),
  pharmacyId: z.string().min(1, 'Pharmacy is required'),
  productCategoryIds: z
    .array(z.string())
    .min(1, 'At least one product category is required'),
});

const updateProductSchema = z.object({
  name: z.string().min(1, 'Product name is required'),
  description: z.string().nullable(),
  image: z.string().optional(),
  genericName: z.enum(genericNameValues).optional(),
  form: z.enum(formValues).optional(),
  tags: z.string().optional(),
  type: z.enum(typeValues).optional(),
  label: z.string().optional(),
  onboardingLabel: z.string().optional(),
  order: z.coerce.number().optional(),
  notice: z.string().optional(),
  supplyLength: z.string().optional(),
  weightLossMultiplier: z.coerce.number().optional(),
  customCard: z.string().optional(),
  isAvailableInOnboarding: z.boolean().optional(),
  pharmacyId: z.string().min(1, 'Pharmacy is required'),
  productCategoryIds: z
    .array(z.string())
    .min(1, 'At least one product category is required'),
});

interface ProductSidebarProps {
  product: any; // Use proper type here if available
  showMenu?: boolean;
  setIsEditMode?: (isEdit: boolean) => void;
  handleDeactivate?: () => void;
  handleReactivate?: () => void;
  handleDelete?: () => void;
}

function ProductSidebar({
  product,
  showMenu = true,
  setIsEditMode,
  handleDeactivate,
  handleReactivate,
  handleDelete,
}: ProductSidebarProps) {
  const getProductIcon = () => {
    if (product.image) return null;

    // Create an icon based on the product form
    const formToIcon: Record<string, string> = {
      injectable: '💉',
      oral: '💊',
      tablet: '💊',
    };

    return formToIcon[product.form || 'injectable'] || '📦';
  };

  const stripeBaseUrl = useMemo(() => {
    return env.NEXT_PUBLIC_ENVIRONMENT === 'production'
      ? 'https://dashboard.stripe.com/products/'
      : 'https://dashboard.stripe.com/test/products/';
  }, []);

  return (
    <div className="flex w-1/3 flex-col justify-between overflow-scroll bg-stone-light px-6 py-10">
      <div className="flex flex-col gap-6">
        <div className="flex justify-between">
          <div className="flex flex-row items-center gap-4">
            {product.image ? (
              <div className="relative h-[50px] w-[50px] overflow-hidden rounded-full">
                <img
                  src={product.image}
                  alt={product.name}
                  className="h-full w-full object-cover"
                />
              </div>
            ) : (
              <div className="relative flex h-[50px] w-[50px] items-center justify-center rounded-full bg-denim-light text-xl text-white">
                {getProductIcon()}
              </div>
            )}
            <div>
              <div className="text-base font-medium text-dark">
                {product.label || product.name}
              </div>
              <div className="flex flex-col text-[11px] font-medium text-stone/70">
                <span>{product.form || 'Not Set'}</span>
                {product.pharmacy && (
                  <Link
                    href={`/pharmacies/all?pharmacyId=${product.pharmacy.id}`}
                    className="text-denim hover:underline"
                  >
                    {product.pharmacy.name}
                  </Link>
                )}
              </div>
            </div>
          </div>
          {showMenu &&
            setIsEditMode &&
            handleDeactivate &&
            handleReactivate &&
            handleDelete && (
              <ProductMiscMenu
                productId={product.id}
                isActive={product.active ?? false}
                setIsEditMode={setIsEditMode}
                handleDeactivate={handleDeactivate}
                handleReactivate={handleReactivate}
                handleDelete={handleDelete}
                canBeDeleted={product.canBeDeleted ?? false}
              />
            )}
        </div>

        <div className="flex flex-col gap-4">
          <div className="text-sm font-normal text-orange">
            Basic Information
          </div>
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">Status</div>
            <div className="text-xs font-normal text-dark">
              {product.active ? (
                <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                  Active
                </span>
              ) : (
                <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                  Inactive
                </span>
              )}
            </div>
          </div>
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">
              Product Type
            </div>
            <div className="text-xs font-normal text-dark">
              {product.type || 'Not Set'}
            </div>
          </div>
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">
              Generic Name
            </div>
            <div className="text-xs font-normal capitalize text-dark">
              {product.genericName || 'Not Set'}
            </div>
          </div>
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">Categories</div>
            <div className="text-xs font-normal text-dark">
              {product.productCategories && product.productCategories.length > 0
                ? product.productCategories
                    .map(
                      (pc: {
                        productCategory: { label?: string; name: string };
                      }) => pc.productCategory.label || pc.productCategory.name,
                    )
                    .join(', ')
                : 'Not Set'}
            </div>
          </div>
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">Stripe</div>
            <Link
              href={`${stripeBaseUrl}${product.id}`}
              target="_blank"
              className="text-xs text-denim hover:underline"
            >
              {product.name}
            </Link>
          </div>
        </div>

        <div className="flex flex-col gap-4">
          <div className="text-sm font-normal text-orange">
            Display Settings
          </div>
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">Label</div>
            <div className="text-xs font-normal text-dark">
              {product.label || 'Not Set'}
            </div>
          </div>
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">
              Onboarding Label
            </div>
            <div className="text-xs font-normal text-dark">
              {product.onboardingLabel || 'Not Set'}
            </div>
          </div>
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">
              Available in Onboarding
            </div>
            <div className="text-xs font-normal text-dark">
              {product.isAvailableInOnboarding ? 'Yes' : 'No'}
            </div>
          </div>
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">Order</div>
            <div className="text-xs font-normal text-dark">
              {product.order !== undefined ? product.order : 'Not Set'}
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-4">
          <div className="text-sm font-normal text-orange">
            Additional Details
          </div>
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">
              Supply Length
            </div>
            <div className="text-xs font-normal text-dark">
              {product.supplyLength || 'Not Set'}
            </div>
          </div>
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">
              Weight Loss Multiplier
            </div>
            <div className="text-xs font-normal text-dark">
              {product.weightLossMultiplier !== undefined
                ? product.weightLossMultiplier
                : 'Not Set'}
            </div>
          </div>
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">Custom Card</div>
            <div className="text-xs font-normal text-dark">
              {product.customCard || 'Not Set'}
            </div>
          </div>
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">Tags</div>
            <div className="text-xs font-normal text-dark">
              {product.tags || 'Not Set'}
            </div>
          </div>
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">Created At</div>
            <div className="text-xs font-normal text-dark">
              {format(new Date(product.createdAt), 'MMM dd, yyyy')}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export function ProductInfo({ productId, handleClose }: ProductInfoProps) {
  const isNewProduct = productId === 'new';
  // Check if we're duplicating a product by parsing the URL query params
  const searchParams =
    typeof window !== 'undefined'
      ? new URLSearchParams(window.location.search)
      : new URLSearchParams();
  const duplicateFromId = searchParams.get('duplicate');

  // Get the original product data if we're duplicating
  const { data: duplicateSourceProduct, isLoading: isLoadingDuplicateSource } =
    useGetProduct(duplicateFromId || '');

  // Get the current product data (or empty if new)
  const {
    data: product,
    isLoading,
    refetch,
  } = useGetProduct(isNewProduct ? '' : productId);

  const { data: pharmaciesData } = useGetPharmacies({
    limit: 100, // Fetch up to 100 pharmacies
    showInactive: true, // Show all pharmacies, including disabled ones
    sortBy: 'regularPriority', // Sort by priority
    direction: 'desc', // Higher priority first
  });

  const { data: categoriesData } = useGetProductCategories({
    limit: 100, // Fetch all categories
    sortBy: 'order',
    direction: 'asc',
  });
  const { mutateAsync: createProduct } = useCreateProduct();
  const { mutateAsync: updateProduct } = useUpdateProduct();
  const { mutateAsync: deactivateProduct } = useDeactivateProduct();
  const { mutateAsync: reactivateProduct } = useReactivateProduct();
  const { mutateAsync: deleteProduct } = useDeleteProduct();
  const { mutateAsync: uploadImage, isPending: isUploading } =
    useUploadProductImage();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isEditMode, setIsEditMode] = useState(isNewProduct);
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [selectedTab, setSelectedTab] = useState('details');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const form = useForm<{
    name: string;
    description: string | null;
    image?: string;
    genericName?: ProductGenericName;
    form?: ProductForms;
    tags?: string;
    type?: ProductTypes;
    label?: string;
    onboardingLabel?: string;
    order?: number;
    notice?: string;
    supplyLength?: string;
    weightLossMultiplier?: number;
    customCard?: string;
    isAvailableInOnboarding?: boolean;
    pharmacyId: string;
    productCategoryIds: string[];
  }>({
    resolver: zodResolver(
      isNewProduct ? createProductSchema : updateProductSchema,
    ),
    defaultValues: isNewProduct
      ? {
          name: '',
          description: '',
          genericName: ProductGenericName.semaglutide,
          form: ProductForms.injectable,
          type: ProductTypes.core,
          isAvailableInOnboarding: false,
          pharmacyId: '',
          productCategoryIds: [],
        }
      : undefined,
  });

  useEffect(() => {
    // If duplicating a product, use the duplicate source data
    if (isNewProduct && duplicateSourceProduct && duplicateFromId) {
      const metadata = duplicateSourceProduct.metadata || {};

      // Set the image URL if it exists in the source product
      if (duplicateSourceProduct.image) {
        setImageUrl(duplicateSourceProduct.image);
      }

      // Cast form and type values to their respective enum types
      const formValue = (duplicateSourceProduct.form ||
        metadata.form ||
        'injectable') as ProductForms;
      const typeValue = (duplicateSourceProduct.type ||
        metadata.type ||
        'core') as ProductTypes;

      form.reset({
        name: duplicateSourceProduct.name || '',
        description: duplicateSourceProduct.description || '',
        image: duplicateSourceProduct.image || '',
        genericName:
          duplicateSourceProduct.genericName || ProductGenericName.semaglutide,
        form: formValue,
        tags: duplicateSourceProduct.tags || metadata.tags || '',
        type: typeValue,
        label: duplicateSourceProduct.label || metadata.label || '',
        onboardingLabel:
          duplicateSourceProduct.onboardingLabel ||
          metadata.onboardingLabel ||
          '',
        order: duplicateSourceProduct.order || metadata.order,
        notice: duplicateSourceProduct.notice || metadata.notice || '',
        supplyLength:
          duplicateSourceProduct.supplyLength || metadata.supplyLength || '',
        weightLossMultiplier:
          duplicateSourceProduct.weightLossMultiplier ||
          metadata.weightLossMultiplier,
        customCard:
          duplicateSourceProduct.customCard || metadata.customCard || '',
        isAvailableInOnboarding:
          duplicateSourceProduct.isAvailableInOnboarding ??
          metadata.isAvailableInOnboarding ??
          false,
        pharmacyId: '', // Reset pharmacy ID so user must select one
        productCategoryIds: duplicateSourceProduct.productCategories
          ? duplicateSourceProduct.productCategories.map(
              (pc) => pc.productCategory.id,
            )
          : [],
      });

      setIsEditMode(true);
    }
    // For regular editing of existing product, use the product data
    else if (product && !isNewProduct) {
      // Extract values from metadata if they exist there and the dedicated fields don't
      const metadata = product.metadata || {};

      // Set the image URL if it exists
      if (product.image) {
        setImageUrl(product.image);
      }

      // Cast form and type values to their respective enum types
      const formValue = (product.form ||
        metadata.form ||
        'injectable') as ProductForms;
      const typeValue = (product.type ||
        metadata.type ||
        'core') as ProductTypes;

      form.reset({
        name: product.name || '',
        description: product.description || '',
        image: product.image || '',
        genericName: product.genericName || ProductGenericName.semaglutide,
        form: formValue,
        tags: product.tags || metadata.tags || '',
        type: typeValue,
        label: product.label || metadata.label || '',
        onboardingLabel:
          product.onboardingLabel || metadata.onboardingLabel || '',
        order: product.order || metadata.order,
        notice: product.notice || metadata.notice || '',
        supplyLength: product.supplyLength || metadata.supplyLength || '',
        weightLossMultiplier:
          product.weightLossMultiplier || metadata.weightLossMultiplier,
        customCard: product.customCard || metadata.customCard || '',
        isAvailableInOnboarding:
          product.isAvailableInOnboarding ??
          metadata.isAvailableInOnboarding ??
          false,
        pharmacyId: product.pharmacy?.id || '',
        productCategoryIds: product.productCategories
          ? product.productCategories.map((pc) => pc.productCategory.id)
          : [],
      });
    }
  }, [
    product,
    duplicateSourceProduct,
    duplicateFromId,
    form,
    isNewProduct,
    setIsEditMode,
  ]);

  const onSubmit = async (formData: {
    name: string;
    description: string | null;
    image?: string;
    genericName?: ProductGenericName;
    form?: ProductForms;
    tags?: string;
    type?: ProductTypes;
    label?: string;
    onboardingLabel?: string;
    order?: number;
    notice?: string;
    supplyLength?: string;
    weightLossMultiplier?: number;
    customCard?: string;
    isAvailableInOnboarding?: boolean;
    pharmacyId: string;
    productCategoryIds: string[];
  }) => {
    setIsSubmitting(true);
    try {
      const data = {
        name: formData.name,
        description: formData.description,
        image: formData.image || imageUrl, // Use imageUrl state as fallback
        genericName: formData.genericName,
        form: formData.form,
        tags: formData.tags,
        type: formData.type,
        label: formData.label,
        onboardingLabel: formData.onboardingLabel,
        order: formData.order,
        notice: formData.notice,
        supplyLength: formData.supplyLength,
        weightLossMultiplier: formData.weightLossMultiplier,
        customCard: formData.customCard,
        isAvailableInOnboarding: formData.isAvailableInOnboarding,
        pharmacyId: formData.pharmacyId,
        productCategoryIds: formData.productCategoryIds,
      };

      if (isNewProduct) {
        await createProduct(data);
        toast({
          title: 'Success',
          description: 'Product created successfully',
        });
        handleClose();
      } else {
        await updateProduct({
          id: productId,
          productData: data,
        });

        // Explicitly refetch the product data to ensure we have the latest information
        await refetch();

        toast({
          title: 'Success',
          description: 'Product updated successfully',
        });
        setIsEditMode(false); // Switch back to view mode after successful update
      }
    } catch (error) {
      console.error('Error saving product:', error);
      toast({
        title: 'Error',
        description: 'Failed to save product',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Function to resize image before upload
  const resizeImage = (
    file: File,
    maxWidth = 800,
    maxHeight = 800,
    quality = 0.7,
  ): Promise<File> => {
    return new Promise((resolve, reject) => {
      // Create a FileReader to read the image
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = (event) => {
        // Create an image element to load the file data
        // Use document.createElement to create an image (avoids TypeScript issue with the Image constructor)
        const img = document.createElement('img');
        img.src = event.target?.result as string;

        img.onload = () => {
          // Create a canvas to resize the image
          const canvas = document.createElement('canvas');
          let width = img.width;
          let height = img.height;

          // Maintain aspect ratio while resizing
          if (width > height) {
            if (width > maxWidth) {
              height = Math.round((height * maxWidth) / width);
              width = maxWidth;
            }
          } else {
            if (height > maxHeight) {
              width = Math.round((width * maxHeight) / height);
              height = maxHeight;
            }
          }

          // Set canvas dimensions and draw resized image
          canvas.width = width;
          canvas.height = height;
          const ctx = canvas.getContext('2d');
          if (!ctx) {
            reject(new Error('Could not get canvas context'));
            return;
          }

          ctx.drawImage(img, 0, 0, width, height);

          // Convert canvas to Blob
          canvas.toBlob(
            (blob) => {
              if (!blob) {
                reject(new Error('Could not create blob'));
                return;
              }

              // Create a new File from the Blob
              const resizedFile = new File([blob], file.name, {
                type: 'image/jpeg',
                lastModified: Date.now(),
              });

              // Check if the file is still too large
              if (resizedFile.size > 400 * 1024) {
                // If still > 400KB
                // Create a new file from the blob for the next iteration
                const newFile = new File([blob], file.name, {
                  type: 'image/jpeg',
                  lastModified: Date.now(),
                });

                // Try with lower quality or dimensions
                resizeImage(
                  newFile,
                  maxWidth * 0.8,
                  maxHeight * 0.8,
                  quality * 0.8,
                )
                  .then(resolve)
                  .catch(() => {
                    // If recursive resize fails, just use what we have
                    console.warn(
                      'Error in recursive resize, using current file',
                    );
                    resolve(resizedFile);
                  });
              } else {
                resolve(resizedFile);
              }
            },
            'image/jpeg',
            quality,
          );
        };

        img.onerror = () => {
          reject(new Error('Failed to load image'));
        };
      };

      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };
    });
  };

  const handleImageUpload = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!/^image\/(jpeg|png|jpg|gif)$/.exec(file.type)) {
      toast({
        title: 'Error',
        description: 'Please select a valid image file (JPEG, PNG, or GIF)',
        variant: 'destructive',
      });
      return;
    }

    // Validate file size
    if (file.size > 5 * 1024 * 1024) {
      // 5MB
      toast({
        title: 'Error',
        description: 'Image size exceeds 5MB limit',
        variant: 'destructive',
      });
      return;
    }

    try {
      // Show uploading state
      toast({
        title: 'Processing',
        description: 'Preparing image for upload...',
      });

      // Resize the image if needed
      let imageToUpload = file;
      if (file.size > 400 * 1024) {
        // If > 400KB, resize it
        imageToUpload = await resizeImage(file);
      }

      // Upload to Stripe to get a temporary URL for preview
      const result = await uploadImage(imageToUpload);

      // Set the image URL in state
      setImageUrl(result.url);
      // Update the form value
      form.setValue('image', result.url);

      toast({
        title: 'Success',
        description: 'Image uploaded successfully',
      });
    } catch (error: unknown) {
      console.error('Error uploading image:', error);
      let errorMessage =
        'Failed to upload image. Please ensure the image is smaller than 512KB.';

      // Check if error is an object with a message property
      if (
        error &&
        typeof error === 'object' &&
        'message' in error &&
        typeof error.message === 'string'
      ) {
        errorMessage = error.message;
      }

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    }
  };

  const handleDeactivate = async () => {
    if (
      !isNewProduct &&
      window.confirm('Are you sure you want to deactivate this product?')
    ) {
      setIsSubmitting(true);
      try {
        await deactivateProduct(productId);
        await refetch(); // Refetch to get updated status
        toast({
          title: 'Success',
          description: 'Product deactivated successfully',
        });
      } catch (error) {
        console.error('Error deactivating product:', error);
        toast({
          title: 'Error',
          description: 'Failed to deactivate product',
          variant: 'destructive',
        });
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const handleReactivate = async () => {
    if (
      !isNewProduct &&
      window.confirm('Are you sure you want to reactivate this product?')
    ) {
      setIsSubmitting(true);
      try {
        await reactivateProduct(productId);
        await refetch(); // Refetch to get updated status
        toast({
          title: 'Success',
          description: 'Product reactivated successfully',
        });
      } catch (error) {
        console.error('Error reactivating product:', error);
        toast({
          title: 'Error',
          description: 'Failed to activate product',
          variant: 'destructive',
        });
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const handleDelete = async () => {
    if (
      !isNewProduct &&
      product?.canBeDeleted &&
      window.confirm(
        'Are you sure you want to delete this product? This action cannot be undone.',
      )
    ) {
      setIsSubmitting(true);
      try {
        await deleteProduct(productId);
        toast({
          title: 'Success',
          description: 'Product deleted successfully',
        });
        handleClose();
      } catch (error) {
        console.error('Error deleting product:', error);
        toast({
          title: 'Error',
          description: 'Failed to delete product',
          variant: 'destructive',
        });
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  // Show loading when either getting product details or duplicate source product
  if (
    (isLoading && !isNewProduct) ||
    (isLoadingDuplicateSource && duplicateFromId)
  ) {
    return (
      <div className="flex h-full w-full items-center justify-center bg-white">
        <Loader className="w-full bg-white" />
      </div>
    );
  }

  // Edit mode for existing products or new product form
  // Product sidebar component

  if (isEditMode) {
    // For existing products, use the split layout
    if (!isNewProduct && product) {
      return (
        <div className="relative flex h-full w-full grow bg-white">
          {/* Use the reusable sidebar component */}
          <ProductSidebar product={product} showMenu={false} />

          {/* Right content area with form */}
          <div className="flex w-2/3 flex-col overflow-scroll">
            <div className="sticky top-0 z-10 bg-white px-10 pt-10">
              <div className="mb-6 flex flex-row justify-between">
                <h2 className="text-xl font-semibold text-denim">
                  Edit Product
                </h2>
                <XIcon
                  size={24}
                  className="cursor-pointer text-denim"
                  onClick={handleClose}
                />
              </div>
            </div>

            <div className="px-10 pb-10">
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-5"
                >
                  <div className="grid grid-cols-1 gap-6">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Product Name</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Image Upload Section */}
                    <div>
                      <FormLabel>Product Image</FormLabel>
                      <div className="mt-2 flex flex-col gap-4">
                        {/* Image Preview */}
                        {(imageUrl || form.getValues('image')) && (
                          <div className="relative h-48 w-48 overflow-hidden rounded-md">
                            <img
                              src={imageUrl || form.getValues('image')}
                              alt="Product Preview"
                              className="h-full w-full object-cover"
                            />
                          </div>
                        )}

                        {/* Upload Button and Field */}
                        <div className="flex flex-col gap-2">
                          <Button
                            type="button"
                            variant="denim"
                            className="w-fit"
                            onClick={() => fileInputRef.current?.click()}
                            disabled={isUploading}
                          >
                            {isUploading
                              ? 'Uploading...'
                              : 'Upload Product Image'}
                          </Button>
                          <input
                            type="file"
                            ref={fileInputRef}
                            className="hidden"
                            accept="image/jpeg,image/png,image/jpg,image/gif"
                            onChange={handleImageUpload}
                          />
                          <FormField
                            control={form.control}
                            name="image"
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input {...field} type="hidden" />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <p className="text-xs text-gray-500">
                            Accepted formats: JPG, PNG, GIF. Max size: 5MB.
                            Images will be automatically resized to fit
                            Stripe&apos;s 512KB limit.
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Pharmacy and Product Categories (skip these at the top) */}
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="pharmacyId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Pharmacy</FormLabel>
                            <FormControl>
                              <select
                                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                {...field}
                              >
                                <option value="">Select a pharmacy</option>
                                {pharmaciesData?.pharmacies.map((pharmacy) => (
                                  <option
                                    key={pharmacy.id}
                                    value={pharmacy.id}
                                    style={{
                                      color: pharmacy.enabled
                                        ? 'inherit'
                                        : '#999',
                                    }}
                                  >
                                    {pharmacy.name}
                                    {!pharmacy.enabled ? ' (Disabled)' : ''}
                                  </option>
                                ))}
                              </select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="productCategoryIds"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Product Categories</FormLabel>
                            <FormControl>
                              <Popover>
                                <PopoverTrigger asChild>
                                  <Button
                                    variant="denimOutline"
                                    role="combobox"
                                    className="w-full justify-between font-normal"
                                  >
                                    {field.value && field.value.length > 0
                                      ? `${field.value.length} selected`
                                      : 'Select categories'}
                                    <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                  </Button>
                                </PopoverTrigger>
                                <PopoverContent className="w-full p-0">
                                  <div className="max-h-[200px] overflow-y-auto p-2">
                                    {categoriesData?.categories.map(
                                      (category) => (
                                        <label
                                          key={category.id}
                                          className="flex cursor-pointer items-center space-x-2 rounded p-2 hover:bg-gray-100"
                                        >
                                          <Checkbox
                                            checked={(
                                              field.value || []
                                            ).includes(category.id)}
                                            onCheckedChange={(checked) => {
                                              const currentValue =
                                                field.value || [];
                                              const newValue = checked
                                                ? [...currentValue, category.id]
                                                : currentValue.filter(
                                                    (id) => id !== category.id,
                                                  );
                                              field.onChange(newValue);
                                            }}
                                          />
                                          <span className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                                            {category.label || category.name}
                                          </span>
                                        </label>
                                      ),
                                    )}
                                  </div>
                                </PopoverContent>
                              </Popover>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Type, Generic Name, and Form - 3 columns */}
                    <div className="grid grid-cols-3 gap-4">
                      <FormField
                        control={form.control}
                        name="type"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Type</FormLabel>
                            <FormControl>
                              <select
                                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                {...field}
                              >
                                <option value="core">Core</option>
                                <option value="additional">Additional</option>
                              </select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="genericName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Generic Name</FormLabel>
                            <FormControl>
                              <select
                                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                {...field}
                              >
                                <option value="semaglutide">Semaglutide</option>
                                <option value="tirzepatide">Tirzepatide</option>
                                <option value="ondansetron">Ondansetron</option>
                              </select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="form"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Form</FormLabel>
                            <FormControl>
                              <select
                                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                {...field}
                              >
                                <option value="injectable">Injectable</option>
                                <option value="oral">Oral</option>
                                <option value="tablet">Tablet</option>
                              </select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Description and Notice */}
                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Textarea
                              {...field}
                              value={field.value || ''}
                              placeholder="Product description"
                              className="min-h-[100px] resize-y"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="notice"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Notice</FormLabel>
                          <FormControl>
                            <Input {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Label and Onboarding Label - 2 columns */}
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="label"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Label</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="onboardingLabel"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Onboarding Label</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Tags and Custom Card - 2 columns */}
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="tags"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Tags</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="customCard"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Custom Card</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Supply Length and Weight Loss Multiplier - 2 columns */}
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="supplyLength"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Supply Length</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="weightLossMultiplier"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Weight Loss Multiplier</FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                type="number"
                                step="0.01"
                                value={field.value || ''}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Order and Available in Onboarding - 2 columns */}
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="order"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Order</FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                type="number"
                                value={field.value || ''}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="isAvailableInOnboarding"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center space-x-3 space-y-0 rounded-md border p-4">
                            <FormControl>
                              <input
                                type="checkbox"
                                className="h-4 w-4"
                                checked={field.value}
                                onChange={(e) =>
                                  field.onChange(e.target.checked)
                                }
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>Available in Onboarding</FormLabel>
                            </div>
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  <div className="flex justify-between border-t pt-6">
                    <div className="flex gap-2">
                      <Button
                        type="button"
                        variant="tertiary"
                        onClick={() => setIsEditMode(false)}
                        disabled={isSubmitting}
                      >
                        Cancel
                      </Button>
                      <Button type="submit" disabled={isSubmitting}>
                        {isSubmitting ? 'Saving...' : 'Update Product'}
                      </Button>
                    </div>
                  </div>
                </form>
              </Form>
            </div>
          </div>
        </div>
      );
    }

    // For new products, use a split layout with a thin column
    return (
      <div className="relative flex h-full w-full grow bg-white">
        {/* Left sidebar - thin column */}
        <div className="flex w-[50px] flex-col bg-stone-light">
          {/* Empty sidebar */}
        </div>

        {/* Right content area with form */}
        <div className="flex w-[calc(100%-50px)] flex-col overflow-scroll">
          <div className="sticky top-0 z-10 bg-white px-10 pt-10">
            <div className="mb-6 flex flex-row justify-between">
              <h2 className="text-xl font-semibold text-denim">
                Add New Product
              </h2>
              <XIcon
                size={24}
                className="cursor-pointer text-denim"
                onClick={handleClose}
              />
            </div>
          </div>

          <div className="px-10 pb-10">
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-5"
              >
                <div className="grid grid-cols-1 gap-6">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Product Name</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Image Upload Section */}
                  <div>
                    <FormLabel>Product Image</FormLabel>
                    <div className="mt-2 flex flex-col gap-4">
                      {/* Image Preview */}
                      {(imageUrl || form.getValues('image')) && (
                        <div className="relative h-48 w-48 overflow-hidden rounded-md">
                          <img
                            src={imageUrl || form.getValues('image')}
                            alt="Product Preview"
                            className="h-full w-full object-cover"
                          />
                        </div>
                      )}

                      {/* Upload Button and Field */}
                      <div className="flex flex-col gap-2">
                        <Button
                          type="button"
                          variant="denim"
                          className="w-fit"
                          onClick={() => fileInputRef.current?.click()}
                          disabled={isUploading}
                        >
                          {isUploading
                            ? 'Uploading...'
                            : 'Upload Product Image'}
                        </Button>
                        <input
                          type="file"
                          ref={fileInputRef}
                          className="hidden"
                          accept="image/jpeg,image/png,image/jpg,image/gif"
                          onChange={handleImageUpload}
                        />
                        <FormField
                          control={form.control}
                          name="image"
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Input {...field} type="hidden" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <p className="text-xs text-gray-500">
                          Accepted formats: JPG, PNG, GIF. Max size: 5MB. Images
                          will be automatically resized to fit Stripe&apos;s
                          512KB limit.
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Pharmacy and Product Categories (skip these at the top) */}
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="pharmacyId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Pharmacy</FormLabel>
                          <FormControl>
                            <select
                              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                              {...field}
                            >
                              <option value="">Select a pharmacy</option>
                              {pharmaciesData?.pharmacies.map((pharmacy) => (
                                <option
                                  key={pharmacy.id}
                                  value={pharmacy.id}
                                  style={{
                                    color: pharmacy.enabled
                                      ? 'inherit'
                                      : '#999',
                                  }}
                                >
                                  {pharmacy.name}
                                  {!pharmacy.enabled ? ' (Disabled)' : ''}
                                </option>
                              ))}
                            </select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="productCategoryIds"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Product Categories</FormLabel>
                          <FormControl>
                            <Popover>
                              <PopoverTrigger asChild>
                                <Button
                                  variant="denimOutline"
                                  role="combobox"
                                  className="w-full justify-between font-normal"
                                >
                                  {field.value && field.value.length > 0
                                    ? `${field.value.length} selected`
                                    : 'Select categories'}
                                  <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-full p-0">
                                <div className="max-h-[200px] overflow-y-auto p-2">
                                  {categoriesData?.categories.map(
                                    (category) => (
                                      <label
                                        key={category.id}
                                        className="flex cursor-pointer items-center space-x-2 rounded p-2 hover:bg-gray-100"
                                      >
                                        <Checkbox
                                          checked={(field.value || []).includes(
                                            category.id,
                                          )}
                                          onCheckedChange={(checked) => {
                                            const currentValue =
                                              field.value || [];
                                            const newValue = checked
                                              ? [...currentValue, category.id]
                                              : currentValue.filter(
                                                  (id) => id !== category.id,
                                                );
                                            field.onChange(newValue);
                                          }}
                                        />
                                        <span className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                                          {category.label || category.name}
                                        </span>
                                      </label>
                                    ),
                                  )}
                                </div>
                              </PopoverContent>
                            </Popover>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Type, Generic Name, and Form - 3 columns */}
                  <div className="grid grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="type"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Type</FormLabel>
                          <FormControl>
                            <select
                              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                              {...field}
                            >
                              <option value="core">Core</option>
                              <option value="additional">Additional</option>
                            </select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="genericName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Generic Name</FormLabel>
                          <FormControl>
                            <select
                              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                              {...field}
                            >
                              <option value="semaglutide">Semaglutide</option>
                              <option value="tirzepatide">Tirzepatide</option>
                              <option value="ondansetron">Ondansetron</option>
                            </select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="form"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Form</FormLabel>
                          <FormControl>
                            <select
                              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                              {...field}
                            >
                              <option value="injectable">Injectable</option>
                              <option value="oral">Oral</option>
                              <option value="tablet">Tablet</option>
                            </select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Description and Notice */}
                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            value={field.value || ''}
                            placeholder="Product description"
                            className="min-h-[100px] resize-y"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="notice"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Notice</FormLabel>
                        <FormControl>
                          <Input {...field} value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Label and Onboarding Label - 2 columns */}
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="label"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Label</FormLabel>
                          <FormControl>
                            <Input {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="onboardingLabel"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Onboarding Label</FormLabel>
                          <FormControl>
                            <Input {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Tags and Custom Card - 2 columns */}
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="tags"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Tags</FormLabel>
                          <FormControl>
                            <Input {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="customCard"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Custom Card</FormLabel>
                          <FormControl>
                            <Input {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Supply Length and Weight Loss Multiplier - 2 columns */}
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="supplyLength"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Supply Length</FormLabel>
                          <FormControl>
                            <Input {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="weightLossMultiplier"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Weight Loss Multiplier</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              type="number"
                              step="0.01"
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Order and Available in Onboarding - 2 columns */}
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="order"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Order</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              type="number"
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="isAvailableInOnboarding"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <input
                              type="checkbox"
                              className="h-4 w-4"
                              checked={field.value}
                              onChange={(e) => field.onChange(e.target.checked)}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>Available in Onboarding</FormLabel>
                          </div>
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <div className="flex justify-between border-t pt-6">
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="tertiary"
                      onClick={handleClose}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting ? 'Saving...' : 'Create Product'}
                    </Button>
                  </div>
                </div>
              </form>
            </Form>
          </div>
        </div>
      </div>
    );
  }

  // View mode for existing products with new split layout
  if (!isEditMode && !isNewProduct && product) {
    return (
      <div className="relative flex h-full w-full grow bg-white">
        {/* Use the reusable sidebar component with menu */}
        <ProductSidebar
          product={product}
          setIsEditMode={setIsEditMode}
          handleDeactivate={handleDeactivate}
          handleReactivate={handleReactivate}
          handleDelete={handleDelete}
        />

        {/* Right content area with tabs */}
        <div className="flex w-2/3 flex-col overflow-scroll">
          <Tabs
            defaultValue={selectedTab}
            onValueChange={setSelectedTab}
            className="relative grid w-full grid-rows-[auto_1fr]"
          >
            <div className="sticky top-0 z-10 bg-white px-10 pt-10">
              <div className="flex flex-row justify-end text-denim">
                <XIcon
                  size={24}
                  className="cursor-pointer"
                  onClick={handleClose}
                />
              </div>
              <TabsList className="h-fit w-full justify-start gap-5 rounded-none border-b-[1px] border-denim-muted bg-white p-0 text-denim">
                <TabsTrigger
                  className="rounded-none text-xs font-normal text-muted-foreground data-[state=active]:border-b-[3px] data-[state=active]:border-denim data-[state=active]:text-denim data-[state=active]:shadow-none"
                  value="details"
                >
                  Details
                </TabsTrigger>
                <TabsTrigger
                  className="rounded-none text-xs font-normal text-muted-foreground data-[state=active]:border-b-[3px] data-[state=active]:border-denim data-[state=active]:text-denim data-[state=active]:shadow-none"
                  value="prices"
                >
                  Prices
                </TabsTrigger>
              </TabsList>
            </div>

            {/* Details tab content */}
            <TabsContent className="px-10" value="details">
              <div className="mb-5 mt-10 space-y-6">
                <div className="text-2xl font-medium text-denim">
                  Product Details
                </div>

                {/* Product Image */}
                {product.image && (
                  <div className="flex justify-center">
                    <div className="relative h-48 w-48 overflow-hidden rounded-md">
                      <img
                        src={product.image}
                        alt={product.name}
                        className="h-full w-full object-cover"
                      />
                    </div>
                  </div>
                )}

                {/* Product Description */}
                <div className="space-y-2">
                  <h3 className="text-lg font-medium text-denim">
                    Description
                  </h3>
                  <div className="rounded-lg border p-4 shadow-sm">
                    <p className="text-sm">
                      {product.description || 'No description provided'}
                    </p>
                  </div>
                </div>

                {/* Product Notice (only if present) */}
                {product.notice && (
                  <div className="space-y-2">
                    <h3 className="text-lg font-medium text-denim">Notice</h3>
                    <div className="rounded-lg border p-4 shadow-sm">
                      <p className="text-sm">{product.notice}</p>
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            {/* Prices tab content */}
            <TabsContent className="px-10" value="prices">
              <div className="mb-5 mt-10 space-y-6">
                <div className="text-2xl font-medium text-denim">
                  Product Prices
                </div>

                {/* All Prices List */}
                <div className="mb-8">
                  {product.productPrice && product.productPrice.length > 0 ? (
                    <ul className="space-y-3">
                      {product.productPrice.map((price) => (
                        <li key={price.id} className="border-b pb-3">
                          <Link
                            href={`/product-prices?productId=${product.id}&priceId=${price.id}`}
                            className="flex items-center justify-between rounded-md p-2 hover:bg-gray-50"
                          >
                            <div className="flex items-center">
                              <span className="text-sm font-medium">
                                {price.name}
                              </span>
                              {product.defaultPrice &&
                                product.defaultPrice.id === price.id && (
                                  <span className="ml-2 inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                                    Default
                                  </span>
                                )}
                              {!price.active && (
                                <span className="ml-2 inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                                  Inactive
                                </span>
                              )}
                            </div>
                            <span className="text-sm font-semibold">
                              ${(price.unit_amount / 100).toFixed(2)}
                            </span>
                          </Link>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="mb-4 text-sm text-gray-500">
                      No prices available for this product
                    </p>
                  )}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    );
  }

  return null;
}
