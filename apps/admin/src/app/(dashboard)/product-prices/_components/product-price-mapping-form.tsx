import { useEffect, useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { PlusIcon, Trash2Icon, XIcon } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { But<PERSON> } from '@willow/ui/base/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@willow/ui/base/form';
import { Input } from '@willow/ui/base/input';
import { useToast } from '@willow/ui/base/use-toast';
import { Loader } from '@willow/ui/loader';

import type { CreateProductPriceMappingDto } from '~/hooks/product-price-mappings';
import {
  useCreateProductPriceMapping,
  useGetProductPriceMapping,
  useUpdateProductPriceMapping,
} from '~/hooks/product-price-mappings';

const mappingFormSchema = z
  .object({
    externalId: z.string().min(1, 'External ID is required'),
    name: z.string().optional(),
  })
  .refine(
    (data) => {
      // Custom validation will be handled in the component for metadata
      return true;
    },
    {
      message: 'Invalid metadata',
    },
  );

type FormValues = z.infer<typeof mappingFormSchema>;

interface MetadataEntry {
  key: string;
  value: string;
}

interface ProductPriceMappingFormProps {
  productPriceId: string;
  mappingId?: string; // If provided, we're in edit mode
  onSuccess: () => void;
  onCancel: () => void;
}

export function ProductPriceMappingForm({
  productPriceId,
  mappingId,
  onSuccess,
  onCancel,
}: ProductPriceMappingFormProps) {
  const isEditMode = !!mappingId;
  const { toast } = useToast();
  const { data: mapping, isLoading: isLoadingMapping } =
    useGetProductPriceMapping(mappingId || '');
  const { mutateAsync: createMapping, isPending: isCreating } =
    useCreateProductPriceMapping();
  const { mutateAsync: updateMapping, isPending: isUpdating } =
    useUpdateProductPriceMapping();

  const [metadataEntries, setMetadataEntries] = useState<MetadataEntry[]>([]);

  const isSubmitting = isCreating || isUpdating;
  const isLoading = isEditMode ? isLoadingMapping : false;

  const form = useForm<FormValues>({
    resolver: zodResolver(mappingFormSchema),
    defaultValues: {
      externalId: '',
      name: '',
    },
  });

  useEffect(() => {
    if (mapping && isEditMode) {
      const metadata = mapping.metadata! || {};
      const entries = Object.entries(metadata).map(([key, value]) => ({
        key,
        value: String(value || ''), // Convert to string and handle null/undefined
      }));

      setMetadataEntries(entries);
      form.reset({
        externalId: mapping.externalId,
        name: mapping.name || '',
      });
    }
  }, [mapping, form, isEditMode]);

  const addMetadataEntry = () => {
    setMetadataEntries([...metadataEntries, { key: '', value: '' }]);
  };

  const removeMetadataEntry = (index: number) => {
    setMetadataEntries(metadataEntries.filter((_, i) => i !== index));
  };

  const updateMetadataEntry = (
    index: number,
    field: 'key' | 'value',
    value: string,
  ) => {
    const newEntries = [...metadataEntries];
    if (newEntries[index]) {
      newEntries[index][field] = value;
      setMetadataEntries(newEntries);
    }
  };

  const onSubmit = async (formData: FormValues) => {
    // Validate metadata: if key is provided, value must also be provided
    const invalidEntries = metadataEntries.filter(
      (entry) =>
        String(entry.key || '').trim() !== '' &&
        String(entry.value || '').trim() === '',
    );

    if (invalidEntries.length > 0) {
      toast({
        title: 'Validation Error',
        description: 'All metadata keys must have corresponding values.',
        variant: 'destructive',
      });
      return;
    }

    try {
      // Convert metadata entries to object, filtering out empty keys
      const metadata = metadataEntries
        .filter((entry) => String(entry.key || '').trim() !== '')
        .reduce(
          (acc, entry) => {
            acc[String(entry.key || '').trim()] = String(entry.value || '');
            return acc;
          },
          {} as Record<string, string>,
        );

      if (isEditMode && mappingId) {
        // Update existing mapping
        await updateMapping({
          mappingId,
          mappingData: {
            externalId: formData.externalId,
            name: formData.name,
            metadata,
          },
        });
        toast({
          title: 'Success',
          description: 'Mapping updated successfully',
        });
      } else {
        // Create new mapping
        const createData: CreateProductPriceMappingDto = {
          externalId: formData.externalId,
          name: formData.name,
          productPriceId,
          metadata,
        };

        await createMapping(createData);
        toast({
          title: 'Success',
          description: 'Mapping created successfully',
        });
      }
      onSuccess();
    } catch (error) {
      console.error('Error submitting form:', error);
      toast({
        title: 'Error',
        description: `Failed to ${isEditMode ? 'update' : 'create'} mapping`,
        variant: 'destructive',
      });
    }
  };

  if (isLoading) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <Loader />
      </div>
    );
  }

  return (
    <div className="relative flex h-full w-full flex-col overflow-x-hidden bg-white p-6">
      <div className="mb-4 flex items-center justify-between">
        <h2 className="text-xl font-semibold text-denim">
          {isEditMode ? 'Edit Mapping' : 'Add Mapping'}
        </h2>
        <XIcon
          size={24}
          className="cursor-pointer text-denim"
          onClick={onCancel}
        />
      </div>

      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex w-full max-w-full flex-col space-y-4"
        >
          {/* External ID */}
          <FormField
            control={form.control}
            name="externalId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>External ID*</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="External system ID"
                    className="max-w-full text-ellipsis"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Name */}
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Name</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="Basic product description"
                    className="max-w-full text-ellipsis"
                  />
                </FormControl>
                <FormMessage />
                <p className="mt-1 text-xs text-gray-500">
                  Leave blank for the actual product mapping, otherwise use a
                  basic product description, like "syringe"
                </p>
              </FormItem>
            )}
          />

          {/* Metadata Section */}
          <div className="rounded-lg border border-gray-200 p-4">
            <div className="mb-3 flex items-center justify-between">
              <FormLabel className="text-base font-medium">Metadata</FormLabel>
              <Button
                type="button"
                variant="denimOutline"
                size="sm"
                onClick={addMetadataEntry}
                className="flex items-center gap-1"
              >
                <PlusIcon size={16} />
                Add Row
              </Button>
            </div>

            <div className="space-y-2">
              {metadataEntries.map((entry, index) => {
                const hasKeyWithoutValue =
                  String(entry.key || '').trim() !== '' &&
                  String(entry.value || '').trim() === '';

                return (
                  <div key={index} className="flex items-center gap-2">
                    <Input
                      placeholder="key"
                      value={entry.key}
                      onChange={(e) =>
                        updateMetadataEntry(index, 'key', e.target.value)
                      }
                      className="flex-1"
                    />
                    <Input
                      placeholder="value"
                      value={entry.value}
                      onChange={(e) =>
                        updateMetadataEntry(index, 'value', e.target.value)
                      }
                      className={`flex-1 ${hasKeyWithoutValue ? 'border-red-500 focus:border-red-500' : ''}`}
                    />
                    <Button
                      type="button"
                      variant="destructive"
                      size="icon"
                      onClick={() => removeMetadataEntry(index)}
                      className="flex-shrink-0"
                    >
                      <Trash2Icon size={16} className="text-red-500" />
                    </Button>
                  </div>
                );
              })}

              {metadataEntries.length === 0 && (
                <p className="py-4 text-center text-sm text-gray-500">
                  No metadata entries. Click "Add Row" to add key-value pairs.
                </p>
              )}
            </div>
          </div>

          <div className="mt-6 flex justify-end space-x-2 overflow-hidden pt-4">
            <Button type="button" onClick={onCancel} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting
                ? 'Saving...'
                : isEditMode
                  ? 'Update Mapping'
                  : 'Add Mapping'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
