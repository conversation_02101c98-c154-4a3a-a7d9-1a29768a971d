'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useQueryClient } from '@tanstack/react-query';
import { PencilIcon, PlusIcon } from 'lucide-react';

import { Button } from '@willow/ui/base/button';
import { DrawerTitle } from '@willow/ui/base/drawer';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@willow/ui/base/table';
import {
  Drawer,
  DrawerContent,
  DrawerOverlay,
  DrawerPortal,
} from '@willow/ui/base/vaul';
import { Loader } from '@willow/ui/loader';

import { useGetProductPriceEquivalenceByProductPriceId } from '~/hooks/product-price-equivalence';
import { useGetProductPrice } from '~/hooks/product-prices';
import { useGetProduct } from '~/hooks/products';
import { ProductPriceEquivalenceForm } from '../../pharmacies/_components/product-price-equivalence-form';

interface ProductPriceEquivalenceListProps {
  productPriceId: string;
}

export function ProductPriceEquivalenceList({
  productPriceId,
}: ProductPriceEquivalenceListProps) {
  const [selectedMappingId, setSelectedMappingId] = useState<
    string | undefined
  >();
  const [formMode, setFormMode] = useState<'create' | 'edit' | 'view'>(
    'create',
  );
  const queryClient = useQueryClient();

  const {
    data: equivalenceGroup,
    isLoading: isLoadingEquivalence,
    isError: isErrorEquivalence,
  } = useGetProductPriceEquivalenceByProductPriceId(productPriceId);

  const {
    data: productPrice,
    isLoading: isLoadingProductPrice,
    isError: isErrorProductPrice,
  } = useGetProductPrice('', productPriceId);

  // Get the product to access the pharmacy ID
  const {
    data: product,
    isLoading: isLoadingProduct,
    isError: isErrorProduct,
  } = useGetProduct(productPrice?.productId || '');

  // Listen for edit events from the form component
  useEffect(() => {
    const handleOpenMappingEdit = (event: Event) => {
      const customEvent = event as CustomEvent<{ mappingId: string }>;
      if (customEvent.detail?.mappingId) {
        setSelectedMappingId(customEvent.detail.mappingId);
        setFormMode('edit');
      }
    };

    window.addEventListener('open-mapping-edit', handleOpenMappingEdit);

    return () => {
      window.removeEventListener('open-mapping-edit', handleOpenMappingEdit);
    };
  }, []);

  const isLoading =
    isLoadingEquivalence || isLoadingProductPrice || isLoadingProduct;
  const isError = isErrorEquivalence || isErrorProductPrice || isErrorProduct;

  if (isLoading) {
    return (
      <div className="flex h-40 items-center justify-center">
        <Loader />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex h-40 items-center justify-center rounded-md border border-dashed p-4 text-center text-sm text-red-500">
        Error loading price mappings. Please try again.
      </div>
    );
  }

  if (!equivalenceGroup) {
    return (
      <div className="flex h-40 flex-col items-center justify-center gap-4 rounded-md border border-dashed p-4 text-center text-sm text-gray-500">
        <div>This product price is not part of any equivalence group.</div>
        {product?.pharmacy?.id && (
          <Link
            href={`/pharmacies/all?pharmacyId=${product.pharmacy.id}&tab=mappings&mappingId=new&formMode=create`}
            passHref
          >
            <Button size="sm" className="flex items-center gap-2">
              <PlusIcon className="h-4 w-4" />
              Add Equivalence Group
            </Button>
          </Link>
        )}
      </div>
    );
  }

  return (
    <div className="flex flex-col space-y-4">
      <div className="flex flex-col gap-2">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-denim">Equivalence Group</h3>
          <Button
            size="sm"
            onClick={() => {
              // Invalidate cache to ensure fresh data
              void queryClient.invalidateQueries({
                queryKey: ['product-price-equivalence', equivalenceGroup.id],
              });
              setSelectedMappingId(equivalenceGroup.id);
              setFormMode('edit');
            }}
            className="flex items-center gap-2"
          >
            <PencilIcon className="h-4 w-4" />
            Edit Mapping
          </Button>
        </div>
        <div className="rounded-md border border-gray-200 bg-gray-50 p-4">
          <div className="mb-2 text-sm font-medium">Group Name</div>
          <div className="text-sm">{equivalenceGroup.name}</div>
        </div>
      </div>

      <div className="flex flex-col gap-2">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-denim">
            Equivalent Product Prices
          </h3>
        </div>
        {equivalenceGroup.productPrices.length === 0 ? (
          <div className="flex h-40 items-center justify-center rounded-md border border-dashed p-4 text-center text-sm text-gray-500">
            No equivalent product prices found.
          </div>
        ) : (
          <div className="overflow-hidden rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product Price</TableHead>
                  <TableHead>Product</TableHead>
                  <TableHead>Pharmacy</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {equivalenceGroup.productPrices.map((price) => {
                  // Get the pharmacy from the product
                  const pharmacy = price.product?.pharmacy;

                  return (
                    <TableRow
                      key={price.id}
                      className={
                        price.id === productPriceId ? 'bg-blue-50' : ''
                      }
                    >
                      <TableCell className="font-medium">
                        {price.id === productPriceId ? (
                          <span className="flex items-center gap-2">
                            {price.name}
                          </span>
                        ) : (
                          <Link
                            href={`/product-prices?productId=${price.product?.id || ''}&priceId=${price.id}&&tab=price-mappings`}
                            className="text-denim hover:text-denim-light hover:underline"
                          >
                            {price.name}
                          </Link>
                        )}
                      </TableCell>
                      <TableCell>
                        {price.product
                          ? `${price.product.label} - ${price.product.form}`
                          : 'Not specified'}
                      </TableCell>
                      <TableCell>
                        {pharmacy ? (
                          <Link
                            href={`/pharmacies/all?pharmacyId=${pharmacy.id}`}
                            className="text-denim hover:text-denim-light hover:underline"
                          >
                            {pharmacy.name}
                          </Link>
                        ) : (
                          'Not specified'
                        )}
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        )}
      </div>

      <Drawer
        direction="right"
        open={!!selectedMappingId}
        onOpenChange={(value) => {
          if (!value) setSelectedMappingId(undefined);
        }}
        modal={true}
      >
        <DrawerPortal>
          <DrawerOverlay className="fixed inset-0 z-10 bg-dark/40" />
          <DrawerContent className="fixed bottom-0 right-0 top-0 z-20 flex w-[600px] !touch-none !select-text shadow-xl">
            <DrawerTitle className="hidden">
              Product Price Equivalence Group
            </DrawerTitle>
            <ProductPriceEquivalenceForm
              pharmacyId={
                equivalenceGroup.productPrices[0]?.product?.pharmacy?.id || ''
              }
              equivalenceId={selectedMappingId}
              isOpen={true}
              mode={formMode}
              onClose={() => {
                setSelectedMappingId(undefined);
                setFormMode('create');
              }}
            />
          </DrawerContent>
        </DrawerPortal>
      </Drawer>
    </div>
  );
}
