'use client';

import { useEffect, useMemo, useState } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import Big from 'big.js';
import { format } from 'date-fns';
import {
  EllipsisIcon,
  ExternalLinkIcon,
  InfoIcon,
  LinkIcon,
  XIcon,
} from 'lucide-react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { cn } from '@willow/ui';
import { Button } from '@willow/ui/base/button';
import { Checkbox } from '@willow/ui/base/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@willow/ui/base/dropdown-menu';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@willow/ui/base/form';
import { Input } from '@willow/ui/base/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@willow/ui/base/tabs';
import { useToast } from '@willow/ui/base/use-toast';
import { Loader } from '@willow/ui/loader';

import { env } from '~/env';
import { useGetPharmacies } from '~/hooks/pharmacies';
import {
  useCreateProductPrice,
  useDeactivateProductPrice,
  useDeleteProductPrice,
  useGetProductPrice,
  useReactivateProductPrice,
  useReplaceProductPrice,
  useUpdateProductPrice,
} from '~/hooks/product-prices';
import { useGetProducts } from '~/hooks/products';
import { ProductPriceEquivalenceList } from './product-price-equivalence-list';
import { ProductPriceMappingsList } from './product-price-mappings-list';

interface ActionType {
  name: string;
  handleAction?: () => void;
  className?: string;
}

interface PriceMiscMenuProps {
  price: any;
  productId: string;
  setIsEditMode: (value: boolean) => void;
  handleDeactivate: () => Promise<void>;
  handleReactivate: () => Promise<void>;
  handleDelete?: () => void;
}

function PriceMiscMenu({
  price,
  productId,
  setIsEditMode,
  handleDeactivate,
  handleReactivate,
  handleDelete,
}: PriceMiscMenuProps) {
  const isActive = price.active;

  const actions: ActionType[] = useMemo(() => {
    const menuItems = [
      {
        name: 'Edit Price',
        handleAction: () => setIsEditMode(true),
      },
      {
        name: 'Duplicate Price',
        handleAction: () => {
          // Navigate to the new price form with duplicate parameter
          const params = new URLSearchParams();
          params.set('productId', productId);
          params.set('priceId', 'new');
          params.set('duplicate', price.id);
          window.location.href = `/product-prices?${params.toString()}`;
        },
      },
      { name: 'separator-1' },
      {
        name: isActive ? 'Deactivate Price' : 'Activate Price',
        className: isActive ? 'text-red-500' : undefined,
        handleAction: () => {
          if (isActive) {
            void handleDeactivate();
          } else {
            void handleReactivate();
          }
        },
      },
    ];

    if (handleDelete) {
      menuItems.push(
        { name: 'separator-2' },
        {
          name: 'Delete Price',
          className: 'text-red-700 font-semibold',
          handleAction: handleDelete,
        },
      );
    }

    return menuItems;
  }, [
    isActive,
    setIsEditMode,
    handleDeactivate,
    handleReactivate,
    handleDelete,
  ]);

  return (
    <div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <div className="cursor-pointer">
            <EllipsisIcon size={24} color="#2F4C78" />
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-44 px-0" align="end">
          {actions.map((action) => {
            if (action.name.includes('separator'))
              return (
                <DropdownMenuSeparator
                  key={action.name}
                  className="mx-auto w-10/12"
                />
              );
            return (
              <DropdownMenuItem
                key={action.name}
                onSelect={() => {
                  if (action.handleAction) {
                    action.handleAction();
                  }
                }}
                className={cn(
                  'cursor-pointer px-4 py-3 text-xs font-normal focus:bg-[#ffecca] focus:text-[#2f4c78]',
                  action.className,
                )}
              >
                {action.name}
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}

interface ProductPriceInfoProps {
  productId: string;
  priceId: string;
  handleClose: () => void;
}

const createProductPriceSchema = z.object({
  name: z.string().min(1, 'Price name is required'),
  description: z.string().optional(),
  unit_amount: z.coerce
    .number({
      required_error: 'Amount is required',
      invalid_type_error: 'Amount must be a number',
    })
    .min(0, 'Price must be a positive number')
    .transform((val) => Math.round(val * 100)), // Convert to cents
  active: z.boolean().optional().default(false), // Default to inactive
  isDefaultPrice: z.boolean().optional().default(false),
  dosageDescription: z.string().optional(),
  dosageLabel: z.string().optional(),
  dosageTimeframe: z.string().optional(),
  dosageAdditionalMessage: z.string().optional(),
  compoundName: z.string().optional(),
  patientDirections: z.string().optional(),
  label: z.string().optional(),
  milligrams: z.coerce.number().optional(),
  phase: z.coerce.number().optional(),
  productId: z.string().optional(),
  additiveBenefit: z.string().optional(),
});

const updateProductPriceSchema = z.object({
  name: z.string().min(1, 'Price name is required'),
  description: z.string().optional(),
  unit_amount: z.coerce
    .number({
      required_error: 'Amount is required',
      invalid_type_error: 'Amount must be a number',
    })
    .min(0, 'Price must be a positive number')
    .transform((val) => Math.round(val * 100)), // Convert to cents
  active: z.boolean().optional(), // Allow toggling active status
  isDefaultPrice: z.boolean().optional().default(false),
  dosageDescription: z.string().optional(),
  dosageLabel: z.string().optional(),
  dosageTimeframe: z.string().optional(),
  dosageAdditionalMessage: z.string().optional(),
  compoundName: z.string().optional(),
  patientDirections: z.string().optional(),
  label: z.string().optional(),
  milligrams: z.coerce.number().optional(),
  phase: z.coerce.number().optional(),
  additiveBenefit: z.string().optional(),
});

interface ProductPriceSidebarProps {
  price: any; // Use proper type here if available
  productId?: string;
  showMenu?: boolean;
  setIsEditMode?: (isEdit: boolean) => void;
  handleDeactivate?: () => Promise<void>;
  handleReactivate?: () => Promise<void>;
  deleteProductPrice?: any;
  handleClose?: () => void;
  setIsSubmitting?: (isSubmitting: boolean) => void;
  toast?: any;
}

function ProductPriceSidebar({
  price,
  productId,
  showMenu = true,
  setIsEditMode,
  handleDeactivate,
  handleReactivate,
  deleteProductPrice,
  handleClose,
  setIsSubmitting,
  toast,
}: ProductPriceSidebarProps) {
  const stripeBaseUrl = useMemo(() => {
    return env.NEXT_PUBLIC_ENVIRONMENT === 'production'
      ? 'https://dashboard.stripe.com/prices/'
      : 'https://dashboard.stripe.com/test/prices/';
  }, []);

  const handleDelete =
    price.canBeDeleted &&
    showMenu &&
    setIsEditMode &&
    handleDeactivate &&
    handleReactivate &&
    deleteProductPrice &&
    handleClose &&
    setIsSubmitting &&
    toast
      ? () => {
          if (
            window.confirm(
              'Are you sure you want to delete this price? This action cannot be undone.',
            )
          ) {
            setIsSubmitting(true);
            deleteProductPrice({ productId, priceId: price.id })
              .then(() => {
                toast({
                  title: 'Success',
                  description: 'Product price deleted successfully',
                });
                handleClose();
              })
              .catch((error: any) => {
                console.error('Error deleting product price:', error);
                toast({
                  title: 'Error',
                  description: 'Failed to delete product price',
                  variant: 'destructive',
                });
              })
              .finally(() => {
                setIsSubmitting(false);
              });
          }
        }
      : undefined;

  return (
    <div className="flex w-[270px] flex-none flex-col justify-between overflow-scroll bg-stone-light px-6 py-10">
      <div className="flex flex-col gap-6">
        <div className="flex justify-between">
          <div className="flex flex-row items-center gap-4">
            <div>
              {showMenu ? (
                <>
                  <div className="text-base font-medium text-dark">
                    {price.product?.label}
                  </div>
                  <div className="text-sm text-dark">{price.dosageLabel}</div>
                  <div className="flex flex-col text-[11px] font-medium text-stone/70">
                    {price.isDefaultPrice && <span>Default Price</span>}
                    {price.product && (
                      <span>
                        <Link
                          href={`/products/all?productId=${price.product.id}`}
                          className="text-denim hover:underline"
                        >
                          {price.product.label}
                        </Link>
                        ,{' '}
                        <Link
                          href={`/pharmacies/all?pharmacyId=${price.product?.pharmacy?.id}`}
                          className="text-denim hover:underline"
                        >
                          {price.product?.pharmacy?.name}
                        </Link>
                      </span>
                    )}
                  </div>
                </>
              ) : (
                <>
                  <div className="text-base font-medium text-dark">
                    {price.name}
                  </div>
                  {price.isDefaultPrice && (
                    <div className="flex flex-col text-[11px] font-medium text-stone/70">
                      <span>Default Price</span>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
          {showMenu &&
            setIsEditMode &&
            handleDeactivate &&
            handleReactivate && (
              <PriceMiscMenu
                price={price}
                productId={productId || ''}
                setIsEditMode={setIsEditMode}
                handleDeactivate={handleDeactivate}
                handleReactivate={handleReactivate}
                handleDelete={handleDelete}
              />
            )}
        </div>

        <div className="flex flex-col gap-4">
          <div className="text-sm font-normal text-orange">
            Basic Information
          </div>
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">Status</div>
            <div className="text-xs font-normal text-dark">
              {price.active ? (
                <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                  Active
                </span>
              ) : (
                <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                  Inactive
                </span>
              )}
            </div>
          </div>
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">Stripe</div>
            <Link
              href={`${stripeBaseUrl}${price.id}`}
              target="_blank"
              className="text-xs text-denim hover:underline"
            >
              {price.name}
            </Link>
          </div>
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">Amount</div>
            <div className="text-xs font-normal text-dark">
              ${(price.unit_amount / 100).toFixed(2)} USD
            </div>
          </div>
          <div className="text-sm font-normal text-orange">
            Display Settings
          </div>
          {price.label && (
            <div className="flex flex-col gap-1">
              <div className="text-xs font-medium text-stone/70">Label</div>
              <div className="text-xs font-normal text-dark">{price.label}</div>
            </div>
          )}
          {price.dosageLabel && (
            <div className="flex flex-col gap-1">
              <div className="text-xs font-medium text-stone/70">
                Dosage Label
              </div>
              <div className="text-xs font-normal text-dark">
                {price.dosageLabel}
              </div>
            </div>
          )}
          {price.dosageDescription && (
            <div className="flex flex-col gap-1">
              <div className="text-xs font-medium text-stone/70">
                Dosage Description
              </div>
              <div className="text-xs font-normal text-dark">
                {price.dosageDescription}
              </div>
            </div>
          )}
          {price.dosageTimeframe && (
            <div className="flex flex-col gap-1">
              <div className="text-xs font-medium text-stone/70">
                Dosage Timeframe
              </div>
              <div className="text-xs font-normal text-dark">
                {price.dosageTimeframe}
              </div>
            </div>
          )}
          {price.dosageAdditionalMessage && (
            <div className="flex flex-col gap-1">
              <div className="text-xs font-medium text-stone/70">
                Dosage Additional Message
              </div>
              <div className="text-xs font-normal text-dark">
                {price.dosageAdditionalMessage}
              </div>
            </div>
          )}
          <div className="text-sm font-normal text-orange">
            Additional Details
          </div>
          {price.milligrams !== undefined && price.milligrams !== null && (
            <div className="flex flex-col gap-1">
              <div className="text-xs font-medium text-stone/70">
                Milligrams
              </div>
              <div className="text-xs font-normal text-dark">
                {price.milligrams}
              </div>
            </div>
          )}
          {price.phase !== undefined && price.phase !== null && (
            <div className="flex flex-col gap-1">
              <div className="text-xs font-medium text-stone/70">Phase</div>
              <div className="text-xs font-normal text-dark">{price.phase}</div>
            </div>
          )}
          {price.created && (
            <div className="flex flex-col gap-1">
              <div className="text-xs font-medium text-stone/70">
                Created At
              </div>
              <div className="text-xs font-normal text-dark">
                {format(new Date(price.created), 'MMM dd, yyyy')}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export function ProductPriceInfo({
  productId,
  priceId,
  handleClose,
}: ProductPriceInfoProps) {
  const isNewPrice = priceId === 'new';
  const router = useRouter();
  const searchParams = useSearchParams();
  const defaultTab = searchParams.get('tab') || 'details';
  const duplicateFromId = searchParams.get('duplicate');

  const {
    data: price,
    isLoading,
    refetch,
  } = useGetProductPrice(productId, isNewPrice ? '' : priceId);

  // Get the source price data if we're duplicating
  const { data: duplicateSourcePrice, isLoading: isLoadingDuplicateSource } =
    useGetProductPrice(productId, duplicateFromId || '');

  // For duplicate form, get list of pharmacies
  const { data: pharmaciesData, isLoading: isLoadingPharmacies } =
    useGetPharmacies({
      limit: 100,
      showInactive: true,
    });

  // State for pharmacy and product selection in duplicate flow
  const [selectedPharmacyId, setSelectedPharmacyId] = useState<
    string | undefined
  >(isNewPrice && duplicateFromId ? '' : productId);

  // Get products for the selected pharmacy
  const { data: productsData, isLoading: isLoadingProducts } = useGetProducts({
    limit: 100,
    showInactive: true,
  });

  // Filter products by selected pharmacy
  const filteredProducts = useMemo(() => {
    if (!productsData?.products || !selectedPharmacyId) return [];
    return productsData.products.filter(
      (product) => product.pharmacy?.id === selectedPharmacyId,
    );
  }, [productsData, selectedPharmacyId]);
  const { mutateAsync: createProductPrice } = useCreateProductPrice();
  const { mutateAsync: updateProductPrice } = useUpdateProductPrice();
  const { mutateAsync: deactivateProductPrice } = useDeactivateProductPrice();
  const { mutateAsync: reactivateProductPrice } = useReactivateProductPrice();
  const { mutateAsync: deleteProductPrice } = useDeleteProductPrice();
  const { mutateAsync: replaceProductPrice } = useReplaceProductPrice();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isEditMode, setIsEditMode] = useState(isNewPrice);
  const [isPriceEditEnabled, setIsPriceEditEnabled] = useState(false);
  const [originalPrice, setOriginalPrice] = useState<number | null>(null);

  // Function to update URL with selected tab
  const updateTabInURL = (tab: string) => {
    // Create a new URLSearchParams
    const params = new URLSearchParams(searchParams.toString());
    params.set('tab', tab);

    // Update the URL without triggering a navigation
    router.replace(`?${params.toString()}`, { scroll: false });
  };

  const form = useForm<{
    name: string;
    unit_amount: number;
    productId?: string;
    description?: string;
    active?: boolean;
    dosageDescription?: string;
    dosageLabel?: string;
    dosageTimeframe?: string;
    dosageAdditionalMessage?: string;
    compoundName?: string;
    patientDirections?: string;
    label?: string;
    milligrams?: number;
    phase?: number;
    isDefaultPrice?: boolean;
    pharmacy?: string;
    additiveBenefit?: string;
  }>({
    resolver: zodResolver(
      isNewPrice
        ? duplicateFromId
          ? createProductPriceSchema.refine(
              (data) => {
                // First check if pharmacy is selected
                if (!selectedPharmacyId) {
                  return false;
                }
                // Then check if product is selected
                return !!data.productId;
              },
              {
                message: selectedPharmacyId
                  ? 'Product is required when duplicating a price'
                  : 'Pharmacy is required when duplicating a price',
                path: selectedPharmacyId ? ['productId'] : ['pharmacy'],
              },
            )
          : createProductPriceSchema
        : isPriceEditEnabled
          ? updateProductPriceSchema.refine(
              (data) => {
                // When editing price, ensure it's different from original
                if (originalPrice !== null && data.unit_amount !== undefined) {
                  // Use Big.js for precise comparison
                  const newPriceInCents = new Big(data.unit_amount)
                    .times(100)
                    .round()
                    .toNumber();
                  const originalPriceInCents = new Big(originalPrice)
                    .times(100)
                    .round()
                    .toNumber();
                  return newPriceInCents !== originalPriceInCents;
                }
                return true;
              },
              {
                message: 'New price must be different from the current price',
                path: ['unit_amount'],
              },
            )
          : updateProductPriceSchema,
    ),
    defaultValues: isNewPrice
      ? {
          name: '',
          description: '',
          unit_amount: 0, // Explicitly set to 0 to ensure it's not blank
          active: false, // Default to inactive
          isDefaultPrice: false,
          productId: isNewPrice && duplicateFromId ? '' : productId,
        }
      : undefined,
  });

  useEffect(() => {
    // For existing prices (not new and not duplicate), use the price data
    if (price && !isNewPrice && !duplicateFromId) {
      // Extract values from metadata if they exist there
      const metadata = price.metadata || {};
      // Use Big.js for precise conversion from cents to dollars
      const priceInDollars = Number(
        new Big(price.unit_amount).div(100).toFixed(2),
      );

      form.reset({
        name: price.name || '',
        unit_amount: priceInDollars, // Convert from cents to dollars for display
        description:
          price.description || (metadata.description as string) || '',
        active: price.active, // Include active status
        isDefaultPrice: price.isDefaultPrice || false,
        dosageDescription:
          price.dosageDescription ||
          (metadata.dosageDescription as string) ||
          '',
        dosageLabel:
          price.dosageLabel || (metadata.dosageLabel as string) || '',
        dosageTimeframe:
          price.dosageTimeframe || (metadata.dosageTimeframe as string) || '',
        dosageAdditionalMessage:
          price.dosageAdditionalMessage ||
          (metadata.dosageAdditionalMessage as string) ||
          '',
        compoundName: price.compoundName || '',
        patientDirections: price.patientDirections || '',
        label: price.label || (metadata.label as string) || '',
        milligrams:
          price.milligrams || (metadata.milligrams as number | undefined),
        phase: price.phase || (metadata.phase as number | undefined),
        additiveBenefit:
          price.additiveBenefit || (metadata.additiveBenefit as string) || '',
      });

      // Store the original price for comparison
      setOriginalPrice(priceInDollars);
    }
    // For duplicating a price, use the source price data
    else if (duplicateSourcePrice && duplicateFromId) {
      const metadata = duplicateSourcePrice.metadata || {};

      form.reset({
        name: `${duplicateSourcePrice.name} (Copy)`,
        unit_amount: Number(
          new Big(duplicateSourcePrice.unit_amount).div(100).toFixed(2),
        ), // Convert from cents to dollars for display
        description:
          duplicateSourcePrice.description ||
          (metadata.description as string) ||
          '',
        active: false, // Always set active to false for duplicates
        isDefaultPrice: false, // Always set default to false for duplicates
        dosageDescription:
          duplicateSourcePrice.dosageDescription ||
          (metadata.dosageDescription as string) ||
          '',
        dosageLabel:
          duplicateSourcePrice.dosageLabel ||
          (metadata.dosageLabel as string) ||
          '',
        dosageTimeframe:
          duplicateSourcePrice.dosageTimeframe ||
          (metadata.dosageTimeframe as string) ||
          '',
        dosageAdditionalMessage:
          duplicateSourcePrice.dosageAdditionalMessage ||
          (metadata.dosageAdditionalMessage as string) ||
          '',
        compoundName: duplicateSourcePrice.compoundName || '',
        patientDirections: duplicateSourcePrice.patientDirections || '',
        label: duplicateSourcePrice.label || (metadata.label as string) || '',
        milligrams:
          duplicateSourcePrice.milligrams ||
          (metadata.milligrams as number | undefined),
        phase:
          duplicateSourcePrice.phase || (metadata.phase as number | undefined),
        additiveBenefit:
          duplicateSourcePrice.additiveBenefit ||
          (metadata.additiveBenefit as string) ||
          '',
      });
    }
  }, [price, duplicateSourcePrice, form, isNewPrice, duplicateFromId]);

  // Trigger form revalidation when price edit mode changes
  useEffect(() => {
    if (!isNewPrice && isPriceEditEnabled) {
      // Trigger validation when enabling price edit
      void form.trigger('unit_amount');
    }
  }, [isPriceEditEnabled, form, isNewPrice]);

  const onSubmit = async (formData: {
    name: string;
    unit_amount: number;
    productId?: string;
    description?: string;
    active?: boolean;
    dosageDescription?: string;
    dosageLabel?: string;
    dosageTimeframe?: string;
    dosageAdditionalMessage?: string;
    compoundName?: string;
    patientDirections?: string;
    label?: string;
    milligrams?: number;
    phase?: number;
    isDefaultPrice?: boolean;
    pharmacy?: string;
    additiveBenefit?: string;
  }) => {
    setIsSubmitting(true);
    try {
      if (isNewPrice) {
        // Use selected product ID if duplicating, otherwise use the original productId
        const targetProductId =
          isNewPrice && duplicateFromId && formData.productId
            ? formData.productId
            : productId;

        const result = await createProductPrice({
          productId: targetProductId,
          name: formData.name,
          unit_amount: formData.unit_amount, // Already transformed to cents by zod
          active: formData.active ?? false, // Default to inactive
          isDefaultPrice: formData.isDefaultPrice,
          description: formData.description,
          dosageDescription: formData.dosageDescription,
          dosageLabel: formData.dosageLabel,
          dosageTimeframe: formData.dosageTimeframe,
          dosageAdditionalMessage: formData.dosageAdditionalMessage,
          compoundName: formData.compoundName,
          patientDirections: formData.patientDirections,
          label: formData.label,
          milligrams: formData.milligrams,
          phase: formData.phase,
          additiveBenefit: formData.additiveBenefit,
        });
        toast({
          title: 'Success',
          description: 'Product price created successfully',
        });

        // If we're duplicating, redirect to the product prices page with the new product ID
        if (duplicateFromId && formData.productId) {
          // Create a new URL with the new product ID
          const params = new URLSearchParams();
          params.set('productId', formData.productId);
          params.set('priceId', result.id);

          // Redirect to the product prices page with the new product ID
          window.location.href = `/product-prices?${params.toString()}`;
        } else {
          handleClose();
        }
      } else {
        // Check if price has changed and we need to replace it
        const priceChanged =
          isPriceEditEnabled &&
          originalPrice !== null &&
          formData.unit_amount !== originalPrice;

        if (priceChanged) {
          // Validate that the new price is different
          if (formData.unit_amount === originalPrice) {
            toast({
              title: 'Validation Error',
              description: 'New price must be different from the current price',
              variant: 'destructive',
            });
            setIsSubmitting(false);
            return;
          }

          // Replace the price (create new and archive old)
          await replaceProductPrice({
            productId,
            priceId,
            unit_amount: formData.unit_amount, // Already in cents from zod transform
          });

          toast({
            title: 'Success',
            description:
              'Product price replaced successfully. A new price was created and the old one was archived.',
          });

          // Refresh to get the new price data
          handleClose();
        } else {
          // Normal update without price change
          await updateProductPrice({
            productId,
            priceId,
            priceData: {
              name: formData.name,
              // unit_amount is immutable in Stripe, so we don't include it in updates
              active: formData.active, // Include active status in updates
              isDefaultPrice: formData.isDefaultPrice,
              description: formData.description,
              dosageDescription: formData.dosageDescription,
              dosageLabel: formData.dosageLabel,
              dosageTimeframe: formData.dosageTimeframe,
              dosageAdditionalMessage: formData.dosageAdditionalMessage,
              compoundName: formData.compoundName,
              patientDirections: formData.patientDirections,
              label: formData.label,
              milligrams: formData.milligrams,
              phase: formData.phase,
              additiveBenefit: formData.additiveBenefit,
            },
          });

          // Explicitly refetch to ensure we have the latest information
          await refetch();

          toast({
            title: 'Success',
            description: 'Product price updated successfully',
          });
          setIsEditMode(false); // Switch back to view mode after successful update
        }
      }
    } catch (error) {
      console.error('Error saving product price:', error);
      toast({
        title: 'Error',
        description: 'Failed to save product price',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeactivate = async () => {
    if (
      !isNewPrice &&
      window.confirm('Are you sure you want to deactivate this price?')
    ) {
      setIsSubmitting(true);
      try {
        await deactivateProductPrice({ productId, priceId });
        await refetch(); // Refetch to get updated status
        toast({
          title: 'Success',
          description: 'Product price deactivated successfully',
        });
      } catch (error) {
        console.error('Error deactivating product price:', error);
        toast({
          title: 'Error',
          description: 'Failed to deactivate product price',
          variant: 'destructive',
        });
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const handleReactivate = async () => {
    if (
      !isNewPrice &&
      window.confirm('Are you sure you want to reactivate this price?')
    ) {
      setIsSubmitting(true);
      try {
        await reactivateProductPrice({ productId, priceId });
        await refetch(); // Refetch to get updated status
        toast({
          title: 'Success',
          description: 'Product price reactivated successfully',
        });
      } catch (error) {
        console.error('Error reactivating product price:', error);
        toast({
          title: 'Error',
          description: 'Failed to reactivate product price',
          variant: 'destructive',
        });
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  // Loading state
  if (
    (isLoading && !isNewPrice) ||
    (isLoadingDuplicateSource && duplicateFromId) ||
    (isNewPrice &&
      duplicateFromId &&
      (isLoadingPharmacies || isLoadingProducts))
  ) {
    return (
      <div className="flex h-full w-full items-center justify-center bg-white">
        <Loader className="w-full bg-white" />
      </div>
    );
  }

  // Edit mode for existing prices or new price form
  if (isEditMode) {
    // For existing prices, use split layout with 1/3 sidebar and 2/3 form
    if (!isNewPrice && price) {
      return (
        <div className="relative flex h-full w-full grow bg-white">
          {/* Use the ProductPriceSidebar component */}
          <ProductPriceSidebar price={price} showMenu={false} />

          {/* Right content area with form - flex grow to fill remaining space */}
          <div className="flex flex-1 flex-col overflow-scroll">
            <div className="sticky top-0 z-10 bg-white px-10 pt-10">
              <div className="mb-6 flex flex-row justify-between">
                <h2 className="text-xl font-semibold text-denim">Edit Price</h2>
                <XIcon
                  size={24}
                  className="cursor-pointer text-denim"
                  onClick={handleClose}
                />
              </div>
            </div>

            <div className="px-10 pb-10">
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-5"
                >
                  <div className="grid grid-cols-1 gap-6">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Price Name</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {isNewPrice ? (
                      <FormField
                        control={form.control}
                        name="unit_amount"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Amount (USD)</FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                type="number"
                                step="0.01"
                                min="0"
                                placeholder="0.00"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <FormField
                        control={form.control}
                        name="unit_amount"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Amount (USD)</FormLabel>
                            <div className="flex flex-col gap-2">
                              <div className="flex gap-2">
                                <FormControl>
                                  <Input
                                    {...field}
                                    type="number"
                                    step="0.01"
                                    min="0"
                                    disabled={!isPriceEditEnabled}
                                    className={
                                      !isPriceEditEnabled ? 'bg-gray-50' : ''
                                    }
                                  />
                                </FormControl>
                                <Button
                                  type="button"
                                  variant="tertiary"
                                  size="sm"
                                  onClick={() => {
                                    setIsPriceEditEnabled(!isPriceEditEnabled);
                                    if (!isPriceEditEnabled) {
                                      // Store current price when enabling edit with proper precision using Big.js
                                      const currentValue = Number(
                                        new Big(field.value).toFixed(2),
                                      );
                                      setOriginalPrice(currentValue);
                                    } else {
                                      // Reset to original price when canceling edit
                                      field.onChange(originalPrice);
                                    }
                                  }}
                                >
                                  {isPriceEditEnabled ? 'Cancel' : 'Edit'}
                                </Button>
                              </div>
                              <span className="text-xs text-gray-500">
                                {isPriceEditEnabled
                                  ? 'Editing creates a new price and archives the current one'
                                  : 'Editing creates a new price and archives the current one'}
                              </span>
                              <FormMessage />
                            </div>
                          </FormItem>
                        )}
                      />
                    )}

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Input {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="space-y-4">
                      <FormField
                        control={form.control}
                        name="active"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                                className="border-denim"
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>Active</FormLabel>
                              <p className="text-sm text-muted-foreground">
                                When enabled, this price will be available for
                                selection.
                              </p>
                            </div>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="isDefaultPrice"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                                className="border-denim"
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>Set as default price</FormLabel>
                              <p className="text-sm text-muted-foreground">
                                This price will be set as the default price for
                                this product in both the database and Stripe.
                              </p>
                            </div>
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="label"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Label</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="dosageLabel"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Dosage Label</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="dosageDescription"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Dosage Description</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="dosageTimeframe"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Dosage Timeframe</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="dosageAdditionalMessage"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Dosage Additional Message</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="compoundName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Compound Name</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="patientDirections"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Patient Directions</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="additiveBenefit"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Additive Benefit</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="milligrams"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Milligrams</FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                type="number"
                                value={field.value || ''}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="phase"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Phase</FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                type="number"
                                value={field.value || ''}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  <div className="flex justify-between border-t pt-6">
                    <div className="flex gap-2">
                      <Button
                        type="button"
                        variant="tertiary"
                        onClick={() => setIsEditMode(false)}
                        disabled={isSubmitting}
                      >
                        Cancel
                      </Button>
                      <Button type="submit" disabled={isSubmitting}>
                        {isSubmitting ? 'Saving...' : 'Update Price'}
                      </Button>
                    </div>
                  </div>
                </form>
              </Form>
            </div>
          </div>
        </div>
      );
    }

    // For new or duplicated prices, use thin 50px left sidebar and almost full width form
    return (
      <div className="relative flex h-full w-full grow bg-white">
        {/* Thin left sidebar */}
        <div className="flex w-[50px] flex-col bg-stone-light">
          {/* Empty sidebar */}
        </div>

        {/* Right content area with form */}
        <div className="flex w-[calc(100%-50px)] flex-col overflow-scroll">
          <div className="sticky top-0 z-10 bg-white px-10 pt-10">
            <div className="mb-6 flex flex-row justify-between">
              <h2 className="text-xl font-semibold text-denim">
                {duplicateFromId ? 'Duplicate Price' : 'Add New Price'}
              </h2>
              <XIcon
                size={24}
                className="cursor-pointer text-denim"
                onClick={handleClose}
              />
            </div>
          </div>

          <div className="px-10 pb-10">
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-5"
              >
                <div className="grid grid-cols-1 gap-6">
                  {/* Pharmacy and product selectors for duplicate flow */}
                  {isNewPrice && duplicateFromId && (
                    <div className="mb-4 space-y-4">
                      <FormField
                        control={form.control}
                        name="pharmacy"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Select Pharmacy</FormLabel>
                            <select
                              className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring"
                              value={selectedPharmacyId}
                              onChange={(e) => {
                                setSelectedPharmacyId(e.target.value);
                                // Reset the product ID when changing pharmacy
                                form.setValue('productId', '');
                                // Trigger validation
                                form.trigger('productId');
                              }}
                            >
                              <option value="">Select a pharmacy...</option>
                              {pharmaciesData?.pharmacies.map((pharmacy) => (
                                <option key={pharmacy.id} value={pharmacy.id}>
                                  {pharmacy.name}{' '}
                                  {pharmacy.enabled ? '' : '(Inactive)'}
                                </option>
                              ))}
                            </select>
                            {!selectedPharmacyId &&
                              form.formState.errors.pharmacy && (
                                <p className="mt-2 text-sm font-medium text-destructive">
                                  {form.formState.errors.pharmacy.message!}
                                </p>
                              )}
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="productId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel
                              className={
                                !selectedPharmacyId
                                  ? 'text-muted-foreground'
                                  : ''
                              }
                            >
                              Select Product
                            </FormLabel>
                            <select
                              className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring"
                              disabled={!selectedPharmacyId}
                              value={field.value}
                              onChange={(e) => field.onChange(e.target.value)}
                            >
                              <option value="">Select a product...</option>
                              {filteredProducts.map((product) => (
                                <option key={product.id} value={product.id}>
                                  {product.name}{' '}
                                  {product.active ? '' : '(Inactive)'}
                                </option>
                              ))}
                            </select>
                            {selectedPharmacyId && <FormMessage />}
                          </FormItem>
                        )}
                      />
                    </div>
                  )}

                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Price Name</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="unit_amount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Amount (USD)</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type="number"
                            step="0.01"
                            min="0"
                            placeholder="0.00"
                            required
                            value={field.value === undefined ? 0 : field.value}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Input {...field} value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="space-y-4">
                    <FormField
                      control={form.control}
                      name="active"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                              className="border-denim"
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>Active</FormLabel>
                            <p className="text-sm text-muted-foreground">
                              When enabled, this price will be available for
                              selection.
                            </p>
                          </div>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="isDefaultPrice"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                              className="border-denim"
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>Set as default price</FormLabel>
                            <p className="text-sm text-muted-foreground">
                              This price will be set as the default price for
                              this product in both the database and Stripe.
                            </p>
                          </div>
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="label"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Label</FormLabel>
                          <FormControl>
                            <Input {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="dosageLabel"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Dosage Label</FormLabel>
                          <FormControl>
                            <Input {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="dosageDescription"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Dosage Description</FormLabel>
                          <FormControl>
                            <Input {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="dosageTimeframe"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Dosage Timeframe</FormLabel>
                          <FormControl>
                            <Input {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="dosageAdditionalMessage"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Dosage Additional Message</FormLabel>
                          <FormControl>
                            <Input {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="compoundName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Compound Name</FormLabel>
                          <FormControl>
                            <Input {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="patientDirections"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Patient Directions</FormLabel>
                          <FormControl>
                            <Input {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="additiveBenefit"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Additive Benefit</FormLabel>
                          <FormControl>
                            <Input {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="milligrams"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Milligrams</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              type="number"
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="phase"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Phase</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              type="number"
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <div className="flex justify-between border-t pt-6">
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="tertiary"
                      onClick={handleClose}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting
                        ? 'Creating...'
                        : duplicateFromId
                          ? 'Duplicate Price'
                          : 'Create Price'}
                    </Button>
                  </div>
                </div>
              </form>
            </Form>
          </div>
        </div>
      </div>
    );
  }

  // View mode for existing prices
  if (!isEditMode && !isNewPrice && price) {
    return (
      <div className="relative flex h-full w-full grow bg-white">
        {/* Use the ProductPriceSidebar component */}
        <ProductPriceSidebar
          price={price}
          productId={productId}
          setIsEditMode={setIsEditMode}
          handleDeactivate={handleDeactivate}
          handleReactivate={handleReactivate}
          deleteProductPrice={deleteProductPrice}
          handleClose={handleClose}
          setIsSubmitting={setIsSubmitting}
          toast={toast}
        />

        {/* Right content area - flex grow to fill remaining space */}
        <div className="flex flex-1 flex-col overflow-scroll">
          <Tabs
            defaultValue={defaultTab}
            onValueChange={updateTabInURL}
            className="relative grid w-full grid-rows-[auto_1fr]"
          >
            <div className="sticky top-0 z-10 bg-white px-10 pt-10">
              <div className="flex flex-row justify-end text-denim">
                <XIcon
                  size={24}
                  className="cursor-pointer"
                  onClick={handleClose}
                />
              </div>
              <TabsList className="h-fit w-full justify-start gap-5 rounded-none border-b-[1px] border-denim-muted bg-white p-0 text-denim">
                <TabsTrigger
                  className="rounded-none text-xs font-normal text-muted-foreground data-[state=active]:border-b-[3px] data-[state=active]:border-denim data-[state=active]:text-denim data-[state=active]:shadow-none"
                  value="details"
                >
                  <InfoIcon size={16} className="mr-1" />
                  <div>Details</div>
                </TabsTrigger>
                <TabsTrigger
                  className="rounded-none text-xs font-normal text-muted-foreground data-[state=active]:border-b-[3px] data-[state=active]:border-denim data-[state=active]:text-denim data-[state=active]:shadow-none"
                  value="mappings"
                >
                  <ExternalLinkIcon size={16} className="mr-1" />
                  <div>External Mappings</div>
                </TabsTrigger>
                <TabsTrigger
                  className="rounded-none text-xs font-normal text-muted-foreground data-[state=active]:border-b-[3px] data-[state=active]:border-denim data-[state=active]:text-denim data-[state=active]:shadow-none"
                  value="price-mappings"
                >
                  <LinkIcon size={16} className="mr-1" />
                  <div>Price Mappings</div>
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent className="px-10" value="details">
              <div className="mb-5 mt-10 space-y-6">
                <div className="text-2xl font-medium text-denim">
                  Price Details
                </div>
                <div className="rounded-lg border p-4 shadow-sm">
                  <h3 className="mb-4 text-lg font-medium text-denim">
                    Price Information
                  </h3>
                  <div className="grid grid-cols-2 gap-x-4 gap-y-3">
                    <div>
                      <span className="text-sm font-medium text-dark">
                        Name:
                      </span>
                      <div className="text-sm text-denim">{price.name}</div>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-dark">
                        Amount:
                      </span>
                      <div className="text-sm text-denim">
                        ${(price.unit_amount / 100).toFixed(2)} USD
                      </div>
                    </div>
                    <div className="col-span-2">
                      <span className="text-sm font-medium text-dark">
                        Description:
                      </span>
                      <div className="text-sm text-denim">
                        {price.description || 'Not specified'}
                      </div>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-dark">
                        Compound name:
                      </span>
                      <div className="text-sm text-denim">
                        {price.compoundName || 'N/A'}
                      </div>
                    </div>
                    <div className="col-span-2">
                      <span className="text-sm font-medium text-dark">
                        Patient directions:
                      </span>
                      <div className="text-sm text-denim">
                        {price.patientDirections || 'N/A'}
                      </div>
                    </div>
                    {price.additiveBenefit && (
                      <div className="col-span-2">
                        <span className="text-sm font-medium text-dark">
                          Additive Benefit:
                        </span>
                        <div className="text-sm text-denim">
                          {price.additiveBenefit}
                        </div>
                      </div>
                    )}
                    <div>
                      <span className="text-sm font-medium text-dark">
                        Type:
                      </span>
                      <div className="mt-1">
                        {price.isDefaultPrice && (
                          <span className="inline-flex items-center rounded-full bg-electric-light px-2.5 py-0.5 text-xs font-medium text-denim">
                            Default Price
                          </span>
                        )}
                      </div>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-dark">
                        Status:
                      </span>
                      <div className="mt-1">
                        {price.active ? (
                          <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                            Active
                          </span>
                        ) : (
                          <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                            Inactive
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent className="px-10" value="mappings">
              <div className="mb-5 mt-10 space-y-6">
                <div className="text-2xl font-medium text-denim">
                  External Mappings
                </div>
                <ProductPriceMappingsList productPriceId={price.id} />
              </div>
            </TabsContent>

            <TabsContent className="px-10" value="price-mappings">
              <div className="mb-5 mt-10 space-y-6">
                <div className="text-2xl font-medium text-denim">
                  Price Mappings
                </div>
                <ProductPriceEquivalenceList productPriceId={price.id} />
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    );
  }

  return null;
}
