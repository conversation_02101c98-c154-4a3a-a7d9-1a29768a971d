'use client';

import { useState } from 'react';
import { Edit2Icon, PlusIcon, Trash2Icon } from 'lucide-react';

import { Button } from '@willow/ui/base/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@willow/ui/base/table';
import { useToast } from '@willow/ui/base/use-toast';
import { Drawer, DrawerContent, DrawerOverlay } from '@willow/ui/base/vaul';
import { Loader } from '@willow/ui/loader';

import {
  useDeleteProductPriceMapping,
  useGetProductPriceMappings,
} from '~/hooks/product-price-mappings';
import { ProductPriceMappingForm } from './product-price-mapping-form';

interface ProductPriceMappingsListProps {
  productPriceId: string;
}

export function ProductPriceMappingsList({
  productPriceId,
}: ProductPriceMappingsListProps) {
  const [editingMappingId, setEditingMappingId] = useState<string | null>(null);
  const [isAddMappingOpen, setIsAddMappingOpen] = useState(false);
  const [isEditMappingOpen, setIsEditMappingOpen] = useState(false);
  const { toast } = useToast();

  const {
    data: mappingsData,
    isLoading,
    refetch,
  } = useGetProductPriceMappings({
    productPriceId,
  });

  const { mutateAsync: deleteMapping } = useDeleteProductPriceMapping();

  const handleDelete = async (mappingId: string) => {
    if (window.confirm('Are you sure you want to delete this mapping?')) {
      try {
        await deleteMapping({ mappingId });
        toast({
          title: 'Success',
          description: 'Mapping deleted successfully',
        });
        void refetch();
      } catch (error) {
        console.error('Error deleting mapping:', error);
        toast({
          title: 'Error',
          description: 'Failed to delete mapping',
          variant: 'destructive',
        });
      }
    }
  };

  const handleEdit = (mappingId: string) => {
    setEditingMappingId(mappingId);
    setIsEditMappingOpen(true);
  };

  const handleMappingSuccess = () => {
    setIsAddMappingOpen(false);
    setIsEditMappingOpen(false);
    setEditingMappingId(null);
    void refetch();
  };

  if (isLoading) {
    return (
      <div className="flex h-40 items-center justify-center">
        <Loader />
      </div>
    );
  }

  const mappings = mappingsData?.mappings || [];

  return (
    <div className="flex flex-col space-y-4">
      <div className="flex justify-between">
        <h3 className="text-lg font-medium text-denim">External Mappings</h3>

        <Button
          size="sm"
          className="flex items-center gap-1"
          onClick={() => setIsAddMappingOpen(true)}
        >
          <PlusIcon size={16} />
          <span>Add Mapping</span>
        </Button>
      </div>

      {/* Add Mapping Drawer */}
      <Drawer
        open={isAddMappingOpen}
        onOpenChange={setIsAddMappingOpen}
        direction="right"
        modal={true}
        dismissible={true}
      >
        <DrawerOverlay className="fixed inset-0 z-50 bg-black/25" />
        <DrawerContent className="fixed right-0 top-0 z-50 m-0 h-full w-[450px] max-w-full overflow-y-auto overflow-x-hidden border-l border-border bg-white p-0 shadow-lg">
          <ProductPriceMappingForm
            productPriceId={productPriceId}
            onSuccess={handleMappingSuccess}
            onCancel={() => setIsAddMappingOpen(false)}
          />
        </DrawerContent>
      </Drawer>

      {/* Edit Mapping Drawer */}
      <Drawer
        open={isEditMappingOpen}
        onOpenChange={(open) => {
          setIsEditMappingOpen(open);
          if (!open) setEditingMappingId(null);
        }}
        direction="right"
        modal={true}
        dismissible={true}
      >
        <DrawerOverlay className="fixed inset-0 z-50 bg-black/25" />
        <DrawerContent className="fixed right-0 top-0 z-50 m-0 h-full w-[450px] max-w-full overflow-y-auto overflow-x-hidden border-l border-border bg-white p-0 shadow-lg">
          {editingMappingId && (
            <ProductPriceMappingForm
              productPriceId={productPriceId}
              mappingId={editingMappingId}
              onSuccess={handleMappingSuccess}
              onCancel={() => {
                setIsEditMappingOpen(false);
                setEditingMappingId(null);
              }}
            />
          )}
        </DrawerContent>
      </Drawer>

      {mappings.length === 0 ? (
        <div className="flex h-40 items-center justify-center rounded-md border border-dashed p-4 text-center text-sm text-gray-500">
          No mappings found for this product price.
        </div>
      ) : (
        <div className="overflow-hidden rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>External ID</TableHead>
                <TableHead>Name</TableHead>
                <TableHead className="w-20 text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mappings.map((mapping) => (
                <TableRow key={mapping.id}>
                  <TableCell>{mapping.externalId}</TableCell>
                  <TableCell>{mapping.name || '-'}</TableCell>
                  <TableCell className="flex justify-end space-x-2">
                    <Button size="icon" onClick={() => handleEdit(mapping.id)}>
                      <Edit2Icon size={16} className="text-denim" />
                    </Button>

                    <Button
                      size="icon"
                      onClick={() => handleDelete(mapping.id)}
                    >
                      <Trash2Icon size={16} className="text-red-500" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
}
