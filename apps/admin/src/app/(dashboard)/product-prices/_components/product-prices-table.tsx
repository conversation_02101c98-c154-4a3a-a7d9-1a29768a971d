'use client';

import type { ColumnDef } from '@tanstack/react-table';
import { useMemo, useState } from 'react';
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { useQueryState } from 'nuqs';

import { cn } from '@willow/ui';
import { Button } from '@willow/ui/base/button';
import { DrawerTitle } from '@willow/ui/base/drawer';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@willow/ui/base/table';
import {
  Drawer,
  DrawerContent,
  DrawerOverlay,
  DrawerPortal,
  DrawerTrigger,
} from '@willow/ui/base/vaul';
import { ColumnHeader } from '@willow/ui/column-header';
import { Loader } from '@willow/ui/loader';
import { Pagination } from '@willow/ui/pagination';
import {
  usePaginationSearchParams,
  useSortingQueryState,
} from '@willow/utils/table';

import type { ProductPrice } from '~/hooks/product-prices';
import { env } from '~/env';
import { useGetProductPrices } from '~/hooks/product-prices';
import { ProductPriceInfo } from './product-price-info';

interface ProductPricesTableProps {
  productId: string;
}

export function ProductPricesTable({ productId }: ProductPricesTableProps) {
  const [pagination, setPagination] = usePaginationSearchParams();
  const [sorting, setSorting] = useSortingQueryState();

  const [selectedPriceId, setSelectedPriceId] = useQueryState('priceId', {
    defaultValue: '',
  });

  // State for showing inactive prices
  const [showInactive, setShowInactive] = useState(false);

  const fetchParams = useMemo(
    () => ({
      productId,
      sortBy: sorting.length > 0 ? sorting[0]?.id : undefined,
      direction:
        sorting.length > 0
          ? sorting[0]?.desc
            ? ('desc' as const)
            : ('asc' as const)
          : ('asc' as const),
      page: pagination.page,
      limit: pagination.pageSize,
      showInactive,
    }),
    [productId, pagination, sorting, showInactive],
  );

  const { data, isPending, isError } = useGetProductPrices(fetchParams);

  // Determine Stripe price URL base on environment
  const stripeBaseUrl = useMemo(() => {
    return env.NEXT_PUBLIC_ENVIRONMENT === 'production'
      ? 'https://dashboard.stripe.com/prices/'
      : 'https://dashboard.stripe.com/test/prices/';
  }, []);

  const columns: ColumnDef<ProductPrice>[] = useMemo(
    () => [
      {
        accessorKey: 'name',
        header: () => <ColumnHeader label="Name" sortKey="name" />,
        cell: ({ row }) => (
          <div className="flex items-center gap-2">
            <div className="text-sm text-denim">{row.original.name}</div>
            {row.original.id === row.original.product?.defaultPriceId && (
              <span className="inline-flex items-center rounded-full bg-stone-100 px-2.5 py-0.5 text-xs font-medium text-stone-800">
                Default
              </span>
            )}
          </div>
        ),
      },
      {
        accessorKey: 'unit_amount',
        header: () => <ColumnHeader label="Price" sortKey="unit_amount" />,
        cell: ({ row }) => (
          <div className="text-sm text-dark">
            ${(row.original.unit_amount / 100).toFixed(2)} USD
          </div>
        ),
      },
      {
        accessorKey: 'id',
        header: 'Stripe',
        cell: ({ row }) => (
          <div className="text-sm text-dark">
            <a
              href={`${stripeBaseUrl}${row.original.id}`}
              target="_blank"
              rel="noopener noreferrer"
              className="text-denim hover:underline"
              onClick={(e) => e.stopPropagation()} // Prevent drawer from opening
            >
              price
            </a>
          </div>
        ),
      },
      {
        accessorKey: 'phase',
        header: 'Phase',
        cell: ({ row }) => (
          <div className="text-sm text-dark">
            {row.original.phase !== undefined ? row.original.phase : '-'}
          </div>
        ),
      },
      {
        accessorKey: 'active',
        header: () => <ColumnHeader label="Status" sortKey="active" />,
        cell: ({ row }) => {
          const isActive = row.original.active;
          return (
            <div className="text-sm">
              {isActive ? (
                <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                  Active
                </span>
              ) : (
                <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                  Inactive
                </span>
              )}
            </div>
          );
        },
      },
    ],
    [stripeBaseUrl],
  );

  // Process the prices data - filter by active status
  const prices = useMemo(() => {
    const allPrices = data?.productPrices || [];

    if (!showInactive) {
      return allPrices.filter((price) => price.active);
    }

    return allPrices;
  }, [data, showInactive]);

  const table = useReactTable({
    data: prices,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: (sorting) => {
      void setSorting(sorting);
    },
    state: {
      sorting,
      pagination: {
        pageIndex: pagination.page - 1,
        pageSize: pagination.pageSize,
      },
    },
    manualPagination: true,
    pageCount: data?.pagination?.totalPages || 1,
  });

  return (
    <div className="grid h-[calc(100vh-160px)] w-full grid-rows-[auto_1fr_auto]">
      <div className="w-full">{/* Header space if needed */}</div>

      <div className="relative flex-1 overflow-auto">
        <Drawer
          direction="right"
          open={selectedPriceId !== ''}
          onOpenChange={(value) => {
            if (!value) void setSelectedPriceId('');
          }}
        >
          <div className="h-full w-full">
            {isPending ? (
              <div className="flex h-40 w-full items-center justify-center">
                <Loader size="lg" />
              </div>
            ) : (
              <Table>
                <TableHeader>
                  {table.getHeaderGroups().map((headerGroup) => (
                    <TableRow key={headerGroup.id}>
                      {headerGroup.headers.map((header) => (
                        <TableHead
                          key={header.id}
                          className="font-bold text-dark"
                        >
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext(),
                              )}
                        </TableHead>
                      ))}
                    </TableRow>
                  ))}
                </TableHeader>
                <TableBody>
                  {table.getRowModel().rows.length > 0 ? (
                    table.getRowModel().rows.map((row) => (
                      <DrawerTrigger
                        key={row.id}
                        asChild
                        onClick={() => {
                          void setSelectedPriceId(row.original.id);
                        }}
                      >
                        <TableRow
                          data-state={
                            selectedPriceId === row.original.id && 'selected'
                          }
                          className="h-[60px] cursor-pointer hover:bg-muted/50"
                        >
                          {row.getVisibleCells().map((cell) => (
                            <TableCell key={cell.id} className="p-2">
                              {flexRender(
                                cell.column.columnDef.cell,
                                cell.getContext(),
                              )}
                            </TableCell>
                          ))}
                        </TableRow>
                      </DrawerTrigger>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell
                        colSpan={columns.length}
                        className="h-24 text-center"
                      >
                        {isError
                          ? 'Error loading product prices.'
                          : 'No results.'}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            )}
          </div>
          {selectedPriceId && (
            <DrawerPortal>
              <DrawerOverlay className="fixed inset-0 bg-dark/40" />
              <DrawerContent className="fixed bottom-0 right-0 top-0 z-10 flex w-[1000px] !touch-none !select-text">
                <DrawerTitle className="hidden">
                  Product Price Information
                </DrawerTitle>
                <ProductPriceInfo
                  productId={productId}
                  priceId={selectedPriceId}
                  handleClose={() => setSelectedPriceId('')}
                />
              </DrawerContent>
            </DrawerPortal>
          )}
        </Drawer>
      </div>

      <div className={cn('flex items-center justify-between py-4')}>
        <div className="flex gap-3">
          <Button
            onClick={() => {
              setSelectedPriceId('new');
              // Clear the duplicate parameter from the URL if it exists
              const url = new URL(window.location.href);
              if (url.searchParams.has('duplicate')) {
                url.searchParams.delete('duplicate');
                window.history.replaceState({}, '', url.toString());
              }
            }}
          >
            Add New Price
          </Button>
          <Button
            variant="electric"
            onClick={() => {
              setShowInactive(!showInactive);
              void setPagination({ page: 1 }); // Reset to first page when toggling
            }}
          >
            {showInactive ? 'Hide Inactive Prices' : 'Show Inactive Prices'}
          </Button>
        </div>
        {table.getRowModel().rows.length > 0 && (
          <Pagination
            currentPage={pagination.page}
            totalPages={table.getPageCount()}
            onPageChange={(page) => setPagination({ page })}
          />
        )}
      </div>
    </div>
  );
}
