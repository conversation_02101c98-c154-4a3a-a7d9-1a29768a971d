'use client';

import { useCallback, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';

import { Loader } from '@willow/ui/loader';

import { useGetProduct } from '~/hooks/products';
import { ProductPricesTable } from './_components/product-prices-table';

export default function ProductPricesPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const productId = searchParams.get('productId');

  // Redirect to products page if no productId is provided
  useEffect(() => {
    if (!productId) {
      router.push('/products');
    }
  }, [productId, router]);

  const { data: product, isLoading } = useGetProduct(productId || '');

  const getBreadcrumbText = useCallback(() => {
    if (isLoading) return 'Loading...';
    if (!product) return 'Product Prices';
    return `Prices for ${product.label}, ${product.form}`;
  }, [product, isLoading]);

  if (!productId) {
    return null; // Will redirect
  }

  if (isLoading) {
    return (
      <div className="flex h-40 w-full items-center justify-center">
        <Loader size="lg" />
      </div>
    );
  }

  if (!product) {
    return (
      <div className="mx-auto mt-10 max-w-4xl text-center">
        <h1 className="text-xl font-semibold text-red-500">
          Product not found
        </h1>
        <p className="mt-4">
          The product you are looking for does not exist or you do not have
          permission to view it.
        </p>
        <button
          onClick={() => router.push('/products')}
          className="mt-6 rounded-md bg-denim px-4 py-2 text-white"
        >
          Back to Products
        </button>
      </div>
    );
  }

  return (
    <div className="flex h-full flex-col">
      <div className="mb-4">
        <h1 className="text-2xl font-semibold text-denim">
          {getBreadcrumbText()}
        </h1>
        <p>
          <a
            href={`/products/all?productId=${productId}`}
            className="text-sm text-denim hover:underline"
          >
            Back to Product
          </a>
        </p>
        <p>
          <a
            href={`/pharmacies/all?pharmacyId=${product.pharmacy?.id}`}
            className="text-sm text-denim hover:underline"
          >
            {product.pharmacy?.name}
          </a>
        </p>
      </div>

      <ProductPricesTable productId={productId} />
    </div>
  );
}
