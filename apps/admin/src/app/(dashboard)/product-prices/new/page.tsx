'use client';

import { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';

export default function NewProductPricePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const productId = searchParams.get('productId');
  const duplicateId = searchParams.get('duplicate');

  useEffect(() => {
    if (!productId) {
      // If no product ID is provided, redirect to products page
      router.replace('/products');
      return;
    }

    // Create new query params
    const queryParams = new URLSearchParams();
    queryParams.set('productId', productId);
    queryParams.set('priceId', 'new');

    // Add duplicate ID if present
    if (duplicateId) {
      queryParams.set('duplicate', duplicateId);
    }

    // Redirect to product prices page with the correct query parameters
    router.replace(`/product-prices?${queryParams.toString()}`);
  }, [router, productId, duplicateId]);

  return null;
}
