'use client';

import { Capability } from '@willow/auth';

import { WithCapability } from '~/components/capability';
import { PatientSidebar } from './_components/PatientSidebar';

export default function PatientSidebarLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <WithCapability
      requiredCapabilities={Capability.VIEW_PATIENTS}
      fallback={
        <div className="flex h-full w-full items-center justify-center">
          <div className="text-xl font-medium text-denim">
            You don&apos;t have permission to access this page.
          </div>
        </div>
      }
    >
      <div className="font-neue grid grid-cols-[auto_1fr]">
        <PatientSidebar />
        <section>
          <main className="h-screen overflow-y-scroll px-10 pb-16 pt-12">
            <div className="flex flex-col items-center justify-center gap-4">
              {children}
            </div>
          </main>
        </section>
      </div>
    </WithCapability>
  );
}
