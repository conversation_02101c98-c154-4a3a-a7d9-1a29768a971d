import { Loader } from '@willow/ui/loader';

import { useGetPatientIntake } from '~/hooks/patients';

export function PatientIntakeHistory({ patientId }: { patientId: string }) {
  const {
    data: patientIntake,
    isLoading,
    error,
  } = useGetPatientIntake(patientId);

  if (isLoading) return <Loader className="h-full py-32" />;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      <div className="mb-8 text-2xl font-medium text-slate-600">
        Intake History
      </div>
      <div className="mb-4 text-sm font-semibold text-denim">
        Health Questions
      </div>

      <div className="flex flex-col gap-6">
        {patientIntake?.record.map((question) => (
          <div key={question.question} className="flex flex-col gap-2">
            <div className="text-xs font-medium">{question.question}</div>
            <div className="text-xs font-medium text-neutral-500">
              {question.answer === '' ? '_' : question.answer}
            </div>
            <div className="h-0 self-stretch bg-slate-200 text-zinc-200 outline outline-[0.75px] outline-offset-[-0.38px]" />
          </div>
        ))}
      </div>
    </div>
  );
}
