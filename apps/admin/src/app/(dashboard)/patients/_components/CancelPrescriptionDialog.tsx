import { useCallback } from 'react';

import { Button } from '@willow/ui/base/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from '@willow/ui/base/dialog';

import Logo from '~/assets/svg/logo-denim.svg';
import { useCancelTreatment } from '~/hooks/treatments';

export const CancelPrescriptionDialog = ({
  open,
  onClose,
  treatmentId,
  patientId,
}: {
  open: boolean;
  treatmentId: string;
  patientId: string;
  onClose: (value: boolean) => void;
}) => {
  const cancelTreatment = useCancelTreatment(patientId, {
    onSettled: () => {
      onClose(false);
    },
  });

  const handleCancelConfirm = useCallback(() => {
    cancelTreatment.mutate(treatmentId);
  }, [cancelTreatment, treatmentId]);

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <div className="mx-auto">
          <Logo />
        </div>

        <div className="flex flex-col gap-4 text-center">
          <DialogTitle asChild>
            <p className="text-2xl font-medium leading-[34px]">
              Cancel Prescription
            </p>
          </DialogTitle>
          <DialogDescription asChild>
            <p className="text-base">
              Are you sure you want to cancel the Prescription?
            </p>
          </DialogDescription>
        </div>
        <div>
          <div className="flex justify-end gap-4 space-x-2">
            <Button
              onClick={() => {
                onClose(false);
              }}
              variant={'denimOutline'}
              className={'w-full'}
              type="button"
              size={'sm'}
            >
              No
            </Button>
            <Button
              onClick={() => {
                handleCancelConfirm();
              }}
              variant={'denim'}
              className={'w-full'}
              type="button"
              size={'sm'}
            >
              CONFIRM
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
