import { env } from '@/env';
import { useAtom } from 'jotai';

import { ChatProvider } from '@willow/chat';
import { Loader } from '@willow/ui/loader';

import { accessTokenAtom } from '~/store/accessToken';
import { Chat } from './Chat';

export const DoctorAdminConversation = ({
  conversationId,
  onMessageSent,
}: {
  conversationId: string;
  onMessageSent?: () => void;
}) => {
  const [signIn] = useAtom(accessTokenAtom);

  if (!signIn?.accessToken) {
    return <Loader className="h-screen" size="xl" />;
  }

  return (
    <ChatProvider
      apiUrl={env.NEXT_PUBLIC_API_URL}
      s3Url={env.NEXT_PUBLIC_API_S3_URL}
      accessToken={signIn.accessToken}
      conversationId={conversationId}
      autoMarkAsRead
      type="doctorAdmin"
    >
      <Chat onMessageSent={onMessageSent} />
    </ChatProvider>
  );
};
