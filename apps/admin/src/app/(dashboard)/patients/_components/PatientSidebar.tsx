import { useEffect, useState } from 'react';

import type { PatientGroup } from '@willow/db/client';

import PanelLeftIcon from '~/assets/svg/panel-left.svg';
import { NavbarItem, NavbarSection } from '~/components/NavbarItem';
import { useStreamingPatientsGroupCounts } from '~/hooks/patients';

export const PatientSidebar = () => {
  const {
    data: groupCounts,
    isLoading,
    isComplete,
    processedGroups,
  } = useStreamingPatientsGroupCounts();
  const [loadingText, setLoadingText] = useState('Loading...');

  // Update loading text to show progress
  useEffect(() => {
    if (isLoading) {
      const intervalId = setInterval(() => {
        setLoadingText((prev) => {
          if (prev === 'Loading...') return 'Loading.';
          if (prev === 'Loading.') return 'Loading..';
          if (prev === 'Loading..') return 'Loading...';
          return 'Loading...';
        });
      }, 500);

      return () => clearInterval(intervalId);
    }
  }, [isLoading]);

  return (
    <aside className="scrollbar-hidden h-screen w-[284px] overflow-y-scroll bg-stone-light px-6 py-10">
      <div className="flex flex-col gap-6 text-[13px]">
        <div className="flex justify-between text-denim">
          <h1 className="text-2xl">Patients</h1>
          <div className="flex items-center gap-2">
            {isLoading && !isComplete && (
              <span className="text-xs text-gray-500">{loadingText}</span>
            )}
            <PanelLeftIcon />
          </div>
        </div>

        <div className="flex flex-col gap-3">
          <PatientSidebarLink
            title="All"
            group="all"
            count={groupCounts?.all || 0}
            processedGroups={processedGroups}
            isComplete={isComplete}
          />
          <PatientSidebarLink
            title="Active"
            group="active"
            count={groupCounts?.active || 0}
            processedGroups={processedGroups}
            isComplete={isComplete}
          />
          <PatientSidebarLink
            title="Inactive"
            group="inactive"
            count={groupCounts?.inactive || 0}
            processedGroups={processedGroups}
            isComplete={isComplete}
          />
          <PatientSidebarLink
            title="Banned"
            group="banned"
            count={groupCounts?.banned || 0}
            processedGroups={processedGroups}
            isComplete={isComplete}
          />
          <PatientSidebarLink
            title="Cancelled"
            group="cancelled"
            count={groupCounts?.cancelled || 0}
            processedGroups={processedGroups}
            isComplete={isComplete}
          />
          <PatientSidebarLink
            title="Deleted"
            group="deleted"
            count={groupCounts?.deleted || 0}
            processedGroups={processedGroups}
            isComplete={isComplete}
          />
        </div>

        <NavbarSection title="Lifecycle">
          <PatientSidebarLink
            title="Onboarding"
            group="onboarding"
            count={groupCounts?.onboarding || 0}
            processedGroups={processedGroups}
            isComplete={isComplete}
          />
          <PatientSidebarLink
            title="Verification Required (Patient)"
            group="pending-patient-verification"
            count={groupCounts?.['pending-patient-verification'] || 0}
            processedGroups={processedGroups}
            isComplete={isComplete}
          />
          <PatientSidebarLink
            title="Verification Required (Doctor)"
            group="pending-doctor-verification"
            count={groupCounts?.['pending-doctor-verification'] || 0}
            processedGroups={processedGroups}
            isComplete={isComplete}
          />
          <PatientSidebarLink
            title="Rejected"
            group="rejected"
            count={groupCounts?.rejected || 0}
            processedGroups={processedGroups}
            isComplete={isComplete}
          />
        </NavbarSection>

        <NavbarSection title="Follow Up">
          <PatientSidebarLink
            title="No Follow Up"
            group="no-follow-up"
            count={groupCounts?.['no-follow-up'] || 0}
            processedGroups={processedGroups}
            isComplete={isComplete}
          />
          <PatientSidebarLink
            title="Follow Up Needs Start"
            group="follow-up-needs-start"
            count={groupCounts?.['follow-up-needs-start'] || 0}
            processedGroups={processedGroups}
            isComplete={isComplete}
          />
          <PatientSidebarLink
            title="Follow Up Needs Complete"
            group="follow-up-needs-complete"
            count={groupCounts?.['follow-up-needs-complete'] || 0}
            processedGroups={processedGroups}
            isComplete={isComplete}
          />
          <PatientSidebarLink
            title="Follow Up Needs Doctor Review"
            group="follow-up-needs-doctor-review"
            count={groupCounts?.['follow-up-needs-doctor-review'] || 0}
            processedGroups={processedGroups}
            isComplete={isComplete}
          />
        </NavbarSection>

        <NavbarSection title="Actions">
          <PatientSidebarLink
            title="Needs Reply"
            group="needs-reply"
            count={groupCounts?.['needs-reply'] || 0}
            processedGroups={processedGroups}
            isComplete={isComplete}
          />
        </NavbarSection>

        <NavbarSection title="Payment">
          <PatientSidebarLink
            title="Failed Transaction"
            group="failed-transaction"
            count={groupCounts?.['failed-transaction'] || 0}
            processedGroups={processedGroups}
            isComplete={isComplete}
          />
          <PatientSidebarLink
            title="Card Expired"
            group="card-expired"
            count={groupCounts?.['card-expired'] || 0}
            processedGroups={processedGroups}
            isComplete={isComplete}
          />
          <PatientSidebarLink
            title="Uncollectible"
            group="uncollectible"
            count={groupCounts?.uncollectible || 0}
            processedGroups={processedGroups}
            isComplete={isComplete}
          />
        </NavbarSection>

        <NavbarSection title="Monitoring">
          <PatientSidebarLink
            title="Red Rock"
            group="red-rock"
            count={groupCounts?.['red-rock'] || 0}
            processedGroups={processedGroups}
            isComplete={isComplete}
          />
          <PatientSidebarLink
            title="Partell"
            group="partell"
            count={groupCounts?.partell || 0}
            processedGroups={processedGroups}
            isComplete={isComplete}
          />
          <PatientSidebarLink
            title="Empower"
            group="empower"
            count={groupCounts?.empower || 0}
            processedGroups={processedGroups}
            isComplete={isComplete}
          />
        </NavbarSection>
        <NavbarSection title="Prescriptions">
          <PatientSidebarLink
            title="Definite Pause"
            group="prescriptions-definite-pause"
            count={groupCounts?.['prescriptions-definite-pause'] || 0}
            processedGroups={processedGroups}
            isComplete={isComplete}
          />
          <PatientSidebarLink
            title="Indefinite Paused"
            group="prescriptions-indefinite-paused"
            count={groupCounts?.['prescriptions-indefinite-paused'] || 0}
            processedGroups={processedGroups}
            isComplete={isComplete}
          />
          <PatientSidebarLink
            title="Complete"
            group="prescriptions-complete"
            count={groupCounts?.['prescriptions-complete'] || 0}
            processedGroups={processedGroups}
            isComplete={isComplete}
          />
        </NavbarSection>

        <NavbarSection title="Outreach">
          <PatientSidebarLink
            title="Abandoned Checkout"
            group="abandoned-checkout"
            count={groupCounts?.['abandoned-checkout'] || 0}
            processedGroups={processedGroups}
            isComplete={isComplete}
          />
          <PatientSidebarLink
            title="Abandoned Verification"
            group="abandoned-verification"
            count={groupCounts?.['abandoned-verification'] || 0}
            processedGroups={processedGroups}
            isComplete={isComplete}
          />
          <PatientSidebarLink
            title="Abandoned Treatment Selection"
            group="abandoned-treatment-selection"
            count={groupCounts?.['abandoned-treatment-selection'] || 0}
            processedGroups={processedGroups}
            isComplete={isComplete}
          />
          <PatientSidebarLink
            title="Abandoned Questionnaire"
            group="abandoned-questionnaire"
            count={groupCounts?.['abandoned-questionnaire'] || 0}
            processedGroups={processedGroups}
            isComplete={isComplete}
          />
        </NavbarSection>
      </div>
    </aside>
  );
};

const PatientSidebarLink = ({
  title,
  group,
  count = 0,
  processedGroups,
  isComplete,
}: {
  title: string;
  group: PatientGroup;
  count?: number;
  processedGroups?: Set<string>;
  isComplete?: boolean;
}) => {
  // Add visual indicators for different states
  let countClass = '';

  // If the response is complete or the group has been processed, don't animate
  if (isComplete || processedGroups?.has(group)) {
    // If the count is 0, show it as dimmed
    if (count === 0) {
      countClass = 'text-gray-400';
    } else {
      // Otherwise, it's a normal count with a value
      countClass = '';
    }
  }
  // If the group hasn't been processed yet, show a loading animation
  else {
    countClass = 'text-gray-400 animate-pulse';
  }

  return (
    <NavbarItem
      href={`/patients/${group}`}
      title={title}
      count={count}
      countClassName={countClass}
    />
  );
};
