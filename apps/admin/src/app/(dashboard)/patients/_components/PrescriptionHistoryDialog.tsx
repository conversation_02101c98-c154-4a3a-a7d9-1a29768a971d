import type { TreatmentItemAPI } from '@willow/db/client';
import { Dialog, DialogContent } from '@willow/ui/base/dialog';
import { formatDate } from '@willow/utils/format';

import { PresctiptionCard } from './PrescriptionCard';
import { PrescriptionTableLogs } from './PrescriptionTableLogs';

export const PrescriptionHistoryDialog = ({
  treatments,
  patientId,
  open,
  onClose,
  selectedTreatmentId,
  setSelectedTreatmentId,
}: {
  treatments: TreatmentItemAPI[];
  selectedTreatmentId: string;
  patientId: string;
  open: boolean;
  setSelectedTreatmentId: (value: string) => void;
  onClose: (value: boolean) => void;
}) => {
  const selectedTreatment = treatments.find(
    (treatment) => treatment.treatmentId === selectedTreatmentId,
  );
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="h-5/6 max-w-7xl p-8">
        <div className="grid grid-cols-6 gap-8 overflow-y-scroll">
          <div className="text-2xl font-medium text-denim">
            <h3>Prescription Logs</h3>
          </div>
          <div className="col-span-2">
            <div className="mb-6 text-lg font-medium text-[#2F4C78]">
              Active prescriptions
            </div>
            <div className="flex flex-col gap-4">
              {treatments?.map((treatment) => (
                <div
                  key={treatment.treatmentId}
                  onClick={() => setSelectedTreatmentId(treatment.treatmentId)}
                  className="cursor-pointer"
                >
                  <PresctiptionCard
                    patientId={patientId}
                    treatment={treatment}
                    handlePrescriptionHistoryClick={() => {
                      console.log('clicked');
                    }}
                    isSelected={selectedTreatmentId === treatment.treatmentId}
                  />
                </div>
              ))}
            </div>
          </div>
          <div className="col-span-3">
            <div className="mb-6 text-lg font-medium text-[#2F4C78]">Logs</div>
            {selectedTreatment && (
              <div className="mb-4 grid grid-cols-3 gap-4 rounded-lg border border-[#2f4c78]/20 p-4">
                <div>
                  <div className="text-[11px] font-normal text-stone/60">
                    Order
                  </div>
                  <div className="w-20 break-all text-[11px] font-normal text-dark">
                    {selectedTreatment.activeProduct.id}
                  </div>
                </div>
                <div>
                  <div className="text-[11px] font-normal text-stone/60">
                    Prescription
                  </div>
                  <div className="text-[11px] font-normal text-dark">
                    {selectedTreatment.activeProduct.name}
                  </div>
                </div>
                <div>
                  <div className="text-[11px] font-normal text-stone/60">
                    Date created
                  </div>
                  <div className="text-[11px] font-normal text-dark">
                    {formatDate(
                      new Date(selectedTreatment.activeProduct.startDate),
                    )}
                  </div>
                </div>
                <div>
                  <div className="text-[11px] font-normal text-stone/60">
                    Dosage
                  </div>
                  <div className="text-[11px] font-normal text-dark">{`${selectedTreatment.activeProduct.dose} mg`}</div>
                </div>
                <div>
                  <div className="text-[11px] font-normal text-stone/60">
                    Last Order Date
                  </div>
                  <div className="text-[11px] font-normal text-dark">
                    {selectedTreatment.lastChargedAt
                      ? formatDate(new Date(selectedTreatment.lastChargedAt))
                      : '-'}
                  </div>
                </div>
                <div>
                  <div className="text-[11px] font-normal text-stone/60">
                    Pharmacy
                  </div>
                  <div className="text-[11px] font-normal text-dark">
                    {selectedTreatment.activeProduct.pharmacy}
                  </div>
                </div>
              </div>
            )}

            {selectedTreatmentId && (
              <PrescriptionTableLogs treatmentId={selectedTreatmentId} />
            )}
            {selectedTreatment?.transferredTo &&
              (() => {
                const transferredTreatment = treatments.find(
                  (t) => t.treatmentId === selectedTreatment.transferredTo,
                );
                if (transferredTreatment) {
                  return (
                    <div className="mt-4 text-sm text-stone/60">
                      Prescription Transferred: {selectedTreatment.pharmacy}{' '}
                      {'>'} {transferredTreatment.pharmacy}
                    </div>
                  );
                }
                return null;
              })()}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
