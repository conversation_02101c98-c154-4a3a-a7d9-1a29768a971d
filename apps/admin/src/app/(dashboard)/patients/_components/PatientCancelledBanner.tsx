import { useMemo } from 'react';

import type { AdminPatientById } from '@willow/db/client';
import { Banner } from '@willow/ui/base/banner';
import { formatDate } from '@willow/utils/format';

export const PatientCancelledBanner = ({
  patient,
}: {
  patient: AdminPatientById;
}) => {
  const canceledBy = useMemo(() => {
    if (!patient.canceledBy) return 'N/A';

    if (patient.canceledBy.id === patient.userId) return 'patient himself';

    return `${patient.canceledBy.type === 'doctor' ? 'Dr.' : 'Admin '} ${patient.canceledBy.firstName} ${patient.canceledBy.lastName}`;
  }, [patient.canceledBy, patient.userId]);

  if (patient.status !== 'cancelled') return null;

  return (
    <Banner variant={'error'} className="absolute top-0 p-1">
      Canceled on{' '}
      {patient.canceledAt ? formatDate(new Date(patient.canceledAt)) : '--'} by{' '}
      {canceledBy}
    </Banner>
  );
};
