import { useCallback } from 'react';

import { Button } from '@willow/ui/base/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from '@willow/ui/base/dialog';

import Logo from '~/assets/svg/logo-denim.svg';
import { useFireNext } from '~/hooks/treatments';

export const FireNextConfirmationDialog = ({
  open,
  setOpen,
  treatmentId,
  patientId,
}: {
  open: boolean;
  treatmentId: string;
  patientId: string;
  setOpen: (value: boolean) => void;
}) => {
  const resumeTreatment = useFireNext(patientId, {
    onSettled: () => {
      setOpen(false);
    },
  });

  const handleResumeConfirm = useCallback(() => {
    resumeTreatment.mutate({ treatmentId });
  }, [resumeTreatment, treatmentId]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <div className="mx-auto">
          <Logo />
        </div>

        <div className="flex flex-col gap-4 text-center">
          <DialogTitle asChild>
            <p className="text-2xl font-medium leading-[34px]">
              Fire Next Order
            </p>
          </DialogTitle>
          <DialogDescription asChild>
            <p className="text-base">
              Would you like to send the next order now?
            </p>
          </DialogDescription>
        </div>
        <div>
          <div className="flex justify-end gap-4 space-x-2">
            <Button
              onClick={() => {
                setOpen(false);
              }}
              variant={'denimOutline'}
              className={'w-full'}
              type="button"
              size={'sm'}
            >
              CANCEL
            </Button>
            <Button
              onClick={handleResumeConfirm}
              variant={'denim'}
              className={'w-full'}
              type="button"
              size={'sm'}
            >
              CONFIRM
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
