import type { TreatmentItemAPI } from '@willow/db/client';
import { Dialog, DialogContent } from '@willow/ui/base/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@willow/ui/base/table';
import { formatDate } from '@willow/utils/format';
import {
  calculateRefillStartDateForProduct,
  getTreatmentProductStatus,
} from '@willow/utils/treatment';

import { useGetTreatment } from '~/hooks/treatments';
import { PresctiptionCard } from './PrescriptionCard';

export const TreatmentsLogsDialog = ({
  patientId,
  treatment,
  treatments,
  onClose,
}: {
  patientId: string;
  treatment: TreatmentItemAPI;
  treatments: TreatmentItemAPI[];
  onClose: () => void;
}) => {
  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="flex max-h-[calc(100vh-100px)] min-w-[1300px]">
        <div className="h-full w-1/4 max-w-40">
          <span className="text-lg text-denim">Prescription Logs</span>
        </div>
        <div className="flex flex-1 gap-x-4">
          <div
            id="treatmets-list"
            className="flex w-2/5 flex-col gap-4 overflow-y-scroll"
          >
            <PresctiptionCard patientId={patientId} treatment={treatment} />
          </div>
          <div className="flex-1">
            <TreatmentLogTable
              patientId={patientId}
              treatmentId={treatment.treatmentId}
              treatments={treatments}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

const TreatmentLogTable = ({
  patientId,
  treatmentId,
  treatments,
}: {
  patientId: string;
  treatmentId: string;
  treatments: TreatmentItemAPI[];
}) => {
  const { data: treatment, isFetching } = useGetTreatment(
    patientId,
    treatmentId,
  );

  if (isFetching) return <div>Loading...</div>;
  if (!treatment) return;

  return (
    <>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-1/4">Timestamp</TableHead>
            <TableHead className="w-1/2">Dosage</TableHead>
            <TableHead className="w-1/4">Status</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {treatment.products.map((product, index) => {
            const date = calculateRefillStartDateForProduct(treatment, index);
            return (
              <TableRow>
                <TableCell className="font-medium">
                  {date ? formatDate(date) : '-'}
                </TableCell>
                <TableCell>{product.dose}mg</TableCell>
                <TableCell>
                  {getTreatmentProductStatus(product, treatment.state)}
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
      {treatment.transferredTo &&
        (() => {
          const transferredTreatment = treatments.find(
            (t) => t.treatmentId === treatment.transferredTo,
          );
          if (transferredTreatment) {
            return (
              <div className="mt-4 text-sm text-stone/60">
                Prescription Transferred: {treatment.pharmacy} {'>'}{' '}
                {transferredTreatment.pharmacy}
              </div>
            );
          }
          return null;
        })()}
    </>
  );
};
