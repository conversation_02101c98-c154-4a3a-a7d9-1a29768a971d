import { useState } from 'react';
import { Check, Copy } from 'lucide-react';

import { useToast } from '@willow/ui/base/use-toast';

export const PatientInfoField = ({
  label,
  value,
  disabled = false,
  copiable = false,
  children,
}: {
  label: string;
  value: string;
  disabled?: boolean;
  children?: React.ReactNode;
  copiable?: boolean;
}) => {
  const { toast } = useToast();

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: 'Copied',
        variant: 'default',
        duration: 1000,
      });
    } catch {
      toast({
        title: 'An error occurred while copying to clipboard',
        variant: 'destructive',
        duration: 1000,
      });
    }
  };

  return (
    <div className="flex flex-col gap-1">
      <div className="flex flex-row items-center justify-between">
        <div className="text-xs font-medium text-stone/70">{label}</div>
        {children}
      </div>
      <div className="flex flex-row items-center gap-4">
        <div
          className={`text-xs font-normal text-dark ${
            disabled ? 'opacity-50' : ''
          }`}
        >
          {value}
        </div>
        {!disabled && copiable && (
          <button
            type="button"
            tabIndex={-1}
            className="text-left text-xs font-normal text-dark opacity-50"
            onClick={() => copyToClipboard(value)}
          >
            <Copy size={16} />
          </button>
        )}
      </div>
    </div>
  );
};
