import { useCallback, useEffect, useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';

import { Button } from '@willow/ui/base/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from '@willow/ui/base/dialog';
import { Loader } from '@willow/ui/loader';

import ErrorIcon from '~/assets/svg/error.svg';
import Logo from '~/assets/svg/logo-denim.svg';
import SuccessIcon from '~/assets/svg/success.svg';
import { useRetryPayment } from '~/hooks/treatments';

export const RetryPaymentConfirmationDialog = ({
  setOpen,
  treatmentId,
  state,
  patientId,
}: {
  treatmentId: string;
  state: string;
  patientId: string;
  setOpen: (value: boolean) => void;
}) => {
  const [isSubmitted, setSubmitted] = useState(false);

  const retryPayment = useRetryPayment(patientId, {
    onSettled: () => {
      setSubmitted(true);
    },
  });

  const handleClose = useCallback(() => {
    setOpen(false);
  }, [setOpen]);

  const handleRetryPayment = useCallback(() => {
    retryPayment.mutate({
      treatmentId,
    });
  }, [retryPayment, treatmentId]);

  const queryClient = useQueryClient();
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (state === 'inProgress.charging') {
      interval = setInterval(() => {
        void queryClient.invalidateQueries({
          queryKey: ['patient', patientId, 'treatments'],
        });
      }, 500);
    }

    return () => {
      clearInterval(interval);
    };
  }, [state, queryClient, patientId]);

  return (
    <Dialog open={true} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[500px]">
        {state === 'inProgress.charging' && (
          <ChargingStateRender handleClose={handleClose} />
        )}
        {state === 'failed' && (
          <FailedStateRender
            handleRetryPayment={handleRetryPayment}
            handleClose={handleClose}
            isSubmitted={isSubmitted}
          />
        )}
        {state === 'inProgress.waitingForPrescription' && (
          <SuccessStateRender handleClose={handleClose} />
        )}
      </DialogContent>
    </Dialog>
  );
};

const FailedStateRender = ({
  handleRetryPayment,
  handleClose,
  isSubmitted,
}: {
  handleRetryPayment: () => void;
  handleClose: () => void;
  isSubmitted: boolean;
}) => {
  if (isSubmitted)
    return (
      <div className="flex flex-col gap-8">
        <div className="mx-auto">
          <ErrorIcon />
        </div>
        <div className="mx-auto flex flex-col gap-4 text-center">
          <DialogTitle asChild>
            <p className="text-2xl font-medium leading-[34px]">
              Charged Failed
            </p>
          </DialogTitle>
          <DialogDescription asChild>
            <p className="text-base">
              Payment failed. Please check your details.
            </p>
          </DialogDescription>
        </div>
        <Button
          onClick={handleClose}
          variant={'denim'}
          className={'mx-auto w-fit min-w-[200px] py-5 text-white'}
          type="button"
          size={'sm'}
        >
          DONE
        </Button>
      </div>
    );
  return (
    <div className="flex flex-col gap-8">
      <div className="mx-auto">
        <Logo />
      </div>
      <div className="flex flex-col gap-8">
        <div className="flex flex-col gap-4 text-center">
          <DialogTitle asChild>
            <p className="text-2xl font-medium leading-[34px]">Retry Charge?</p>
          </DialogTitle>
          <DialogDescription asChild>
            <p className="text-base">
              Do you want to attempt charging this payment again?
            </p>
          </DialogDescription>
        </div>
        <div className="flex justify-end gap-4 space-x-2">
          <Button
            onClick={handleClose}
            variant={'denimOutline'}
            className={'w-full'}
            type="button"
            size={'sm'}
          >
            No
          </Button>
          <Button
            onClick={() => {
              handleRetryPayment();
            }}
            variant={'denim'}
            className={'w-full text-white'}
            type="button"
            size={'sm'}
          >
            CONFIRM
          </Button>
        </div>
      </div>
    </div>
  );
};

const ChargingStateRender = ({ handleClose }: { handleClose: () => void }) => (
  <div className="flex flex-col gap-8">
    <div className="mx-auto">
      <Logo />
    </div>
    <div className="mx-auto mt-3 flex flex-col gap-8">
      <div className="flex flex-col gap-4">
        <DialogTitle hidden />
        <Loader className="pl-4" />
        <DialogDescription asChild>
          <p className="text-base">Charging Payment...</p>
        </DialogDescription>
      </div>

      <Button
        onClick={handleClose}
        variant={'denim'}
        className={'w-full py-5 text-white'}
        type="button"
        size={'sm'}
      >
        NEVERMIND
      </Button>
    </div>
  </div>
);

const SuccessStateRender = ({ handleClose }: { handleClose: () => void }) => (
  <div className="flex flex-col gap-8">
    <div className="mx-auto">
      <SuccessIcon />
    </div>

    <div className="mx-auto flex flex-col gap-4 text-center">
      <DialogTitle asChild>
        <p className="text-2xl font-medium leading-[34px]">
          Charge Successful!
        </p>
      </DialogTitle>
      <DialogDescription asChild>
        <p className="text-base">
          The payment has been processed successfully.
        </p>
      </DialogDescription>
    </div>
    <Button
      onClick={handleClose}
      variant={'denim'}
      className={'mx-auto w-fit min-w-[200px] py-5 text-white'}
      type="button"
      size={'sm'}
    >
      DONE
    </Button>
  </div>
);
