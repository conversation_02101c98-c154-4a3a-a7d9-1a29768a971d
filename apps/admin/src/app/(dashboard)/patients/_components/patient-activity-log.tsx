import React from 'react';

import { Loader } from '@willow/ui/loader';
import { PatientActivityLog as ActivityLog } from '@willow/ui/PatientActivityLog/index';

import {
  useCreatePatientNote,
  useGetPatientActivityLog,
} from '~/hooks/patients';

export function PatientActivityLog({ patientId }: { patientId: string }) {
  const [isHideEvents, setIsHideEvents] = React.useState(false);
  const handleHideEvents = () => {
    setIsHideEvents((prev) => !prev);
  };
  const { data, isLoading, error } = useGetPatientActivityLog(patientId, {
    includes: isHideEvents
      ? 'PATIENT_NOTE_CREATED,INTERCOM_CONVERSATION_MESSAGE_CREATED'
      : undefined,
  });

  const { mutateAsync: createPatientNote, isPending: isCreateNotePending } =
    useCreatePatientNote(patientId);

  if (isLoading) return <Loader className="h-full py-32" />;
  if (error) return <div>Error: {error.message}</div>;
  if (!data) return <div>No data</div>;

  return (
    <ActivityLog
      data={data}
      isLoading={isLoading}
      error={error}
      isHideEvents={isHideEvents}
      handleHideEvents={handleHideEvents}
      onCreateNote={createPatientNote}
      isCreateNotePending={isCreateNotePending}
    />
  );
}
