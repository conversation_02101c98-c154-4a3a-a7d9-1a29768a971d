import { useState } from 'react';
import { formatISO, startOfDay } from 'date-fns';
import { SquarePen } from 'lucide-react';

import { Button } from '@willow/ui/base/button';
import { Calendar } from '@willow/ui/base/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@willow/ui/base/popover';

import { useMoveRefillDate } from '~/hooks/treatments';

export function MoveRefillDateButton({
  patientId,
  treatmentId,
  nextRefillDate,
}: {
  patientId: string;
  treatmentId: string;
  nextRefillDate: string | null;
}) {
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    nextRefillDate ? new Date(nextRefillDate) : undefined,
  );
  const moveRefillDate = useMoveRefillDate(patientId);

  const handleDateSelect = (date: Date | undefined) => {
    if (date && selectedDate) {
      date.setHours(selectedDate.getHours());
      date.setMinutes(selectedDate.getMinutes());
      date.setSeconds(selectedDate.getSeconds());
      date.setMilliseconds(selectedDate.getMilliseconds());
      setSelectedDate(date);
    } else {
      setSelectedDate(date);
    }
  };

  const handleDateConfirm = () => {
    if (selectedDate) {
      moveRefillDate.mutate({
        treatmentId,
        date: formatISO(selectedDate),
      });
      setIsDatePickerOpen(false);
    }
  };
  return (
    <Popover open={isDatePickerOpen} onOpenChange={setIsDatePickerOpen}>
      <PopoverTrigger asChild>
        <button
          className="flex h-5 w-5 items-center justify-center"
          title="Move next refill date"
          type="button"
        >
          <SquarePen size={16} color="#666" />
        </button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="single"
          selected={selectedDate}
          onSelect={handleDateSelect}
          disabled={(date) => date.getTime() < startOfDay(new Date()).getTime()}
          initialFocus
        />
        <div className="flex justify-end gap-2 p-2">
          <Button
            variant={'denimOutline'}
            size="xs"
            className="border py-4"
            type="reset"
            onClick={() => setIsDatePickerOpen(false)}
          >
            Cancel
          </Button>
          <Button
            variant={'denim'}
            className="border border-denim px-6 py-4 text-white"
            size="xs"
            onClick={handleDateConfirm}
          >
            Done
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}
