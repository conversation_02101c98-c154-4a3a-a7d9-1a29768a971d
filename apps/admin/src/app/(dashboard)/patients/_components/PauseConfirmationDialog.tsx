import { useCallback, useEffect, useState } from 'react';
import { startOfDay } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';

import { Button } from '@willow/ui/base/button';
import { Calendar } from '@willow/ui/base/calendar';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from '@willow/ui/base/dialog';
import { Label } from '@willow/ui/base/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@willow/ui/base/popover';
import { RadioGroup, RadioGroupItem } from '@willow/ui/base/radio-group';
import { formatDate } from '@willow/utils/format';

import Logo from '~/assets/svg/logo-denim.svg';
import { usePauseTreatment } from '~/hooks/treatments';

export const PauseConfirmationDialog = ({
  open,
  setOpen,
  treatmentId,
  patientId,
}: {
  open: boolean;
  treatmentId: string;
  patientId: string;
  setOpen: (value: boolean) => void;
}) => {
  const pauseTreatment = usePauseTreatment(patientId, {
    onSettled: () => {
      setOpen(false);
    },
  });

  const handlePauseConfirm = useCallback(
    (date?: Date) => {
      pauseTreatment.mutate({
        treatmentId,
        date: date?.toISOString(),
      });
    },
    [pauseTreatment, treatmentId],
  );

  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [pauseType, setPauseType] = useState<'indefinite' | 'date'>(
    'indefinite',
  );

  const isConfirmDisabled = pauseType === 'date' && !selectedDate;

  useEffect(() => {
    if (!open) {
      setPauseType('indefinite');
      setSelectedDate(undefined);
    }
  }, [open]);

  const handleDateSelect = (date: Date | undefined) => {
    setSelectedDate(date);
    if (date) {
      setIsDatePickerOpen(false);
    }
  };

  const handleConfirm = () => {
    handlePauseConfirm(pauseType === 'date' ? selectedDate : undefined);
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <div className="mx-auto">
          <Logo />
        </div>
        <div className="flex flex-col items-center gap-4 text-center">
          <div>
            <DialogTitle asChild>
              <p className="text-2xl font-medium leading-[34px]">
                Pause Prescription
              </p>
            </DialogTitle>
            <DialogDescription asChild>
              <p className="text-base">
                Are you sure you want to pause this Prescription?
              </p>
            </DialogDescription>
          </div>

          <RadioGroup
            value={pauseType}
            onValueChange={(value) => {
              setPauseType(value as 'indefinite' | 'date');
              if (value === 'indefinite') {
                setSelectedDate(undefined);
              }
            }}
            className="flex gap-6"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="indefinite" id="indefinite" />
              <Label htmlFor="indefinite">Indefinite</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="date" id="date" />
              <Label htmlFor="date">Set a date</Label>
            </div>
          </RadioGroup>

          {pauseType === 'date' && (
            <div className="w-full">
              <Popover
                open={isDatePickerOpen}
                onOpenChange={setIsDatePickerOpen}
              >
                <PopoverTrigger asChild>
                  <div className="flex h-10 w-full cursor-pointer items-center gap-2 border-b border-denim py-2 text-sm">
                    <CalendarIcon className="text-denim" />
                    <span>
                      {selectedDate
                        ? formatDate(selectedDate)
                        : 'Select a date'}
                    </span>
                  </div>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={handleDateSelect}
                    disabled={(date) => {
                      const today = startOfDay(new Date());
                      const maxDate = new Date(today);
                      maxDate.setDate(maxDate.getDate() + 30);
                      return (
                        date.getTime() <= today.getTime() ||
                        date.getTime() > maxDate.getTime()
                      );
                    }}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          )}
          <div className="flex justify-end gap-4 space-x-2">
            <Button
              onClick={() => {
                setOpen(false);
              }}
              variant={'denimOutline'}
              className={'w-full'}
              type="button"
              size={'sm'}
            >
              No
            </Button>
            <Button
              onClick={() => {
                handleConfirm();
              }}
              variant={'denim'}
              className={'w-full'}
              type="button"
              size={'sm'}
              disabled={isConfirmDisabled}
            >
              CONFIRM
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
