import { useCallback, useState } from 'react';
import Image from 'next/image';

import {
  Dialog,
  DialogContent,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from '@willow/ui/base/dialog';
import { useToast } from '@willow/ui/base/use-toast';
import { ImageUploadDialog } from '@willow/ui/image-upload-dialog';
import { useInvalidatedQuery } from '@willow/utils/react-query';

import { usePatientAssetLink } from '~/hooks/links';
import { useUpdatePatientPhoto } from '~/hooks/patients';

export const PatientUploadIdDialog = ({
  open,
  onClose,
  patientId,
  idPhoto,
  facePhoto,
}: {
  open: boolean;
  patientId: string;
  idPhoto: string;
  facePhoto: string;
  onClose: (value: boolean) => void;
}) => {
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="p-8 sm:max-w-5xl">
        <div className="grid grid-cols-6 gap-8">
          <div className="text-2xl font-medium text-denim">
            <h3>Update ID/Photo</h3>
          </div>
          <div className="flex flex-col justify-between gap-5 xl:flex-row">
            <DisplayPhoto
              patientId={patientId}
              type="id-photo"
              title="ID"
              photoPath={idPhoto}
            />
            <DisplayPhoto
              patientId={patientId}
              type="face-photo"
              title="Photo"
              photoPath={facePhoto}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

const DisplayPhoto = ({
  type,
  patientId,
  title,
  photoPath,
}: {
  type: 'face-photo' | 'id-photo';
  patientId: string;
  title: string;
  photoPath: string;
}) => {
  const { toast } = useToast();
  const invalidate = useInvalidatedQuery();

  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);

  const { genAssetLink, invalidateAssetsCache } = usePatientAssetLink();

  const { mutateAsync: upload, isPending } = useUpdatePatientPhoto(
    patientId,
    type,
  );

  const onUpload = useCallback(
    (image: Blob) =>
      upload(image, {
        onSuccess: () => {
          void invalidate(['patients', patientId]);
          invalidateAssetsCache();
        },
        onError: (error) => {
          toast({
            variant: 'destructive',
            title: 'Error uploading photo',
            description: (error as Error).message,
          });
        },
      }) as Promise<void>,
    [upload, invalidateAssetsCache, toast],
  );

  return (
    <div className="flex basis-full flex-col gap-4">
      <div className="text-sm font-semibold text-denim">{title}</div>
      <div
        className="h-fit w-fit cursor-pointer rounded-2xl border border-stone-300 bg-stone-100 p-6"
        onClick={() => {
          if (photoPath) setIsPreviewModalOpen(true);
        }}
      >
        {photoPath ? (
          <div className="min-w-xs relative h-52 w-80">
            <Image
              src={genAssetLink(photoPath)}
              alt={`patient ${title}`}
              objectFit="fill"
              layout="fill"
            />
          </div>
        ) : (
          <div className="flex h-52 w-80 items-center justify-center">
            <div className="text-sm text-stone-300">No {title} uploaded</div>
          </div>
        )}
      </div>
      <div className="flex justify-end">
        <ImageUploadDialog onSave={onUpload} isLoading={isPending} />
      </div>

      <Dialog open={isPreviewModalOpen} onOpenChange={setIsPreviewModalOpen}>
        <DialogContent className="flex h-[80vh] max-h-[80vh] w-[80vw] max-w-[80vw] flex-col p-0">
          <DialogHeader className="border-b bg-white p-4">
            <DialogTitle>{title}</DialogTitle>
          </DialogHeader>
          <div className="relative w-full flex-1 overflow-hidden">
            <Image
              src={genAssetLink(photoPath)}
              alt={`patient ${title}`}
              layout="fill"
              objectFit="contain"
            />
          </div>
          <DialogFooter className="bg-white p-1"></DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
