import { useCallback } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';

import type { CancelPatientSchema } from '@willow/utils/patient/cancel';
import { Button } from '@willow/ui/base/button';
import { Dialog, DialogContent } from '@willow/ui/base/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@willow/ui/base/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@willow/ui/base/select';
import { Textarea } from '@willow/ui/base/textarea';
import {
  cancelPatientInternalReasons,
  zCancelPatientSchema,
} from '@willow/utils/patient/cancel';

import { useCancelPatient, useGetPatientInfo } from '~/hooks/patients';

export const CancelPatientDialog = ({
  open,
  setOpen,
  patientId,
}: {
  open: boolean;
  patientId: string;
  setOpen: (value: boolean) => void;
}) => {
  const handleClose = useCallback(() => {
    setOpen(false);
  }, [setOpen]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="p-8 sm:max-w-[690px]">
        <div className="grid grid-cols-4 gap-8">
          <div className="text-2xl font-medium text-denim">
            <h3>Cancel Patient</h3>
          </div>
          <div className="col-span-3">
            <CancelPatientForm
              patientId={patientId}
              handleClose={handleClose}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

const CancelPatientForm = ({
  patientId,
  handleClose,
}: {
  patientId: string;
  handleClose: () => void;
}) => {
  const { data: patient } = useGetPatientInfo(patientId);

  const form = useForm<CancelPatientSchema>({
    resolver: zodResolver(zCancelPatientSchema),
  });

  const { mutateAsync: cancelPatient, isPending } = useCancelPatient({
    patientId,
  });

  const onSubmitHandler = async (data: CancelPatientSchema) => {
    await cancelPatient(data, {
      onSuccess: () => {
        handleClose();
      },
    });
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmitHandler)}
        className="space-y-8 px-4"
      >
        <p className="text-denim">
          <span>for</span>
          {'  '}
          <span className="text-base font-semibold">
            {patient?.user.firstName} {patient?.user.lastName}
          </span>
        </p>

        <FormField
          control={form.control}
          name="reason"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-normal text-denim">
                Cancellation reason
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger className="h-8 items-center justify-between rounded-none !border-b border-solid border-denim py-2 pl-0 text-sm text-black">
                    <SelectValue placeholder="Select option" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {cancelPatientInternalReasons.map((reason) => (
                    <SelectItem key={reason} value={reason} className="text-sm">
                      {reason}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex flex-col gap-2">
          <label htmlFor="reason" className="text-sm font-normal text-denim">
            Further explanation
          </label>

          <Textarea
            className="rounded-sm border border-denim px-2 py-2"
            placeholder="Write here..."
            disabled={isPending}
            {...form.register('note')}
          />

          {form.formState.errors.note && (
            <p className="mt-4 text-sm text-destructive">
              {form.formState.errors.note.message}
            </p>
          )}
        </div>
        <div className="flex w-full gap-4">
          <Button
            variant={'denimOutline'}
            className="flex-1 border text-sm font-light uppercase"
            size="sm"
            type="reset"
            onClick={handleClose}
            disabled={isPending}
          >
            Cancel
          </Button>
          <Button
            variant={'denim'}
            className="flex-1 text-sm font-light uppercase text-white"
            size="sm"
            type="submit"
            loading={isPending}
            disabled={isPending}
          >
            Submit Cancellation
          </Button>
        </div>
      </form>
    </Form>
  );
};
