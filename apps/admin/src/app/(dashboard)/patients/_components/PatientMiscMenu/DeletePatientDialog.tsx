import { useCallback } from 'react';
import { AlertTriangle } from 'lucide-react';
import { useForm } from 'react-hook-form';

import { Button } from '@willow/ui/base/button';
import { Dialog, DialogContent } from '@willow/ui/base/dialog';
import { Form } from '@willow/ui/base/form';

import {
  useGetPatientInfo,
  usePermanentlyDeletePatient,
} from '~/hooks/patients';

export const DeletePatientDialog = ({
  open,
  setOpen,
  patientId,
  closeInfoDialog,
}: {
  open: boolean;
  patientId: string;
  setOpen: (value: boolean) => void;
  closeInfoDialog: () => void;
}) => {
  const handleClose = useCallback(() => {
    setOpen(false);
    closeInfoDialog();
  }, [setOpen]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="p-0 sm:max-w-[690px]">
        <div className="relative">
          <div className="flex flex-col items-center px-8 pb-8 pt-12">
            <div className="mb-6 rounded-full bg-amber-400 p-4">
              <AlertTriangle className="h-6 w-6 text-white" />
            </div>

            <h3 className="mb-4 text-2xl font-bold text-denim">
              Delete Patient
            </h3>

            <p className="mb-8 text-center text-gray-600">
              Deleting a patient permanently removes them from the Willow
              system. If a patient has been accepted by a doctor then
              <span className="font-bold"> do not delete them</span>.
            </p>

            <DeletePatientForm
              patientId={patientId}
              handleClose={handleClose}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

const DeletePatientForm = ({
  patientId,
  handleClose,
}: {
  patientId: string;
  handleClose: () => void;
}) => {
  const form = useForm({});
  const { data: patient } = useGetPatientInfo(patientId);

  const { mutateAsync: deletePatient, isPending } =
    usePermanentlyDeletePatient();

  const onSubmitHandler = async () => {
    await deletePatient(patientId, {
      onSuccess: () => {
        handleClose();
      },
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmitHandler)} className="w-full">
        <div className="flex w-full gap-4">
          <Button
            variant="denimOutline"
            className="flex-1 border text-sm font-light uppercase"
            type="reset"
            onClick={handleClose}
            disabled={isPending}
          >
            Cancel
          </Button>
          <Button
            variant="denim"
            className="flex-1 bg-denim text-sm font-light uppercase text-white hover:bg-denim/90"
            type="submit"
            loading={isPending}
            disabled={isPending || !!patient?.doctor}
          >
            Permanently Delete
          </Button>
        </div>
      </form>
    </Form>
  );
};
