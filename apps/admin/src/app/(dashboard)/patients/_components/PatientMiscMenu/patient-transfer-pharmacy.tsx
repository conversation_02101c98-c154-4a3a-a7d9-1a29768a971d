import { useCallback } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@willow/ui/base/button';
import { Dialog, DialogContent } from '@willow/ui/base/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@willow/ui/base/form';
import { useToast } from '@willow/ui/base/use-toast';

import {
  useGetAvailablePharmaciesInState,
  useTransferPatientToPharmacy,
} from '~/hooks/pharmacy';

const zFormSchema = z.object({
  newPharmacyId: z.string().min(1, {
    message: 'Please select a pharmacy',
  }),
});

type FormSchema = z.infer<typeof zFormSchema>;

export const PatientTransferPharmacyDialog = ({
  open,
  setOpen,
  patientId,
  currentPharmacyId,
  currentPharmacyName,
  patientStateId,
}: {
  open: boolean;
  patientId: string;
  currentPharmacyId: string | null;
  currentPharmacyName: string | null;
  setOpen: (value: boolean) => void;
  patientStateId: string;
}) => {
  const handleClose = useCallback(() => {
    setOpen(false);
  }, [setOpen]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="p-8 sm:max-w-[690px]">
        <div className="grid grid-cols-4 gap-8">
          <div className="text-2xl font-medium text-denim">
            <h3>Transfer Pharmacy</h3>
          </div>
          <div className="col-span-3">
            <TransferPharmacyForm
              patientId={patientId}
              currentPharmacyId={currentPharmacyId}
              currentPharmacyName={currentPharmacyName}
              handleClose={handleClose}
              patientStateId={patientStateId}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

const TransferPharmacyForm = ({
  patientId,
  currentPharmacyId,
  currentPharmacyName,
  handleClose,
  patientStateId,
}: {
  patientId: string;
  currentPharmacyId: string | null;
  currentPharmacyName: string | null;
  handleClose: () => void;
  patientStateId: string;
}) => {
  const { toast } = useToast();
  const form = useForm<FormSchema>({
    resolver: zodResolver(zFormSchema),
    defaultValues: {
      newPharmacyId: '',
    },
  });

  const { data: pharmacies = [], isLoading: isLoadingPharmacies } =
    useGetAvailablePharmaciesInState(patientStateId);

  // Filter out the current pharmacy from the list
  const availablePharmacies = pharmacies.filter(
    (pharmacy) => pharmacy.id !== currentPharmacyId,
  );

  const { mutate: transferPatient, isPending } =
    useTransferPatientToPharmacy(patientId);

  const onSubmitHandler = (data: FormSchema) => {
    transferPatient(
      { newPharmacyId: data.newPharmacyId },
      {
        onSuccess: () => {
          toast({
            title: 'Success',
            description: 'Patient has been transferred to the new pharmacy',
          });
          handleClose();
        },
        onError: (error: any) => {
          toast({
            variant: 'destructive',
            title: 'Error',
            description:
              error?.response?.data?.message || 'Failed to transfer patient',
          });
        },
      },
    );
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmitHandler)}
        className="space-y-8 px-4"
      >
        {currentPharmacyName && (
          <div className="mb-4">
            <FormLabel>Current Pharmacy</FormLabel>
            <div className="mt-2 rounded-md border border-gray-200 bg-gray-50 px-3 py-2 text-sm">
              {currentPharmacyName}
            </div>
          </div>
        )}

        {isLoadingPharmacies ? (
          <div className="flex items-center justify-center py-4">
            <div className="h-5 w-5 animate-spin rounded-full border-2 border-denim border-t-transparent"></div>
            <span className="ml-2 text-sm text-gray-600">
              Loading pharmacies...
            </span>
          </div>
        ) : availablePharmacies.length === 0 ? (
          <div className="rounded-md border border-yellow-200 bg-yellow-50 p-4 text-sm text-yellow-800">
            No other pharmacies are available for transfer. Please ensure there
            are other enabled pharmacies in the system.
          </div>
        ) : (
          <FormField
            control={form.control}
            name="newPharmacyId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>New Pharmacy</FormLabel>
                <FormControl>
                  <select
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    {...field}
                    disabled={isPending || isLoadingPharmacies}
                  >
                    <option value="">Select a pharmacy</option>
                    {availablePharmacies.map((pharmacy) => (
                      <option key={pharmacy.id} value={pharmacy.id}>
                        {pharmacy.name}
                      </option>
                    ))}
                  </select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}
        <div className="grid grid-cols-2 gap-4">
          <Button
            onClick={handleClose}
            variant="denimOutline"
            size="sm"
            className="w-full uppercase"
            disabled={isPending}
            type="button"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="denim"
            disabled={
              isPending ||
              !form.getValues().newPharmacyId ||
              availablePharmacies.length === 0 ||
              isLoadingPharmacies
            }
            size="sm"
            className="w-full uppercase text-white"
            loading={isPending}
          >
            Transfer
          </Button>
        </div>
      </form>
    </Form>
  );
};
