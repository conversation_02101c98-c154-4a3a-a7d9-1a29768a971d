import { useCallback, useEffect, useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, useFormContext } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@willow/ui/base/button';
import { Checkbox } from '@willow/ui/base/checkbox';
import { Dialog, DialogContent } from '@willow/ui/base/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@willow/ui/base/form';
import { Input } from '@willow/ui/base/input';
import { Label } from '@willow/ui/base/label';
import { normalizeString } from '@willow/utils/format';
import { zAddress } from '@willow/utils/patient/address';

import { useGetPatientInfo, useUpdatePatientAddresses } from '~/hooks/patients';

export const PatientUpdateAddressesDialog = ({
  open,
  setOpen,
  patientId,
}: {
  open: boolean;
  patientId: string;
  setOpen: (value: boolean) => void;
}) => {
  const handleClose = useCallback(() => {
    setOpen(false);
  }, [setOpen]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="my-4 flex max-h-[90vh] min-h-fit p-8 sm:max-w-4xl">
        <div className="flex min-h-0 w-full grid-cols-4 gap-8 lg:grid">
          <div className="text-2xl font-medium text-denim">
            <h3>Edit Addresses</h3>
          </div>
          <div className="col-span-3 max-h-[calc(90vh-8rem)] overflow-y-auto">
            <PatientUpdatePatientAddressForm
              patientId={patientId}
              handleClose={handleClose}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

const zFormSchema = z.object({
  shippingAddress: zAddress.omit({ state: true, phone: true }),
  billingAddress: zAddress.omit({ state: true, phone: true }),
});
type FormSchema = z.infer<typeof zFormSchema>;
const PatientUpdatePatientAddressForm = ({
  patientId,
  handleClose,
}: {
  patientId: string;
  handleClose: () => void;
}) => {
  const [isBillingSameAsShipping, setIsBillingSameAsShipping] = useState(false);
  const { data: patient } = useGetPatientInfo(patientId);

  const form = useForm<FormSchema>({
    resolver: zodResolver(zFormSchema),
  });

  useEffect(() => {
    if (
      normalizeString(patient?.shippingAddress?.address1) ===
        normalizeString(patient?.billingAddress?.address1) &&
      normalizeString(patient?.shippingAddress?.address2) ===
        normalizeString(patient?.billingAddress?.address2) &&
      normalizeString(patient?.shippingAddress?.city) ===
        normalizeString(patient?.billingAddress?.city) &&
      normalizeString(patient?.shippingAddress?.state?.code) ===
        normalizeString(patient?.billingAddress?.state?.code) &&
      normalizeString(patient?.shippingAddress?.zip) ===
        normalizeString(patient?.billingAddress?.zip)
    ) {
      setIsBillingSameAsShipping(true);
    }
  }, [patient]);

  useEffect(() => {
    form.reset({
      shippingAddress: patient?.shippingAddress
        ? {
            ...patient?.shippingAddress,
            address2: patient?.shippingAddress?.address2 ?? undefined,
          }
        : undefined,
      billingAddress: patient?.billingAddress
        ? {
            ...patient?.billingAddress,
            address2: patient?.billingAddress?.address2 ?? undefined,
          }
        : undefined,
    });
  }, [form, patient]);

  const { mutate: update, isPending } = useUpdatePatientAddresses(patientId);

  const onSubmitHandler = (data: FormSchema) => {
    update(
      {
        shippingAddress: data.shippingAddress,
        billingAddress: isBillingSameAsShipping
          ? undefined
          : data.billingAddress,
        isBillingIsSameAsShipping: isBillingSameAsShipping,
      },
      {
        onSuccess: () => {
          handleClose();
        },
      },
    );
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmitHandler)}
        className="space-y-8 px-4"
      >
        <div className="space-y-4">
          <div className="text-sm font-bold text-denim">
            <h4>Shipping Address</h4>
          </div>
          <AddressForm
            state={patient?.state.code ?? ''}
            address="shippingAddress"
          />
        </div>

        <div className="space-y-4">
          <div className="text-sm font-bold text-denim">
            <h4>Billing Address</h4>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="same-as-shipping"
              className="border-denim"
              checked={isBillingSameAsShipping}
              onCheckedChange={(val) => {
                setIsBillingSameAsShipping(
                  val === 'indeterminate' ? true : val,
                );
                if (val) {
                  form.setValue(
                    'billingAddress',
                    form.getValues('shippingAddress'),
                    { shouldDirty: true },
                  );
                }
              }}
            />
            <label
              htmlFor="same-as-shipping"
              className="text-md pt-[2px] font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Same as Shipping Address
            </label>
          </div>

          <div className="text-sm text-stone">
            <span className="font-bold">Note:</span> If you wish to update the
            patient's shipping state, you may use the Transfer State function on
            the patient profile dropdown.
          </div>

          {!isBillingSameAsShipping && (
            <AddressForm
              state={patient?.state.code ?? ''}
              address="billingAddress"
            />
          )}
        </div>

        <div className="grid grid-cols-2 gap-4">
          <Button
            onClick={handleClose}
            variant="denimOutline"
            type="button"
            size="sm"
            className="w-full uppercase"
            disabled={isPending}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="denim"
            disabled={isPending || !form.formState.isDirty}
            size="sm"
            className="w-full uppercase text-white"
            loading={isPending}
          >
            Save
          </Button>
        </div>
      </form>
    </Form>
  );
};

const AddressForm = ({
  address,
  state,
}: {
  address: 'shippingAddress' | 'billingAddress';
  state: string;
}) => {
  const form = useFormContext<FormSchema>();

  return (
    <div className="grid grid-cols-1 gap-8">
      <FormField
        control={form.control}
        name={`${address}.address1`}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Street Address</FormLabel>
            <FormControl>
              <Input
                variant="denim"
                size="sm"
                className="!pl-0 pt-0"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name={`${address}.address2`}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Apartment/Suite</FormLabel>
            <FormControl>
              <Input
                variant="denim"
                size="sm"
                className="!pl-0 pt-0"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <div className="grid grid-cols-1 gap-8 lg:grid-cols-3 lg:gap-4">
        <FormField
          control={form.control}
          name={`${address}.city`}
          render={({ field }) => (
            <FormItem>
              <FormLabel>City</FormLabel>
              <FormControl>
                <Input
                  variant="denim"
                  size="sm"
                  placeholder="Phone"
                  className="!pl-0 pt-0"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="space-y-2">
          <Label className="text-stone/60">State</Label>
          <Input
            variant="denim"
            size="sm"
            className="cursor-not-allowed border-stone/60 !bg-white !pl-0 pt-0 text-stone/60"
            value={state}
            disabled={true}
          />
        </div>

        {/* <FormField
          control={form.control}
          name={`${address}.state` as any}
          disabled={true}
          defaultValue={state}
          render={() => {
            return (
              <FormItem>
                <FormLabel>State</FormLabel>
                <FormControl>
                  <Input
                    variant="denim"
                    size="sm"
                    className="!pl-0 pt-0"
                    value={state}
                    disabled={true}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            );
          }}
        /> */}
        <FormField
          control={form.control}
          name={`${address}.zip`}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Zip Code</FormLabel>
              <FormControl>
                <Input
                  variant="denim"
                  size="sm"
                  className="!pl-0 pt-0"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
};
