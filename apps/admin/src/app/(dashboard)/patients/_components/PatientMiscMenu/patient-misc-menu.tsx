import { use<PERSON>allback, useMemo, useState } from 'react';
import { EllipsisIcon } from 'lucide-react';

import type { AdminPatientById } from '@willow/db/client';
import { cn } from '@willow/ui';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@willow/ui/base/dropdown-menu';
import { useToast } from '@willow/ui/base/use-toast';

import { DeletePatientDialog } from '~/app/(dashboard)/patients/_components/PatientMiscMenu/DeletePatientDialog';
import { useImpersonateUser } from '~/hooks/patients';
import { CancelPatientDialog } from './CancelPatientDialog';
import { PatientResetPasswordDialog } from './patient-reset-password';
import { SetPatientPassword } from './patient-set-password';
import { PatientTransferPharmacyDialog } from './patient-transfer-pharmacy';
import { PatientTransferStateDialog } from './patient-transfer-state';
import { PatientUpdateAddressesDialog } from './patient-update-addresses';
import { UpdatePatientInfoDialog } from './patient-update-info';
import { PatientUploadIdDialog } from './patient-upload-id';
import { ReassignDoctor } from './ReassignDoctor';
import { RestorePatientSubscriptionDialog } from './RestorePatientSubscriptionDialog';

interface ActionType {
  name: string;
  handleAction?: () => void;
  className?: string;
}

export const PatientMiscMenu = ({
  patient,
  setIsDisplayIntakeHistory,
  closeInfoDialog,
}: {
  patient: AdminPatientById;
  setIsDisplayIntakeHistory: (value: boolean) => void;
  closeInfoDialog: () => void;
}) => {
  const { toast } = useToast();
  const { mutate: impersonateUser } = useImpersonateUser();
  const [openSetPasswordDialog, setOpenSetPasswordDialog] = useState(false);
  const [openCancelDialog, setOpenCancelDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openRestoreSubscriptionDialog, setOpenRestoreSubscriptionDialog] =
    useState(false);
  const [openResetPasswordDialog, setOpenResetPasswordDialog] = useState(false);
  const [openUploadIDDialog, setOpenUploadIDDialog] = useState(false);
  const [openTransferStateDialog, setOpenTransferStateDialog] = useState(false);
  const [openTransferPharmacyDialog, setOpenTransferPharmacyDialog] =
    useState(false);
  const [openUpdateInfoDialog, setOpenUpdateInfoDialog] = useState(false);
  const [openReassignDoctorDialog, setOpenReassignDoctorDialog] =
    useState(false);
  const [openUpdateAddressesDialog, setOpenUpdateAddressesDialog] =
    useState(false);

  const handleImpersonateClick = useCallback(() => {
    impersonateUser(
      { userId: patient.userId },
      {
        onSuccess: (data) => {
          console.log('data', data);
          window.open(data.url, '_blank');
        },
        onError: (error) => {
          toast({
            variant: 'destructive',
            title: 'Error generating impersonation link',
            description: error?.message ?? 'An error occurred',
          });
        },
      },
    );
  }, [impersonateUser, toast, patient.userId]);

  const handleCancelClick = useCallback(() => {
    if (patient.status === 'cancelled') {
      setOpenRestoreSubscriptionDialog(true);
    } else {
      setOpenCancelDialog(true);
    }
  }, [patient.status, setOpenCancelDialog, setOpenRestoreSubscriptionDialog]);

  const actions: ActionType[] = useMemo(
    () => [
      {
        name: 'Impersonate',
        handleAction: () => {
          void handleImpersonateClick();
        },
      },
      {
        name: 'Update Info',
        handleAction: () => setOpenUpdateInfoDialog(true),
      },
      {
        name: 'Update ID/Photo',
        handleAction: () => setOpenUploadIDDialog(true),
      },
      {
        name: 'Update Addresses',
        handleAction: () => setOpenUpdateAddressesDialog(true),
      },
      {
        name: 'Reset Password',
        handleAction: () => setOpenResetPasswordDialog(true),
      },
      { name: 'separator-1' },
      {
        name: 'View Intake History',
        handleAction: () => setIsDisplayIntakeHistory(true),
      },
      {
        name: 'Transfer State',
        handleAction: () => setOpenTransferStateDialog(true),
      },
      {
        name: 'Transfer Pharmacy',
        handleAction: () => setOpenTransferPharmacyDialog(true),
      },
      {
        name: 'Set Password',
        handleAction: () => setOpenSetPasswordDialog(true),
      },
      ...(patient.doctor
        ? [
            {
              name: 'Reassign Doctor',
              handleAction: () => setOpenReassignDoctorDialog(true),
            },
          ]
        : []),
      { name: 'separator-2' },
      {
        name: patient.status === 'cancelled' ? 'Uncancel' : 'Cancel',
        className: 'text-red-500',
        handleAction: () => handleCancelClick(),
      },
      {
        name: 'Delete Patient',
        className: 'text-red-500',
        handleAction: () => setOpenDeleteDialog(true),
      },
    ],
    [
      patient.doctor,
      patient.status,
      handleImpersonateClick,
      setIsDisplayIntakeHistory,
      handleCancelClick,
    ],
  );

  return (
    <div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <div className="cursor-pointer">
            <EllipsisIcon size={24} color="#2F4C78" />
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-44 px-0" align="end">
          {actions.map((action) => {
            if (action.name.includes('separator'))
              return (
                <DropdownMenuSeparator
                  key={action.name}
                  className="mx-auto w-10/12"
                />
              );
            return (
              <DropdownMenuItem
                key={action.name}
                onSelect={() => {
                  if (action.handleAction) {
                    action.handleAction();
                  }
                }}
                className={cn(
                  'cursor-pointer px-4 py-3 text-xs font-normal focus:bg-[#ffecca] focus:text-[#2f4c78]',
                  action.className,
                )}
              >
                {action.name}
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>
      <UpdatePatientInfoDialog
        open={openUpdateInfoDialog}
        patientId={patient.id}
        setOpen={setOpenUpdateInfoDialog}
      />
      <SetPatientPassword
        open={openSetPasswordDialog}
        patientId={patient.id}
        setOpen={setOpenSetPasswordDialog}
      />
      <PatientUpdateAddressesDialog
        open={openUpdateAddressesDialog}
        patientId={patient.id}
        setOpen={setOpenUpdateAddressesDialog}
      />
      <CancelPatientDialog
        open={openCancelDialog}
        patientId={patient.id}
        setOpen={setOpenCancelDialog}
      />
      <RestorePatientSubscriptionDialog
        open={openRestoreSubscriptionDialog}
        setOpen={setOpenRestoreSubscriptionDialog}
        patientId={patient.id}
      />
      <DeletePatientDialog
        open={openDeleteDialog}
        patientId={patient.id}
        setOpen={setOpenDeleteDialog}
        closeInfoDialog={closeInfoDialog}
      />
      <ReassignDoctor
        open={openReassignDoctorDialog}
        patientId={patient.id}
        firstName={patient.user.firstName}
        lastName={patient.user.lastName}
        stateCode={patient.state.code}
        doctorId={patient.doctor?.id ?? ''}
        setOpen={setOpenReassignDoctorDialog}
      />
      <PatientResetPasswordDialog
        open={openResetPasswordDialog}
        firstName={patient.user.firstName}
        lastName={patient.user.lastName}
        email={patient.user.email}
        patientId={patient.id}
        onClose={() => setOpenResetPasswordDialog(false)}
      />
      <PatientUploadIdDialog
        open={openUploadIDDialog}
        idPhoto={patient.idPhoto}
        facePhoto={patient.facePhoto}
        patientId={patient.id}
        onClose={() => setOpenUploadIDDialog(false)}
      />
      <PatientTransferStateDialog
        open={openTransferStateDialog}
        patientId={patient.id}
        patientFullName={`${patient.user.firstName} ${patient.user.lastName}`}
        currentDoctorFullName={`${patient.doctor?.user.firstName} ${patient.doctor?.user.lastName}`}
        hasNoDoctor={!patient.doctor}
        pharmacyName={patient.pharmacy?.name}
        setOpen={setOpenTransferStateDialog}
      />
      <PatientTransferPharmacyDialog
        open={openTransferPharmacyDialog}
        patientId={patient.id}
        currentPharmacyId={patient.pharmacy?.id || null}
        currentPharmacyName={patient.pharmacy?.name || null}
        setOpen={setOpenTransferPharmacyDialog}
        patientStateId={patient.state.id}
      />
    </div>
  );
};
