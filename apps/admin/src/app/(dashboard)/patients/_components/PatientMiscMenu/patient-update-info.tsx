import { useCallback, useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { formatInTimeZone } from 'date-fns-tz';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@willow/ui/base/button';
import { DateInput } from '@willow/ui/base/date-input';
import { Dialog, DialogContent } from '@willow/ui/base/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@willow/ui/base/form';
import { Input } from '@willow/ui/base/input';
import { zBirthDate } from '@willow/utils/patient/birthday';

import { useGetPatientInfo, useUpdatePatientInfo } from '~/hooks/patients';

export const UpdatePatientInfoDialog = ({
  open,
  setOpen,
  patientId,
}: {
  open: boolean;
  patientId: string;
  setOpen: (value: boolean) => void;
}) => {
  const handleClose = useCallback(() => {
    setOpen(false);
  }, [setOpen]);
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="p-8 sm:max-w-[690px]">
        <div className="grid grid-cols-4 gap-8">
          <div className="text-2xl font-medium text-denim">
            <h3>Update Profile</h3>
          </div>
          <div className="col-span-3">
            <UpdateInfoForm patientId={patientId} handleClose={handleClose} />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

const formatDate = (date: string) => {
  return formatInTimeZone(new Date(date), 'UTC', 'MM/dd/yyyy');
};

const zFormSchema = z.object({
  firstName: z.string().min(1, {
    message: 'First name cannot be empty',
  }),
  lastName: z.string().min(1, {
    message: 'Last name cannot be empty',
  }),
  phone: z.string().min(1, { message: 'Phone cannot be empty' }),
  birthDate: zBirthDate,
  email: z.string().trim().email({ message: 'Invalid email address' }),
});

type FormSchema = z.infer<typeof zFormSchema>;

const UpdateInfoForm = ({
  patientId,
  handleClose,
}: {
  patientId: string;
  handleClose: () => void;
}) => {
  const { data: patient } = useGetPatientInfo(patientId);

  const form = useForm<FormSchema>({
    resolver: zodResolver(zFormSchema),
  });

  useEffect(() => {
    if (!patient) return;
    form.reset({
      firstName: patient.user.firstName,
      lastName: patient.user.lastName,
      phone: patient.user.phone ?? undefined,
      birthDate: patient.birthDate ? formatDate(patient.birthDate) : undefined,
      email: patient.user.email,
    });
  }, [form, patient]);

  const { mutate: updatePatientInfo, isPending } =
    useUpdatePatientInfo(patientId);

  const onSubmitHandler = (data: FormSchema) => {
    void updatePatientInfo(
      { patientId, updatedData: data },
      {
        onSuccess: () => {
          handleClose();
        },
      },
    );
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmitHandler)}
        className="space-y-8 px-4"
      >
        <div className="grid grid-cols-2 gap-8">
          <FormField
            control={form.control}
            name="firstName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Patient First Name</FormLabel>
                <FormControl>
                  <Input
                    variant="denim"
                    size="sm"
                    className="!pl-0 pt-0"
                    placeholder="First Name"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="lastName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Patient Last Name</FormLabel>
                <FormControl>
                  <Input
                    variant="denim"
                    size="sm"
                    className="!pl-0 pt-0"
                    placeholder="Last Name"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Phone</FormLabel>
                <FormControl>
                  <Input
                    variant="denim"
                    size="sm"
                    placeholder="Phone"
                    className="!pl-0 pt-0"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="birthDate"
            render={({ field }) => {
              return (
                <FormItem>
                  <FormLabel>Date of Birth</FormLabel>
                  <FormControl>
                    <DateInput
                      autoFocus={true}
                      {...field}
                      type="text"
                      placeholder="MM/DD/YYYY"
                      className="rounded-none !border-0 !border-b-2 border-denim !px-2 pb-3 pt-0 text-denim placeholder:text-denim-light focus:outline-none disabled:bg-denim-muted/30"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              );
            }}
          />
          <div className="col-span-2">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email Address</FormLabel>
                  <FormControl>
                    <Input
                      variant="denim"
                      size="sm"
                      placeholder="Email Address"
                      className="!pl-0 pt-0"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
        <div className="grid grid-cols-2 gap-4">
          <Button
            type="button"
            onClick={handleClose}
            variant="denimOutline"
            size="sm"
            className="w-full uppercase"
            disabled={isPending}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="denim"
            disabled={isPending || !form.formState.isDirty}
            size="sm"
            className="w-full uppercase text-white"
            loading={isPending}
          >
            Submit
          </Button>
        </div>
      </form>
    </Form>
  );
};
