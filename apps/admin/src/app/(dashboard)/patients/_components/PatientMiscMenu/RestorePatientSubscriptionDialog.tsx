import { useCallback } from 'react';

import { Button } from '@willow/ui/base/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from '@willow/ui/base/dialog';

import Logo from '~/assets/svg/logo-denim.svg';
import { useRestoreSubscriptionPatient } from '~/hooks/patients';

export const RestorePatientSubscriptionDialog = ({
  open,
  setOpen,
  patientId,
}: {
  open: boolean;
  patientId: string;
  setOpen: (value: boolean) => void;
}) => {
  const { mutateAsync: restoreSubscription } = useRestoreSubscriptionPatient({
    patientId,
  });

  const handleRestoreSubscription = useCallback(async () => {
    await restoreSubscription(undefined, {
      onSuccess: () => {
        setOpen(false);
      },
    });
  }, [restoreSubscription, setOpen]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-md">
        <div className="mx-auto">
          <Logo />
        </div>

        <div className="flex flex-col gap-4 text-center">
          <DialogTitle asChild>
            <p className="text-2xl font-medium leading-[34px]">
              You are about to uncancel this patient
            </p>
          </DialogTitle>
          <DialogDescription asChild>
            <p className="text-base">Confirm patient reactivation</p>
          </DialogDescription>
        </div>
        <div>
          <div className="flex justify-end gap-4 space-x-2">
            <Button
              onClick={() => {
                setOpen(false);
              }}
              variant={'denimOutline'}
              className={'w-full'}
              type="button"
              size={'sm'}
            >
              CANCEL
            </Button>
            <Button
              onClick={handleRestoreSubscription}
              variant={'denim'}
              className={'w-full'}
              type="button"
              size={'sm'}
            >
              CONFIRM
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
