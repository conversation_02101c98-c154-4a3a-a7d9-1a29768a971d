import { useCallback } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, useFormContext } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@willow/ui/base/button';
import { Checkbox } from '@willow/ui/base/checkbox';
import { Dialog, DialogContent } from '@willow/ui/base/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@willow/ui/base/form';
import { Input } from '@willow/ui/base/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@willow/ui/base/select';

import {
  useGetEligibleStatesWithDoctors,
  useTransferPatientState,
} from '~/hooks/patients';

export const PatientTransferStateDialog = ({
  open,
  setOpen,
  patientId,
  patientFullName,
  currentDoctorFullName,
  pharmacyName,
  hasNoDoctor,
}: {
  open: boolean;
  patientId: string;
  patientFullName: string;
  currentDoctorFullName: string;
  pharmacyName: string;
  hasNoDoctor: boolean;
  setOpen: (value: boolean) => void;
}) => {
  const handleClose = useCallback(() => {
    setOpen(false);
  }, [setOpen]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="my-4 flex max-h-[95vh] min-h-fit p-8 sm:max-w-5xl">
        <div className="flex min-h-0 w-full grid-cols-4 gap-8 lg:grid">
          <div className="text-2xl font-medium text-denim">
            <h3>Transfer Patient to a New State</h3>
          </div>
          <div className="col-span-3 max-h-[calc(90vh-8rem)] overflow-y-auto">
            <PatientTransferStateForm
              patientId={patientId}
              handleClose={handleClose}
              patientFullName={patientFullName}
              currentDoctorFullName={currentDoctorFullName}
              pharmacyName={pharmacyName}
              hasNoDoctor={hasNoDoctor}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

type FormSchema = z.infer<typeof zFormSchema>;
const PatientTransferStateForm = ({
  patientId,
  handleClose,
  patientFullName,
  currentDoctorFullName,
  pharmacyName,
  hasNoDoctor,
}: {
  patientId: string;
  handleClose: () => void;
  patientFullName: string;
  currentDoctorFullName: string;
  pharmacyName: string;
  hasNoDoctor: boolean;
}) => {
  const form = useForm<FormSchema>({
    resolver: zodResolver(zFormSchema),
    defaultValues: {
      isBillingSameAsShipping: true,
    },
  });

  const { data: eligibleStates } = useGetEligibleStatesWithDoctors(patientId);

  const { mutate: transferState, isPending } =
    useTransferPatientState(patientId);

  const onSubmitHandler = (data: FormSchema) => {
    // Validate doctor selection
    if (
      !eligibleStates?.find((state) => form.watch('state') === state.id)
        ?.currentDoctorAvailable &&
      !data.doctorId &&
      !hasNoDoctor
    ) {
      return form.setError('doctorId', {
        message: 'Please select a doctor',
      });
    }

    transferState(
      {
        patientId,
        newDoctorId: data.doctorId,
        shippingAddress: {
          ...data.shippingAddress,
          state: data.shippingAddress.state.code,
        },
        billingAddress: data.isBillingSameAsShipping
          ? undefined
          : {
              ...data.billingAddress,
              state: data.billingAddress?.state.code || '',
              address1: data.billingAddress?.address1 || '',
              city: data.billingAddress?.city || '',
              zip: data.billingAddress?.zip || '',
            },
        isBillingIsSameAsShipping: data.isBillingSameAsShipping,
      },
      {
        onSuccess: () => {
          handleClose();
        },
      },
    );
  };

  console.log(
    eligibleStates?.find((state) => form.watch('state') === state.id),
  );

  const isBillingSameAsShipping = form.watch('isBillingSameAsShipping');

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmitHandler)}
        className="space-y-8 px-4"
      >
        <div className="space-y-4">
          <FormField
            control={form.control}
            name="state"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-neutral-700">
                  State transfer to
                </FormLabel>
                <Select
                  onValueChange={(value) => {
                    field.onChange(value);

                    form.setValue(
                      'shippingAddress.state.code',
                      eligibleStates?.find((state) => value === state.id)
                        ?.code ?? '',
                    );
                    form.setValue(
                      'billingAddress.state.code',
                      eligibleStates?.find((state) => value === state.id)
                        ?.code ?? '',
                    );
                    form.setFocus('doctorId');
                  }}
                  value={field.value}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger className="h-8 items-center justify-between rounded-none !border-b-2 border-solid border-denim py-2 pl-0 text-base text-denim">
                      <SelectValue placeholder="Select state" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {eligibleStates?.map((state) => (
                      <SelectItem
                        key={state.id}
                        value={state.id}
                        className="text-sm"
                      >
                        {state.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          <div>
            <h4 className="text-sm font-medium text-neutral-700">Patient</h4>
            <h3 className="text-base font-normal text-denim">
              {patientFullName}
            </h3>
          </div>
        </div>

        {form.watch('state') && (
          <>
            {!eligibleStates?.find((state) => form.watch('state') === state.id)
              ?.currentDoctorAvailable &&
              !hasNoDoctor && (
                <div className="grid grid-cols-3 items-center gap-4">
                  <div>
                    <h4 className="text-sm font-medium text-neutral-700">
                      Doctor
                    </h4>
                    <h3 className="text-base font-normal text-denim">
                      {currentDoctorFullName}
                    </h3>
                  </div>

                  <div className="col-span-2">
                    {eligibleStates?.find(
                      (state) => form.watch('state') === state.id,
                    )?.doctors.length ? (
                      <FormField
                        control={form.control}
                        name="doctorId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-neutral-700">
                              New Doctor
                            </FormLabel>
                            <Select
                              onValueChange={(value) => {
                                field.onChange(value);
                              }}
                              value={field.value}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger className="h-8 items-center justify-between rounded-none !border-b-2 border-solid border-denim py-2 pl-0 text-base text-denim">
                                  <SelectValue placeholder="Select Doctor" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {eligibleStates
                                  ?.find(
                                    (state) => form.watch('state') === state.id,
                                  )
                                  ?.doctors.map(({ doctor, doctorId }) => (
                                    <SelectItem
                                      key={doctorId}
                                      value={doctorId}
                                      className="text-sm"
                                    >
                                      {doctor.user.firstName}{' '}
                                      {doctor.user.lastName}
                                    </SelectItem>
                                  ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <div className="text-base font-medium text-red-500">
                        No doctor available in the selected state.
                      </div>
                    )}
                  </div>
                </div>
              )}

            {!eligibleStates?.find((state) => form.watch('state') === state.id)
              ?.currentPharmacyAvailable && (
              <div className="grid grid-cols-3 items-center gap-4">
                <div>
                  <h4 className="text-sm font-medium text-neutral-700">
                    Pharmacy
                  </h4>
                  <h3 className="text-base font-normal text-denim">
                    {pharmacyName}
                  </h3>
                </div>
                <div className="col-span-2">
                  <h4 className="text-sm font-medium text-neutral-700">
                    New Pharmacy
                  </h4>
                  <h3 className="text-base font-normal text-denim">
                    {eligibleStates?.find(
                      (state) => form.watch('state') === state.id,
                    )?.newPharmacy.name ?? '_'}
                  </h3>
                </div>
              </div>
            )}
          </>
        )}

        <div className="space-y-4">
          <div className="text-sm font-bold text-denim">
            <h4>Shipping Address</h4>
          </div>
          <AddressForm address="shippingAddress" />
        </div>

        <div className="space-y-4">
          <div className="text-sm font-bold text-denim">
            <h4>Billing Address</h4>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="same-as-shipping"
              className="border-denim"
              checked={isBillingSameAsShipping}
              onCheckedChange={(val) => {
                form.setValue(
                  'isBillingSameAsShipping',
                  val === 'indeterminate' ? true : val,
                );
                if (val) {
                  form.setValue(
                    'billingAddress',
                    form.getValues('shippingAddress'),
                    { shouldDirty: true },
                  );
                }
              }}
            />
            <label
              htmlFor="same-as-shipping"
              className="text-md pt-[2px] font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Same as Shipping Address
            </label>
          </div>

          {!isBillingSameAsShipping && <AddressForm address="billingAddress" />}
        </div>

        <div className="grid grid-cols-2 gap-4">
          <Button
            onClick={handleClose}
            variant="denimOutline"
            type="button"
            size="sm"
            className="w-full uppercase"
            disabled={isPending}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="denim"
            disabled={
              isPending ||
              !form.formState.isDirty ||
              // Disable if state not selected
              !form.watch('state') ||
              // Disable if doctor selection required but not selected
              (!eligibleStates?.find(
                (state) => form.watch('state') === state.id,
              )?.currentDoctorAvailable &&
                !form.watch('doctorId') &&
                !hasNoDoctor &&
                // If there are no doctors in this state, disable the button
                eligibleStates?.find(
                  (state) => form.watch('state') === state.id,
                )?.doctors.length === 0)
            }
            size="sm"
            className="w-full uppercase text-white"
            loading={isPending}
          >
            Save
          </Button>
        </div>
      </form>
    </Form>
  );
};

const AddressForm = ({
  address,
}: {
  address: 'shippingAddress' | 'billingAddress';
}) => {
  const form = useFormContext<FormSchema>();

  return (
    <div className="grid grid-cols-1 gap-8">
      <FormField
        control={form.control}
        name={`${address}.address1`}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Street Address</FormLabel>
            <FormControl>
              <Input
                variant="denim"
                size="sm"
                placeholder="Street Address"
                className="!pl-0 pt-0"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name={`${address}.address2`}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Apartment/Suite</FormLabel>
            <FormControl>
              <Input
                variant="denim"
                size="sm"
                placeholder="Apartment/Suite"
                className="!pl-0 pt-0"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <div className="grid grid-cols-1 gap-8 lg:grid-cols-3 lg:gap-4">
        <FormField
          control={form.control}
          name={`${address}.city`}
          render={({ field }) => (
            <FormItem>
              <FormLabel>City</FormLabel>
              <FormControl>
                <Input
                  variant="denim"
                  size="sm"
                  placeholder="City"
                  className="!pl-0 pt-0"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name={`${address}.state.code`}
          render={({ field }) => (
            <FormItem>
              <FormLabel>State</FormLabel>
              <FormControl>
                <Input
                  variant="denim"
                  size="sm"
                  placeholder="State"
                  className="cursor-not-allowed border-stone/60 !bg-white !pl-0 pt-0 text-stone/60"
                  disabled={true}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name={`${address}.zip`}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Zip Code</FormLabel>
              <FormControl>
                <Input
                  variant="denim"
                  placeholder="Zip Code"
                  size="sm"
                  className="!pl-0 pt-0"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
};

const addressSchema = z.object({
  address1: z.string().min(1, { message: 'Address is required' }),
  address2: z.string().optional(),
  city: z.string().min(1, { message: 'City is required' }),
  state: z.object({
    code: z.string().min(1, { message: 'State is required' }),
  }),
  zip: z.string().min(1, { message: 'ZIP code is required' }),
});

const zFormSchema = z
  .object({
    state: z.string(),
    doctorId: z.string().optional(),
    shippingAddress: addressSchema,
    billingAddress: z.object({
      address1: z.string().optional(),
      address2: z.string().optional(),
      city: z.string().optional(),
      state: z.object({
        code: z.string().optional(),
      }),
      zip: z.string().optional(),
    }),
    isBillingSameAsShipping: z.boolean(),
  })
  .superRefine((data, context) => {
    if (!data.isBillingSameAsShipping) {
      if (!data.billingAddress?.address1) {
        context.addIssue({
          message: 'Address is required',
          code: z.ZodIssueCode.custom,
          path: ['billingAddress.address1'],
        });
      }
      if (!data.billingAddress?.city) {
        context.addIssue({
          message: 'City is required',
          code: z.ZodIssueCode.custom,
          path: ['billingAddress.city'],
        });
      }
      if (!data.billingAddress?.state) {
        context.addIssue({
          message: 'State is required',
          code: z.ZodIssueCode.custom,
          path: ['billingAddress.state'],
        });
      }
      if (!data.billingAddress?.zip) {
        context.addIssue({
          message: 'Zipcode code is required',
          code: z.ZodIssueCode.custom,
          path: ['billingAddress.zip'],
        });
      }
    }
    return true;
  });
