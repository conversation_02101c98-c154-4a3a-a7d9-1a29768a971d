import { useCallback } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@willow/ui/base/button';
import { Dialog, DialogContent } from '@willow/ui/base/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@willow/ui/base/form';
import { Input } from '@willow/ui/base/input';

import { useGetPatientInfo, useSetPatientPassword } from '~/hooks/patients';

export const SetPatientPassword = ({
  open,
  setOpen,
  patientId,
}: {
  open: boolean;
  patientId: string;
  setOpen: (value: boolean) => void;
}) => {
  const handleClose = useCallback(() => {
    setOpen(false);
  }, [setOpen]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="p-8 sm:max-w-[690px]">
        <div className="grid grid-cols-4 gap-8">
          <div className="text-2xl font-medium text-denim">
            <h3>Set User Password</h3>
          </div>
          <div className="col-span-3">
            <SetPatientInfoForm
              patientId={patientId}
              handleClose={handleClose}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

const zFormSchema = z
  .object({
    password: z.string().min(1, {
      message: 'password cannot be empty',
    }),
    confirmPassword: z.string().min(1, {
      message: 'confirmPassword cannot be empty',
    }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: 'Entered passwords do not match',
    path: ['confirmPassword'],
  });

type FormSchema = z.infer<typeof zFormSchema>;

const SetPatientInfoForm = ({
  patientId,
  handleClose,
}: {
  patientId: string;
  handleClose: () => void;
}) => {
  const { data: patient } = useGetPatientInfo(patientId);

  const form = useForm<FormSchema>({
    resolver: zodResolver(zFormSchema),
  });

  const { mutate: setPatientPassword, isPending } = useSetPatientPassword(
    patientId,
    {
      onSuccess: () => {
        handleClose();
      },
    },
  );

  const onSubmitHandler = ({ password }: FormSchema) => {
    if (!patient) return;
    setPatientPassword({ email: patient.user.email, password });
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmitHandler)}
        className="space-y-8 px-4"
      >
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Enter Password</FormLabel>
              <FormControl>
                <Input
                  variant="denim"
                  size="sm"
                  className="pt-0"
                  placeholder="Password"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="confirmPassword"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Confirm Password</FormLabel>
              <FormControl>
                <Input
                  variant="denim"
                  size="sm"
                  className="pt-0"
                  placeholder="Confirm"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="grid grid-cols-2 gap-4">
          <Button
            onClick={handleClose}
            variant="denimOutline"
            size="sm"
            className="w-full uppercase"
            disabled={isPending}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="denim"
            disabled={
              isPending ||
              !form.getValues().password ||
              !form.getValues().confirmPassword
            }
            size="sm"
            className="w-full uppercase text-white"
            loading={isPending}
          >
            Set password
          </Button>
        </div>
      </form>
    </Form>
  );
};
