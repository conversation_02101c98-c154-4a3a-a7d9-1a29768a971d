import { useCallback } from 'react';

import { Button } from '@willow/ui/base/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from '@willow/ui/base/dialog';

import Logo from '~/assets/svg/logo-denim.svg';
import { useResetPatientPassword } from '~/hooks/patients';

export const PatientResetPasswordDialog = ({
  open,
  onClose,
  firstName,
  lastName,
  email,
  patientId,
}: {
  open: boolean;
  firstName: string;
  lastName: string;
  email: string;
  patientId: string;
  onClose: (value: boolean) => void;
}) => {
  const { mutateAsync: resetPassword, isPending } = useResetPatientPassword(
    patientId,
    {
      onSuccess: () => {
        onClose(false);
      },
    },
  );

  const handleCancelConfirm = useCallback(async () => {
    await resetPassword({ email });
  }, [resetPassword, patientId]);

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <div className="mx-auto">
          <Logo />
        </div>

        <div className="flex flex-col gap-4 text-center">
          <DialogTitle asChild>
            <p className="text-2xl font-medium text-denim">
              {`Reset Password for ${firstName} ${lastName}`}
            </p>
          </DialogTitle>
          <DialogDescription asChild>
            <p className="text-base !text-denim">
              A reset link will be sent to their registered email address,
              allowing them to create a new password.
            </p>
          </DialogDescription>
        </div>
        <div>
          <div className="flex justify-end gap-4 space-x-2">
            <Button
              onClick={() => {
                onClose(false);
              }}
              variant={'denimOutline'}
              className={'w-full'}
              type="button"
              size={'sm'}
              disabled={isPending}
            >
              CANCEL
            </Button>
            <Button
              onClick={() => {
                void handleCancelConfirm();
              }}
              variant={'denim'}
              className={'w-full text-white'}
              type="button"
              size={'sm'}
              loading={isPending}
              disabled={isPending}
            >
              SEND VIA EMAIL
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
