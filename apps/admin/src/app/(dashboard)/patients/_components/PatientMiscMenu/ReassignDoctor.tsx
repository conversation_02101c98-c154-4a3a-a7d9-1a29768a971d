import { useCallback } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@willow/ui/base/button';
import { Dialog, DialogContent } from '@willow/ui/base/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@willow/ui/base/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@willow/ui/base/select';

import { useGetEligibleDoctors, useReassignDoctor } from '~/hooks/patients';

export const ReassignDoctor = ({
  open,
  setOpen,
  firstName,
  lastName,
  patientId,
  stateCode,
  doctorId,
}: {
  open: boolean;
  firstName: string;
  lastName: string;
  patientId: string;
  stateCode: string;
  doctorId: string;
  setOpen: (value: boolean) => void;
}) => {
  const handleClose = useCallback(() => {
    setOpen(false);
  }, [setOpen]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="p-8 sm:max-w-[690px]">
        <div className="grid grid-cols-5 gap-8">
          <div className="col-span-2 text-2xl font-medium text-denim">
            <h3>New Reassignment</h3>
          </div>
          <div className="col-span-3">
            <ReassignDoctorForm
              patientId={patientId}
              firstName={firstName}
              lastName={lastName}
              stateCode={stateCode}
              handleClose={handleClose}
              doctorId={doctorId}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

const schema = z.object({
  newDoctorId: z.string().min(1, 'Doctor is required'),
});

type FormType = z.infer<typeof schema>;

const PatientInfo = ({
  firstName,
  lastName,
  stateCode,
}: {
  firstName: string;
  lastName: string;
  stateCode: string;
}) => (
  <>
    <div className="flex flex-col gap-3">
      <h6 className="text-sm font-medium text-[#383838]">Patient</h6>
      <h4 className="text-base font-normal text-[#2f4c78]">
        {firstName} {lastName}
      </h4>
    </div>
    <div className="flex flex-col gap-3">
      <h6 className="text-sm font-medium text-[#383838]">State</h6>
      <h4 className="text-base font-normal text-[#2f4c78]">{stateCode}</h4>
    </div>
  </>
);

const ReassignDoctorForm = ({
  patientId,
  firstName,
  lastName,
  stateCode,
  doctorId,
  handleClose,
}: {
  patientId: string;
  firstName: string;
  lastName: string;
  stateCode: string;
  doctorId: string;
  handleClose: () => void;
}) => {
  const { data: eligibleDoctors, isLoading } = useGetEligibleDoctors(patientId);

  const form = useForm<FormType>({
    resolver: zodResolver(schema),
    defaultValues: {
      newDoctorId: '',
    },
  });

  const { mutate: reassignDoctor, isPending } = useReassignDoctor(patientId);

  const onSubmitHandler = ({ newDoctorId }: FormType) => {
    reassignDoctor(
      { newDoctorId, reasonForReassignment: 'ADMIN_TRANSFER' },
      { onSuccess: () => handleClose() },
    );
  };

  const newDoctorId = form.watch('newDoctorId');

  if (isLoading) return <div>Loading...</div>;

  const filteredDoctors =
    eligibleDoctors?.filter((doc) => doc.doctorId !== doctorId) ?? [];

  if (!filteredDoctors.length) {
    return (
      <div className="space-y-8 px-4">
        <div className="grid grid-cols-2 gap-6">
          <PatientInfo
            firstName={firstName}
            lastName={lastName}
            stateCode={stateCode}
          />
        </div>
        <div className="text-base">
          No available doctors for the patient state
        </div>
        <div className="grid grid-cols-2 gap-4">
          <Button
            onClick={handleClose}
            variant="denimOutline"
            size="sm"
            className="w-full uppercase"
          >
            Close
          </Button>
        </div>
      </div>
    );
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmitHandler)}
        className="space-y-8 px-4"
      >
        <div className="grid grid-cols-2 gap-6">
          <PatientInfo
            firstName={firstName}
            lastName={lastName}
            stateCode={stateCode}
          />
          <FormField
            control={form.control}
            name="newDoctorId"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-[#383838]">
                  Reassign To
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger className="h-8 items-center justify-between rounded-none !border-b border-solid border-denim px-0 py-2 text-start text-base font-normal text-[#2f4c78]">
                      <SelectValue placeholder="Select option" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {filteredDoctors.map((doctor) => (
                      <SelectItem
                        key={doctor.doctorId}
                        value={doctor.doctorId}
                        className="text-sm"
                      >
                        {doctor.doctor.user.firstName}{' '}
                        {doctor.doctor.user.lastName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="flex flex-col gap-3">
            <h6 className="text-sm font-medium text-[#383838]">State</h6>
            <h4 className="text-base font-normal text-[#2f4c78]">
              {filteredDoctors
                .find((doc) => doc.doctorId === newDoctorId)
                ?.doctor.prescribesIn.map(({ state }) => state.code)
                .join(', ')}
            </h4>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <Button
            type="button"
            onClick={handleClose}
            variant="denimOutline"
            size="sm"
            className="w-full uppercase"
            disabled={isPending}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="denim"
            disabled={isPending}
            size="sm"
            className="w-full uppercase text-white"
            loading={isPending}
          >
            CONFIRM
          </Button>
        </div>
      </form>
    </Form>
  );
};
