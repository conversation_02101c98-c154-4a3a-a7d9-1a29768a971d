'use client';

import type { ColumnDef } from '@tanstack/react-table';
import { useCallback, useMemo, useRef } from 'react';
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { parseAsString, useQueryState } from 'nuqs';

import type { ListedPatient, PatientGroup } from '@willow/db';
import { cn } from '@willow/ui';
import { Avatar, AvatarFallback, AvatarImage } from '@willow/ui/base/avatar';
import { DrawerTitle } from '@willow/ui/base/drawer';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@willow/ui/base/table';
import {
  Drawer,
  DrawerContent,
  DrawerOverlay,
  DrawerPortal,
  DrawerTrigger,
} from '@willow/ui/base/vaul';
import { ColumnHeader } from '@willow/ui/column-header';
import { Pagination } from '@willow/ui/pagination';
import { SearchInput } from '@willow/ui/search-input';
import { formatBirthDate } from '@willow/utils/format';
import {
  usePaginationSearchParams,
  useSortingQueryState,
} from '@willow/utils/table';

import { useGetDoctor } from '~/hooks/doctors';
import { usePatientAssetLink } from '~/hooks/links';
import { useGetPatients } from '~/hooks/patients';
import { useGetPharmacy } from '~/hooks/pharmacies';
import { useGetStates } from '~/hooks/states';
import { PatientInfo } from './PatientInfo';

export function PatientsTable({ group }: { group: PatientGroup }) {
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [query, setQuery] = useQueryState(
    'query',
    parseAsString.withDefault(''),
  );
  const [doctorId] = useQueryState('doctorId', parseAsString.withDefault(''));
  const [stateCode] = useQueryState('stateCode', parseAsString.withDefault(''));
  const [pharmacyId] = useQueryState(
    'pharmacyId',
    parseAsString.withDefault(''),
  );
  const [pagination, setPagination] = usePaginationSearchParams();
  const [sorting, setSorting] = useSortingQueryState();
  const { genAssetLink } = usePatientAssetLink();

  const [selectedPatientId, setSelectedPatientId] = useQueryState('patientId', {
    defaultValue: '',
  });

  // Fetch doctor, state, and pharmacy information when filters are applied
  const { data: doctorData } = useGetDoctor(doctorId);
  const { data: statesData } = useGetStates();
  const { data: pharmacyData } = useGetPharmacy(pharmacyId);

  // State with filtered data
  const stateInfo = useMemo(() => {
    if (!stateCode || !statesData?.states) return null;
    return statesData.states.find((state) => state.code === stateCode);
  }, [stateCode, statesData]);

  // Compute title based on filters
  const groupTitle = group
    .split('-')
    .map((w) => w[0]?.toUpperCase() + w.slice(1))
    .join(' ');

  const title = useMemo(() => {
    // When no filters are applied, use the standard group title
    if (!doctorId && !stateCode && !pharmacyId) {
      return groupTitle;
    }

    // Start with "All Patients"
    let filterTitle = 'All Patients';

    // Add doctor info if available
    if (doctorId && doctorData) {
      filterTitle += ` from Dr. ${doctorData.user.lastName}`;
    }

    // Add state info if available
    if (stateCode && stateInfo) {
      filterTitle += doctorId
        ? `, ${stateInfo.name}`
        : ` from ${stateInfo.name}`;
    }

    // Add pharmacy info if available
    if (pharmacyId && pharmacyData) {
      filterTitle +=
        doctorId || stateCode
          ? `, ${pharmacyData.name} pharmacy`
          : ` from ${pharmacyData.name} pharmacy`;
    }

    return filterTitle;
  }, [
    groupTitle,
    doctorId,
    stateCode,
    pharmacyId,
    doctorData,
    stateInfo,
    pharmacyData,
  ]);

  const fetchParams = useMemo(
    () => ({
      sortBy: sorting.length > 0 ? sorting[0]?.id : undefined,
      direction:
        sorting.length > 0 ? (sorting[0]?.desc ? 'desc' : 'asc') : 'asc',
      page: pagination.page,
      limit: pagination.pageSize,
      search: query ?? undefined,
      doctorId: doctorId || undefined,
      stateCode: stateCode || undefined,
      pharmacyId: pharmacyId || undefined,
    }),
    [query, pagination, sorting, doctorId, stateCode, pharmacyId],
  );

  const { data, isPending } = useGetPatients(group, fetchParams);

  const columns: ColumnDef<ListedPatient>[] = useMemo(
    () => [
      {
        accessorKey: 'user.firstName',
        id: 'name',
        header: () => <ColumnHeader label="Name" sortKey="name" />,
        cell: ({ row }) => (
          <div className="flex items-center gap-3">
            <div
              style={{ width: '40px', height: '40px', position: 'relative' }}
            >
              <Avatar>
                <AvatarImage src={genAssetLink(row.original.facePhoto)} />
                <AvatarFallback className="font-bold uppercase text-denim-light">
                  {row.original.user.firstName?.[0]}
                  {row.original.user.lastName?.[0]}
                </AvatarFallback>
              </Avatar>
            </div>
            <div className="flex flex-col gap-1">
              <div className="text-sm text-denim">
                {row.original.user.firstName} {row.original.user.lastName}
              </div>
              {row.original.doctor.firstName && (
                <div className="font-sans text-xs font-normal text-dark">
                  Dr. {row.original.doctor.firstName}{' '}
                  {row.original.doctor.lastName}
                </div>
              )}
            </div>
          </div>
        ),
      },
      {
        accessorKey: 'birthDate',
        header: 'Birthday',
        cell: ({ row }) => (
          <div className="text-sm text-dark">
            {formatBirthDate(new Date(row.original.birthDate), 'short')}
          </div>
        ),
      },
      {
        accessorKey: 'contact',
        header: 'Contact',
        cell: ({ row }) => (
          <div className="text-sm text-denim">{row.original.contact}</div>
        ),
      },
    ],
    [genAssetLink],
  );

  const table = useReactTable({
    data: data?.patients || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: (sorting) => {
      void setSorting(sorting);
    },
    state: {
      sorting,
      pagination: {
        pageIndex: pagination.page - 1,
        pageSize: pagination.pageSize,
      },
    },
    manualPagination: true,
    pageCount: data?.pagination?.totalPages || 1,
  });

  const handleSearchSubmit = useCallback(
    (query: string | null) => {
      void setQuery(query);
      void setPagination({ page: 1 });
    },
    [setPagination, setQuery],
  );

  //removed resetFilters function

  return (
    <div className="grid h-[calc(100vh-96px)] w-full grid-rows-[auto_1fr_auto]">
      <div className="w-full">
        <div className="mb-5 text-2xl font-medium capitalize text-denim">
          {title}
        </div>
        <SearchInput
          className="mb-5"
          ref={searchInputRef}
          onSearch={handleSearchSubmit}
          placeholder="Search patients"
          defaultValue={query}
        />
      </div>

      <div className="overflow-y-scroll">
        <Drawer
          direction="right"
          open={selectedPatientId !== ''}
          onOpenChange={(value) => {
            if (!value) void setSelectedPatientId('');
          }}
        >
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id} className="font-bold text-dark">
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows.length ? (
                table.getRowModel().rows.map((row) => (
                  <DrawerTrigger
                    key={row.id}
                    asChild
                    onClick={() => {
                      void setSelectedPatientId(row.original.id);
                    }}
                  >
                    <TableRow
                      data-state={
                        row.original.id === selectedPatientId
                          ? 'selected'
                          : undefined
                      }
                      className="h-[60px] cursor-pointer hover:bg-muted/50"
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id} className="p-2">
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext(),
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  </DrawerTrigger>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center"
                  >
                    {isPending ? 'Loading...' : 'No results.'}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
          {selectedPatientId && (
            <DrawerPortal>
              <DrawerOverlay className="fixed inset-0 bg-dark/40" />
              <DrawerContent
                className="fixed bottom-0 right-0 top-0 z-10 flex w-[1000px] !touch-none !select-text"
                draggable={false}
                data-vaul-no-drag={true}
              >
                <DrawerTitle className="hidden">
                  Patient Information
                </DrawerTitle>
                <PatientInfo
                  patientId={selectedPatientId}
                  handleClose={() => setSelectedPatientId('')}
                />
              </DrawerContent>
            </DrawerPortal>
          )}
        </Drawer>
      </div>

      <div className={cn('flex items-center justify-end py-4')}>
        {data?.pagination && table.getRowModel().rows.length > 0 && (
          <Pagination
            currentPage={pagination.page}
            totalPages={data.pagination.totalPages || 1}
            onPageChange={(page) => setPagination({ page })}
          />
        )}
      </div>
    </div>
  );
}
