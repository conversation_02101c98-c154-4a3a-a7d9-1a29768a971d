'use client';

import type { ColumnDef } from '@tanstack/react-table';
import { useCallback, useMemo, useRef } from 'react';
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { parseAsInteger, parseAsString, useQueryState } from 'nuqs';

import { cn } from '@willow/ui';
import { Skeleton } from '@willow/ui/base/skeleton';
import { Switch } from '@willow/ui/base/switch';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@willow/ui/base/table';
import { ColumnHeader } from '@willow/ui/column-header';
import { Pagination } from '@willow/ui/pagination';
import { SearchInput } from '@willow/ui/search-input';
import { useSortingQueryState } from '@willow/utils/table';

import type { State } from '~/hooks/states';
import { useGetStates, useToggleStateEnabled } from '~/hooks/states';

export function StatesTable() {
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [query, setQuery] = useQueryState(
    'query',
    parseAsString.withDefault(''),
  );
  const [page, setPage] = useQueryState('page', parseAsInteger.withDefault(1));
  const [sorting, setSorting] = useSortingQueryState();

  const fetchParams = useMemo(
    () => ({
      sortBy: sorting.length > 0 ? sorting[0]?.id : undefined,
      direction:
        sorting.length > 0
          ? sorting[0]?.desc
            ? ('desc' as const)
            : ('asc' as const)
          : ('asc' as const),
      page,
      limit: 10,
      search: query ?? undefined,
      includePatientCounts: true,
    }),
    [query, page, sorting],
  );

  const { data, isPending, isError } = useGetStates(fetchParams);
  const { mutate: toggleStateEnabled, isPending: isToggling } =
    useToggleStateEnabled();

  const columns: ColumnDef<State>[] = useMemo(
    () => [
      {
        accessorKey: 'name',
        header: () => <ColumnHeader label="State" sortKey="name" />,
        cell: ({ row }) => (
          <div className="text-sm text-denim">{row.original.name}</div>
        ),
      },
      {
        accessorKey: 'code',
        header: () => <ColumnHeader label="Code" sortKey="code" />,
        cell: ({ row }) => (
          <div className="text-sm text-denim">{row.original.code}</div>
        ),
      },
      {
        accessorKey: 'patientCount',
        header: () => <ColumnHeader label="Patients" sortKey="patientCount" />,
        cell: ({ row }) => (
          <div className="text-sm text-denim">
            {row.original.patientCount ?? '0'}
          </div>
        ),
      },
      {
        accessorKey: 'enabled',
        header: () => <ColumnHeader label="Enabled" sortKey="enabled" />,
        cell: ({ row }) => {
          const enabled = row.original.enabled;
          return (
            <Switch
              variant="denim"
              checked={enabled}
              disabled={isToggling}
              onCheckedChange={(checked) => {
                toggleStateEnabled({
                  id: row.original.id,
                  enabled: checked,
                });
              }}
            />
          );
        },
      },
    ],
    [isToggling, toggleStateEnabled],
  );

  const table = useReactTable({
    data: data?.states || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    manualSorting: true,
    onSortingChange: (sorting) => {
      void setSorting(sorting);
    },
    state: {
      sorting,
      pagination: {
        pageIndex: page - 1,
        pageSize: 25,
      },
    },
    pageCount: data?.pagination?.totalPages || 1,
  });

  const handleSearchSubmit = useCallback(
    (query: string | null) => {
      void setQuery(query);
      void setPage(1);
    },
    [setPage, setQuery],
  );

  return (
    <div className="grid h-[calc(100vh-96px)] w-full grid-rows-[auto_1fr_auto]">
      <div className="w-full">
        <div className="mb-5 text-2xl font-medium text-denim">States</div>
        <SearchInput
          className="mb-5"
          ref={searchInputRef}
          onSearch={handleSearchSubmit}
          placeholder="Search states"
          defaultValue={query}
        />
      </div>

      <div className="relative flex-1 overflow-auto">
        <div className="h-full w-full">
          {isPending ? (
            <div className="space-y-2">
              {Array.from({ length: 10 }).map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </div>
          ) : (
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => (
                      <TableHead
                        key={header.id}
                        className="font-bold text-dark"
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext(),
                            )}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows.length > 0 ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && 'selected'}
                      className="h-[60px]"
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id} className="p-2">
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext(),
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                    >
                      {isError ? 'Error loading states.' : 'No results.'}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          )}
        </div>
      </div>

      <div className={cn('flex items-center justify-end py-4')}>
        {table.getRowModel().rows.length > 0 && (
          <Pagination
            currentPage={page}
            totalPages={data?.pagination?.totalPages || 1}
            onPageChange={(newPage) => setPage(newPage)}
          />
        )}
      </div>
    </div>
  );
}
