import { useMemo, useState } from 'react';
import Link from 'next/link';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@willow/ui/base/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@willow/ui/base/table';

import type { TopicInsightV2 } from '~/hooks/insights';

interface ConsumerGroupsTableProps {
  topicsInsight: TopicInsightV2[];
}

type FilterType = 'all' | 'failed' | 'unconfirmed' | 'problems';

export function ConsumerGroupsTable({
  topicsInsight,
}: ConsumerGroupsTableProps) {
  const [filterType, setFilterType] = useState<FilterType>('problems');
  const [searchTerm, setSearchTerm] = useState('');

  const filteredSubscriptions = useMemo(
    () =>
      topicsInsight.flatMap((topic) => {
        return topic.subscriptions
          .filter((sub) => {
            // Apply status filters first
            const statusFilter =
              (filterType === 'problems' &&
                (!sub.isConfirmed || sub.failedMessages.length > 0)) ||
              (filterType === 'failed' && sub.failedMessages.length > 0) ||
              (filterType === 'unconfirmed' && !sub.isConfirmed) ||
              filterType === 'all';

            // Then apply search term filter
            const searchFilter =
              searchTerm === '' ||
              sub.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
              topic.name.toLowerCase().includes(searchTerm.toLowerCase());

            return statusFilter && searchFilter;
          })
          .map((sub) => ({
            topic,
            subscription: sub,
          }));
      }),
    [topicsInsight, filterType, searchTerm],
  );

  return (
    <div className="mt-8 rounded-md bg-white shadow">
      <div className="p-6">
        <div className="mb-4 flex flex-col items-start justify-between space-y-4 md:flex-row md:items-center md:space-y-0">
          <h2 className="text-lg font-semibold">Consumer Groups</h2>
          <div className="flex w-full flex-col items-start space-y-4 md:w-auto md:flex-row md:items-center md:space-x-4 md:space-y-0">
            <div className="relative w-full md:w-64">
              <input
                type="text"
                placeholder="Search consumer groups..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full rounded-md border px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm('')}
                  className="absolute right-3 top-1/2 -translate-y-1/2 transform text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              )}
            </div>
            <Select
              // className="w-full rounded-md border px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 md:w-auto"
              value={filterType}
              onValueChange={(value) => setFilterType(value as FilterType)}
              // onChange={(e) => setFilterType(e.target.value as FilterType)}
            >
              <SelectTrigger
                size="xs"
                variant={'outline'}
                className="text-md w-[300px] text-muted-foreground"
              >
                <SelectValue placeholder="Select a State" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all" size="small">
                  All Consumer Groups
                </SelectItem>
                <SelectItem value="problems" size="small">
                  With Problems
                </SelectItem>
                <SelectItem value="failed" size="small">
                  With Failed Messages
                </SelectItem>
                <SelectItem value="unconfirmed" size="small">
                  Unconfirmed Groups
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Topic</TableHead>
                <TableHead>Consumer Group</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Failed Messages</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredSubscriptions.map(({ topic, subscription }) => (
                <TableRow key={subscription.endpoint}>
                  <TableCell>{topic.name}</TableCell>
                  <TableCell className="">
                    <b>{subscription.name}</b>
                  </TableCell>
                  <TableCell>
                    {subscription.isConfirmed ? (
                      <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                        Confirmed
                      </span>
                    ) : (
                      <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                        Unconfirmed
                      </span>
                    )}
                  </TableCell>
                  <TableCell>
                    {subscription.failedMessages.length > 0 ? (
                      <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                        {subscription.failedMessages.length} failed
                      </span>
                    ) : (
                      <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                        No failures
                      </span>
                    )}
                  </TableCell>
                  <TableCell>
                    <Link
                      href={`/insights/queues/messages?topics=${topic.name}&status=failed`}
                      className="text-xs text-blue-600 hover:text-blue-800"
                    >
                      View Details
                    </Link>

                    {subscription.dlrQueueUrl !== null &&
                      subscription.failedMessages.length > 0 && (
                        <button
                          onClick={() => {
                            if (subscription.dlrQueueUrl) {
                              window.open(
                                `https://console.aws.amazon.com/sqs/v2/home#/queues/details/${encodeURIComponent(subscription.dlrQueueUrl)}`,
                                '_blank',
                              );
                            }
                          }}
                          className="text-xs text-blue-600 hover:text-blue-800"
                        >
                          View DLQ
                        </button>
                      )}
                  </TableCell>
                </TableRow>
              ))}
              {filteredSubscriptions.length === 0 && (
                <TableRow>
                  <TableCell
                    colSpan={5}
                    className="py-4 text-center text-gray-500"
                  >
                    No consumer groups found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}
