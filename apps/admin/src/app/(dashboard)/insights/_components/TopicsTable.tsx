import { useMemo, useState } from 'react';

import type { TopicInsightV2 } from '~/hooks/insights';

interface TopicsTableProps {
  topicsInsight: TopicInsightV2[];
}

export function TopicsTable({ topicsInsight }: TopicsTableProps) {
  const [searchTerm, setSearchTerm] = useState('');

  const filteredTopics = useMemo(() => {
    return topicsInsight.filter(
      (topic) =>
        searchTerm === '' ||
        topic.name.toLowerCase().includes(searchTerm.toLowerCase()),
    );
  }, [topicsInsight, searchTerm]);

  return (
    <div className="rounded-md bg-white shadow">
      <div className="p-6">
        <div className="mb-4 flex items-center justify-between">
          <h2 className="text-lg font-semibold">Topics Detail</h2>
          <div className="relative">
            <input
              type="text"
              placeholder="Search topics..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="rounded-md border px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            {searchTerm && (
              <button
                onClick={() => setSearchTerm('')}
                className="absolute right-3 top-1/2 -translate-y-1/2 transform text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            )}
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="px-4 py-2 text-left font-medium">Topic Name</th>
                <th className="px-4 py-2 text-left font-medium">Subscribers</th>
                <th className="px-4 py-2 text-left font-medium">Messages</th>
                <th className="px-4 py-2 text-left font-medium">
                  Backup Queue
                </th>
                <th className="px-4 py-2 text-left font-medium">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredTopics.map((topic) => (
                <tr key={topic.topicArn} className="border-b hover:bg-gray-50">
                  <td className="px-4 py-2">{topic.name}</td>
                  <td className="px-4 py-2">{topic.subscriptions.length}</td>
                  <td className="px-4 py-2">{topic.messagesCount}</td>
                  <td className="px-4 py-2">
                    {topic.backupQueue ? (
                      <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                        Yes
                      </span>
                    ) : (
                      <span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800">
                        No
                      </span>
                    )}
                  </td>
                  <td className="px-4 py-2">
                    <button
                      onClick={() =>
                        window.open(
                          `https://console.aws.amazon.com/sns/v3/home#/topic/${encodeURIComponent(topic.topicArn)}`,
                          '_blank',
                        )
                      }
                      className="text-xs text-blue-600 hover:text-blue-800"
                    >
                      View in AWS
                    </button>
                  </td>
                </tr>
              ))}
              {filteredTopics.length === 0 && (
                <tr>
                  <td colSpan={5} className="py-4 text-center text-gray-500">
                    No topics found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
