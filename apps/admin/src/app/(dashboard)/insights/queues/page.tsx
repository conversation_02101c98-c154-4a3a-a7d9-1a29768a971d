'use client';

import { useMemo, useState } from 'react';
import {
  AlertCircle,
  HeartPulse,
  MessageSquare,
  RefreshCcw,
} from 'lucide-react';

import { cn } from '@willow/ui';
import { Button } from '@willow/ui/base/button';
import { Card, CardContent, CardHeader, CardTitle } from '@willow/ui/base/card';

import { useTopcisInsights } from '~/hooks/insights';
import { ConsumerGroupsTable } from '../_components/ConsumerGroupsTable';
import { TopicsTable } from '../_components/TopicsTable';

export default function QueueInsightsPage() {
  const [forceRefresh, setForceRefresh] = useState(false);

  const {
    data: insights,
    isPending,
    isFetching,
    isError,
    error,
    refetch,
  } = useTopcisInsights({ forceRefresh });

  const topicsCount = useMemo(() => {
    if (!insights) return 0;
    return insights.topics.length;
  }, [insights]);

  const subscribersCount = useMemo(() => {
    if (!insights) return 0;
    return insights.topics.reduce(
      (total, queue) => total + queue.subscriptions.length,
      0,
    );
  }, [insights]);

  const totalMessageVolume = useMemo(() => {
    if (!insights) return 0;
    return insights.topics.reduce(
      (total, queue) => total + queue.messagesCount,
      0,
    );
  }, [insights]);

  const subscriptionPendingConfirmation = useMemo(() => {
    if (!insights) return [];
    return insights.topics.flatMap((queue) =>
      queue.subscriptions.filter((sub) => !sub.isConfirmed),
    );
  }, [insights]);

  const failedMessages = useMemo(() => {
    if (!insights) return [];
    return insights.topics.flatMap((queue) =>
      queue.subscriptions.flatMap((sub) => sub.failedMessages),
    );
  }, [insights]);

  if (isPending) {
    return (
      <div className="container mx-auto p-6">
        <h1 className="mb-8 text-3xl font-bold">Queue Insights</h1>
        <div className="flex h-64 items-center justify-center">
          <p className="text-lg">Loading queue insights...</p>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="container mx-auto p-6">
        <h1 className="mb-8 text-3xl font-bold">Queue Insights</h1>
        <div className="flex items-center rounded-md border border-red-200 bg-red-50 p-4">
          <AlertCircle className="mr-2 text-red-500" />
          <p>{error.message}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen overflow-y-scroll p-6 pt-24">
      <div className="mb-8 flex items-center justify-between">
        <h1 className="text-3xl font-bold">Queue Insights</h1>
        <Button
          disabled={isFetching}
          size="xs"
          onClick={() => {
            setForceRefresh(true);
            void refetch();
          }}
          variant="denimOutline"
        >
          <RefreshCcw
            className={cn({
              'animate-spin': isFetching,
            })}
          />
          Refresh Data
        </Button>
      </div>

      {/* Last updated timestamp */}
      <p className="mb-6 text-sm text-gray-500">
        Last updated: {new Date(insights.date).toLocaleString()}
      </p>

      {/* Dashboard Cards */}
      <div className="mb-8 grid grid-cols-1 gap-6 md:grid-cols-5">
        {/* Topics Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Topics</CardTitle>
            <HeartPulse className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{topicsCount}</div>
            <p className="text-xs text-muted-foreground">
              SNS topics in the system
            </p>
          </CardContent>
        </Card>

        {/* Consumer Groups Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Consumer Groups
            </CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{subscribersCount}</div>
            <p className="text-xs text-muted-foreground">
              Total subscribers across all topics
            </p>
          </CardContent>
        </Card>

        {/* Message Volume Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Message Volume
            </CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalMessageVolume}</div>
            <p className="text-xs text-muted-foreground">
              Total messages in all topics
            </p>
          </CardContent>
        </Card>

        {/* Unconfirmed Subscriptions Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Unconfirmed Subscriptions
            </CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {subscriptionPendingConfirmation.length}
            </div>
            <p className="text-xs text-muted-foreground">
              Subscriptions pending confirmation
            </p>
          </CardContent>
        </Card>

        {/* Failed Messages Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Topics With Failed Messages
            </CardTitle>
            <AlertCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{failedMessages.length}</div>
            <p className="text-xs text-muted-foreground">
              Topics with message delivery failures
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="flex flex-col space-y-8">
        {/* Consumer Groups Table Component */}
        <ConsumerGroupsTable topicsInsight={insights.topics} />

        {/* Topics Table Component */}
        <TopicsTable topicsInsight={insights.topics} />
      </div>
    </div>
  );
}
