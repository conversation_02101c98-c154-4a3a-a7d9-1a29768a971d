'use client';

import { use<PERSON><PERSON>back, useMemo, useState } from 'react';
import { format } from 'date-fns';
import {
  AlertCircle,
  CalendarIcon,
  CheckCircle2,
  Eye,
  Filter,
  RefreshCcw,
  Search,
  XCircle,
} from 'lucide-react';
import {
  parseAsArrayOf,
  parseAsInteger,
  parseAsString,
  useQueryState,
} from 'nuqs';

import { cn } from '@willow/ui';
import { Badge } from '@willow/ui/base/badge';
import { Button } from '@willow/ui/base/button';
import { Calendar } from '@willow/ui/base/calendar';
import { Card, CardContent } from '@willow/ui/base/card';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@willow/ui/base/dropdown-menu';
import { Input } from '@willow/ui/base/input';
import { Label } from '@willow/ui/base/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@willow/ui/base/popover';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@willow/ui/base/table';
import {
  Drawer,
  DrawerContent,
  DrawerOverlay,
  DrawerPortal,
} from '@willow/ui/base/vaul';

import { useTopcisInsights } from '~/hooks/insights';
import { MessageDetails } from './_components/MessageDetails';

interface Filters {
  patientId: string;
  topics: string[];
  events: string[];
  dateRange: {
    from: Date | undefined;
    to: Date | undefined;
  };
  bodySearch: string;
  status: ('success' | 'failed')[];
}

export default function QueueInsightsMessagesPage() {
  const [forceRefresh, setForceRefresh] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState<any>(null);

  // Pagination with URL state
  const [page, setPage] = useQueryState('page', parseAsInteger.withDefault(1));
  const itemsPerPage = 10;

  // Filters using URL state
  const [patientId, setPatientId] = useQueryState('patientId', parseAsString);
  const [topics, setTopics] = useQueryState(
    'topics',
    parseAsArrayOf(parseAsString).withDefault([]),
  );
  const [events, setEvents] = useQueryState(
    'events',
    parseAsArrayOf(parseAsString).withDefault([]),
  );
  // Store date range as a serialized JSON string
  const [dateRangeStr, setDateRangeStr] = useQueryState(
    'dateRange',
    parseAsString,
  );
  const [bodySearch, setBodySearch] = useQueryState(
    'bodySearch',
    parseAsString,
  );
  const [status, setStatus] = useQueryState(
    'status',
    parseAsArrayOf(parseAsString).withDefault([]),
  );

  // Parse the date range from the URL string
  const parsedDateRange = useMemo(() => {
    if (!dateRangeStr) return { from: undefined, to: undefined };
    try {
      const parsed = JSON.parse(dateRangeStr);
      return {
        from: parsed.from ? new Date(parsed.from as string) : undefined,
        to: parsed.to ? new Date(parsed.to as string) : undefined,
      };
    } catch {
      // Silent error handling
      return { from: undefined, to: undefined };
    }
  }, [dateRangeStr]);

  // Combine URL state into filters object for easier usage
  const filters = useMemo<Filters>(
    () => ({
      patientId: patientId || '',
      topics: topics || [],
      events: events || [],
      dateRange: parsedDateRange,
      bodySearch: bodySearch || '',
      status: (status || []) as ('success' | 'failed')[],
    }),
    [patientId, topics, events, parsedDateRange, bodySearch, status],
  );

  const {
    data: insights,
    isFetching,
    isPending,
    isError,
    error,
    refetch,
  } = useTopcisInsights({ forceRefresh });

  // Create a mapping of failed message IDs to subscription names
  const failedMessageMap = useMemo(() => {
    const map: Record<string, string[]> = {};
    insights?.topics.forEach((topic) => {
      topic.subscriptions.forEach((sub) => {
        sub.failedMessages.forEach((msg) => {
          if (!map[msg.messageId]) {
            map[msg.messageId] = [];
          }
          map[msg.messageId]?.push(sub.name);
        });
      });
    });

    console.log('Failed message map:', map);
    return map;
  }, [insights]);

  // Process messages into a flat list with additional status info
  const allMessages = useMemo(() => {
    return (
      insights?.topics.flatMap((topic) => {
        return Object.values(topic.messages)
          .map((msg) => {
            const consumerGroups = topic.subscriptions.map((sub) => sub.name);
            const failedConsumerGroups = failedMessageMap[msg.messageId] || [];
            return {
              ...msg,
              status: failedConsumerGroups.length > 0 ? 'failed' : 'success',
              failedConsumerGroups,
              consumerGroups: consumerGroups,
              d: new Date(msg.timestamp),
            };
          })
          .sort((a, b) => {
            return b.d.getTime() - a.d.getTime();
          });
      }) ?? []
    );
  }, [insights, failedMessageMap]);

  // Get unique topics and events for filters
  const availableTopics = useMemo(() => {
    return [...new Set(insights?.topics.map((t) => t.name) ?? [])];
  }, [insights]);

  const availableEventByTopic = useMemo(() => {
    const map: Record<string, string[]> = {};
    allMessages.forEach((msg) => {
      if (!map[msg.topicName]) {
        map[msg.topicName] = [];
      }
      if (!map[msg.topicName]!.includes(msg.event)) {
        map[msg.topicName]!.push(msg.event);
      }
    });
    return map;
  }, [allMessages]);

  // Apply filters to messages
  const filteredMessages = useMemo(() => {
    return allMessages.filter((msg) => {
      // Patient ID filter
      if (filters.patientId && !msg.patientId.includes(filters.patientId)) {
        return false;
      }

      // Topics filter
      if (
        filters.topics.length > 0 &&
        !filters.topics.includes(msg.topicName)
      ) {
        return false;
      }

      // Events filter
      if (filters.events.length > 0 && !filters.events.includes(msg.event)) {
        return false;
      }

      // Date range filter
      const msgDate = new Date(msg.timestamp);
      if (filters.dateRange.from && msgDate < filters.dateRange.from) {
        return false;
      }
      if (filters.dateRange.to) {
        const endDate = new Date(filters.dateRange.to);
        endDate.setHours(23, 59, 59, 999); // End of the selected day
        if (msgDate > endDate) {
          return false;
        }
      }

      // Body search
      if (
        filters.bodySearch &&
        !msg.body.toLowerCase().includes(filters.bodySearch.toLowerCase())
      ) {
        return false;
      }

      // Status filter
      if (
        filters.status.length > 0 &&
        !filters.status.includes(msg.status as 'success' | 'failed')
      ) {
        return false;
      }

      return true;
    });
  }, [allMessages, filters]);

  // Paginate results
  const totalPages = Math.ceil(filteredMessages.length / itemsPerPage);
  const paginatedMessages = filteredMessages.slice(
    (page - 1) * itemsPerPage,
    page * itemsPerPage,
  );

  // Handle filter changes
  const handlePatientIdChange = useCallback(
    async (value: string) => {
      await Promise.all([setPatientId(value || null), setPage(1)]);
    },
    [setPatientId, setPage],
  );

  const handleTopicChange = useCallback(
    async (topicsArr: string[]) => {
      await Promise.all([
        setTopics(topicsArr.length > 0 ? topicsArr : null),
        setPage(1),
      ]);
    },
    [setTopics, setPage],
  );

  const handleEventChange = useCallback(
    async (eventsArr: string[]) => {
      await Promise.all([
        setEvents(eventsArr.length > 0 ? eventsArr : null),
        setPage(1),
      ]);
    },
    [setEvents, setPage],
  );

  const handleDateRangeChange = useCallback(
    async (range: { from: Date | undefined; to: Date | undefined }) => {
      // Convert the range to a JSON string for URL storage
      const rangeStr =
        range.from || range.to
          ? JSON.stringify({
              from: range.from ? range.from.toISOString() : null,
              to: range.to ? range.to.toISOString() : null,
            })
          : null;

      await Promise.all([setDateRangeStr(rangeStr), setPage(1)]);
    },
    [setDateRangeStr, setPage],
  );

  const handleBodySearchChange = useCallback(
    async (value: string) => {
      await Promise.all([setBodySearch(value || null), setPage(1)]);
    },
    [setBodySearch, setPage],
  );

  const handleStatusChange = useCallback(
    async (statusArr: ('success' | 'failed')[]) => {
      await Promise.all([
        setStatus(statusArr.length > 0 ? (statusArr as string[]) : null),
        setPage(1),
      ]);
    },
    [setStatus, setPage],
  );

  // Handle pagination with promises
  const goToPreviousPage = async () => {
    await setPage(Math.max((page || 1) - 1, 1));
  };

  const goToNextPage = async () => {
    await setPage(Math.min((page || 1) + 1, totalPages));
  };

  if (isPending) {
    return (
      <div className="container mx-auto p-6">
        <h1 className="mb-8 text-3xl font-bold">Queue Messages</h1>
        <div className="flex h-64 items-center justify-center">
          <p className="text-lg">Loading messages...</p>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="container mx-auto p-6">
        <h1 className="mb-8 text-3xl font-bold">Queue Messages</h1>
        <div className="flex items-center rounded-md border border-red-200 bg-red-50 p-4">
          <AlertCircle className="mr-2 text-red-500" />
          <p>{error.message}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen overflow-y-scroll p-6 pt-24">
      <div className="mb-8 flex items-center justify-between">
        <h1 className="text-3xl font-bold">Queue Messages</h1>
        <Button
          disabled={isFetching}
          size="xs"
          onClick={() => {
            setForceRefresh(true);
            setTimeout(() => {
              void refetch();
            }, 200);
          }}
          variant="denimOutline"
        >
          <RefreshCcw
            className={cn({
              'animate-spin': isFetching,
            })}
          />
          Refresh Data
        </Button>
      </div>

      {/* Last updated timestamp */}
      <p className="mb-6 text-sm text-gray-500">
        Last updated: {new Date(insights.date).toLocaleString()}
      </p>

      {/* Filter Section */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
            {/* Topics filter */}
            <div className="flex flex-col space-y-2">
              <Label>Topics</Label>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    size="sm"
                    variant="denimOutline"
                    className="justify-between rounded-none"
                  >
                    {filters.topics.length === 0
                      ? 'All Topics'
                      : `${filters.topics.length} selected`}
                    <Filter className="ml-2 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56">
                  {availableTopics.map((topic) => (
                    <DropdownMenuCheckboxItem
                      key={topic}
                      checked={filters.topics.includes(topic)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          void handleTopicChange([...filters.topics, topic]);
                        } else {
                          void handleTopicChange(
                            filters.topics.filter((t) => t !== topic),
                          );
                        }
                      }}
                    >
                      {topic}
                    </DropdownMenuCheckboxItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* Events filter */}
            <div className="flex flex-col space-y-2">
              <Label>Events</Label>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    size="sm"
                    variant="denimOutline"
                    className="justify-between rounded-none"
                  >
                    {filters.events.length === 0
                      ? 'All Events'
                      : `${filters.events.length} selected`}
                    <Filter className="ml-2 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-auto min-w-[300px]">
                  {Object.entries(availableEventByTopic).map(
                    ([topic, events], i) => (
                      <>
                        {i > 0 && <DropdownMenuSeparator />}
                        <DropdownMenuLabel>{topic}</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        {events.map((event) => (
                          <DropdownMenuCheckboxItem
                            key={event}
                            checked={filters.events.includes(event)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                void handleEventChange([
                                  ...filters.events,
                                  event,
                                ]);
                              } else {
                                void handleEventChange(
                                  filters.events.filter((e) => e !== event),
                                );
                              }
                            }}
                          >
                            {event}
                          </DropdownMenuCheckboxItem>
                        ))}
                      </>
                    ),
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* Date range filter */}
            <div className="flex flex-col space-y-2">
              <Label>Date Range</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    id="date"
                    size="sm"
                    variant="denimOutline"
                    className="justify-start rounded-none text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {filters.dateRange.from ? (
                      filters.dateRange.to ? (
                        <>
                          {format(filters.dateRange.from, 'PP')} -{' '}
                          {format(filters.dateRange.to, 'PP')}
                        </>
                      ) : (
                        format(filters.dateRange.from, 'PP')
                      )
                    ) : (
                      <span>Pick a date range</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    initialFocus
                    mode="range"
                    defaultMonth={filters.dateRange.from}
                    selected={{
                      from: filters.dateRange.from,
                      to: filters.dateRange.to,
                    }}
                    onSelect={(range) => {
                      void handleDateRangeChange({
                        from: range?.from,
                        to: range?.to,
                      });
                    }}
                    numberOfMonths={2}
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* Status filter */}
            <div className="flex flex-col space-y-2">
              <Label>Status</Label>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    size="sm"
                    variant="denimOutline"
                    className="justify-between rounded-none"
                  >
                    {filters.status.length === 0
                      ? 'All Statuses'
                      : `${filters.status.length} selected`}
                    <Filter className="ml-2 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56">
                  <DropdownMenuCheckboxItem
                    checked={filters.status.includes('success')}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        void handleStatusChange([...filters.status, 'success']);
                      } else {
                        void handleStatusChange(
                          filters.status.filter((s) => s !== 'success'),
                        );
                      }
                    }}
                  >
                    Success
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={filters.status.includes('failed')}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        void handleStatusChange([...filters.status, 'failed']);
                      } else {
                        void handleStatusChange(
                          filters.status.filter((s) => s !== 'failed'),
                        );
                      }
                    }}
                  >
                    Failed
                  </DropdownMenuCheckboxItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            {/* Patient ID filter */}
            <div className="flex flex-col space-y-2">
              <Label htmlFor="patientId">Patient ID</Label>
              <Input
                size="sm"
                className="rounded-none py-2"
                id="patientId"
                placeholder="Filter by patient ID"
                value={filters.patientId}
                onChange={(e) => void handlePatientIdChange(e.target.value)}
              />
            </div>

            {/* Body Search */}
            <div className="flex flex-col space-y-2">
              <Label htmlFor="bodySearch">Search in Message Body</Label>
              <div className="relative">
                <Search className="absolute left-2 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="bodySearch"
                  size={'sm'}
                  placeholder="Search message content"
                  className="rounded-none py-2 pl-8"
                  value={filters.bodySearch}
                  onChange={(e) => void handleBodySearchChange(e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Reset filters button */}
          <div className="flex justify-end">
            <Button
              size="sm"
              variant="denimOutline"
              onClick={async () => {
                // Reset all filters
                await Promise.all([
                  setPatientId(null),
                  setTopics(null),
                  setEvents(null),
                  setDateRangeStr(null),
                  setBodySearch(null),
                  setStatus(null),
                  setPage(1),
                ]);
              }}
              className="rounded-none"
            >
              Reset Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Messages Table */}
      <Card>
        <CardContent className="p-6">
          <h2 className="mb-4 text-xl font-semibold">
            Messages ({filteredMessages.length})
          </h2>

          {filteredMessages.length === 0 ? (
            <div className="flex h-32 items-center justify-center">
              <p className="text-muted-foreground">
                No messages found matching your filters
              </p>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>ID</TableHead>
                      <TableHead>Patient ID</TableHead>
                      <TableHead>Topic</TableHead>
                      <TableHead>Event</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {paginatedMessages.map((message) => (
                      <TableRow key={message.messageId}>
                        <TableCell>{message.messageId}</TableCell>
                        <TableCell>{message.patientId}</TableCell>
                        <TableCell>{message.topicName}</TableCell>
                        <TableCell>{message.event}</TableCell>
                        <TableCell>
                          {new Date(message.timestamp).toLocaleString()}
                        </TableCell>
                        <TableCell className="w-34 pt-4">
                          {message.status === 'success' ? (
                            <Badge className="bg-green-500 text-white">
                              <CheckCircle2 className="mr-1 h-3 w-3" />
                              Success ({message.consumerGroups.length})
                            </Badge>
                          ) : (
                            <div>
                              <Badge className="bg-red-500 text-white">
                                <XCircle className="mr-1 h-3 w-3" />
                                Failed ({message.failedConsumerGroups.length}/
                                {message.consumerGroups.length})
                              </Badge>
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          <Button
                            size="sm"
                            variant="denimOutline"
                            className="p-2"
                            onClick={() => setSelectedMessage(message)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              <div className="mt-4 flex items-center justify-between">
                <div className="text-sm text-muted-foreground">
                  Showing{' '}
                  {paginatedMessages.length > 0
                    ? (page - 1) * itemsPerPage + 1
                    : 0}
                  -{Math.min(page * itemsPerPage, filteredMessages.length)} of{' '}
                  {filteredMessages.length} messages
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="denimOutline"
                    size="sm"
                    onClick={() => void goToPreviousPage()}
                    disabled={page === 1}
                  >
                    Previous
                  </Button>
                  <Button
                    variant="denimOutline"
                    size="sm"
                    onClick={() => void goToNextPage()}
                    disabled={page >= totalPages}
                  >
                    Next
                  </Button>
                </div>
              </div>

              {/* Message Details Drawer */}
              <Drawer
                open={selectedMessage !== null}
                onOpenChange={(open) => {
                  if (!open) setSelectedMessage(null);
                }}
                direction="right"
              >
                {selectedMessage && (
                  <DrawerPortal>
                    <DrawerOverlay className="fixed inset-0 bg-black/40" />
                    <DrawerContent className="fixed bottom-0 right-0 top-0 z-50 w-[800px] bg-white">
                      <MessageDetails
                        message={selectedMessage}
                        onClose={() => setSelectedMessage(null)}
                        onReplaySubscription={async (subscriptionName) => {
                          // Here you would implement the replay functionality
                          console.log(
                            'Replaying message for subscription:',
                            subscriptionName,
                          );
                          // Mock async operation
                          await new Promise((resolve) =>
                            setTimeout(resolve, 1000),
                          );
                          alert(`Replayed message for ${subscriptionName}`);
                        }}
                        onDismissSubscription={async (subscriptionName) => {
                          // Here you would implement the dismiss functionality
                          console.log(
                            'Dismissing message for subscription:',
                            subscriptionName,
                          );
                          // Mock async operation
                          await new Promise((resolve) =>
                            setTimeout(resolve, 1000),
                          );
                          alert(`Dismissed message for ${subscriptionName}`);
                        }}
                      />
                    </DrawerContent>
                  </DrawerPortal>
                )}
              </Drawer>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
