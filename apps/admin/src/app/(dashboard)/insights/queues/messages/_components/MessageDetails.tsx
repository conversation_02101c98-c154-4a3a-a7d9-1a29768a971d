import { useMemo, useState } from 'react';
import { Co<PERSON>, RotateCw, X } from 'lucide-react';

import { Button } from '@willow/ui/base/button';
import { useToast } from '@willow/ui/base/use-toast';

interface MessageDetailsProps {
  message: any;
  onClose: () => void;
  onReplaySubscription?: (subscriptionName: string) => Promise<void> | void;
  onDismissSubscription?: (subscriptionName: string) => Promise<void> | void;
}

// Field component similar to PatientInfoField
const MessageInfoField = ({
  label,
  value,
  disabled = false,
  copiable = false,
  children,
}: {
  label: string;
  value: string;
  disabled?: boolean;
  children?: React.ReactNode;
  copiable?: boolean;
}) => {
  const { toast } = useToast();

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: 'Copied',
        variant: 'default',
        duration: 1000,
      });
    } catch {
      toast({
        title: 'An error occurred while copying to clipboard',
        variant: 'destructive',
        duration: 1000,
      });
    }
  };

  return (
    <div className="flex flex-col gap-1">
      <div className="flex flex-row items-center justify-between">
        <div className="text-xs font-medium text-stone/70">{label}</div>
        {children}
      </div>
      <div className="flex flex-row items-center gap-4">
        <div
          className={`text-xs font-normal text-dark ${
            disabled ? 'opacity-50' : ''
          }`}
        >
          {value}
        </div>
        {!disabled && copiable && (
          <button
            type="button"
            tabIndex={-1}
            className="text-left text-xs font-normal text-dark opacity-50"
            onClick={() => copyToClipboard(value)}
          >
            <Copy size={16} />
          </button>
        )}
      </div>
    </div>
  );
};

export function MessageDetails({
  message,
  onClose,
  onReplaySubscription,
  onDismissSubscription,
}: MessageDetailsProps) {
  const [loading, setLoading] = useState<Record<string, boolean>>({});
  const { toast } = useToast();

  const handleReplay = async (subscriptionName: string) => {
    if (!onReplaySubscription) return;

    setLoading((prev) => ({ ...prev, [subscriptionName]: true }));
    try {
      await onReplaySubscription(subscriptionName);
      toast({
        title: 'Success',
        description: `Message replayed for ${subscriptionName}`,
        variant: 'default',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to replay message: ${error instanceof Error ? error.message : String(error)}`,
        variant: 'destructive',
      });
    } finally {
      setLoading((prev) => ({ ...prev, [subscriptionName]: false }));
    }
  };

  const handleDismiss = async (subscriptionName: string) => {
    if (!onDismissSubscription) return;

    setLoading((prev) => ({ ...prev, [subscriptionName]: true }));
    try {
      await onDismissSubscription(subscriptionName);
      toast({
        title: 'Success',
        description: `Message dismissed for ${subscriptionName}`,
        variant: 'default',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to dismiss message: ${error instanceof Error ? error.message : String(error)}`,
        variant: 'destructive',
      });
    } finally {
      setLoading((prev) => ({ ...prev, [subscriptionName]: false }));
    }
  };

  const prettyBody = useMemo(() => {
    try {
      const bodyStr = message.body as string;
      const parsed = JSON.parse(bodyStr);
      return JSON.stringify(parsed, null, 2);
    } catch {
      return message.body as string;
    }
  }, [message.body]);

  const consumerGroups = useMemo(() => {
    const consumerGroups: string[] = message.consumerGroups || [];
    const failedConsumerGroups: string[] = message.failedConsumerGroups || [];

    return consumerGroups.map((group: string) => ({
      name: group,
      failed: failedConsumerGroups.includes(group),
    }));
  }, [message]);

  return (
    <div className="flex h-full bg-white">
      {/* Message Basic Info */}
      <div className="flex h-full w-1/3 flex-col justify-between bg-stone-light px-6 py-10">
        <div className="flex flex-col gap-4">
          <div className="text-sm font-normal text-orange">
            Message Information
          </div>

          <MessageInfoField
            label="Message ID"
            value={message.messageId}
            copiable
          />

          <MessageInfoField
            label="Patient ID"
            value={message.patientId}
            copiable
          />

          <MessageInfoField label="Topic" value={message.topicName} copiable />

          <MessageInfoField label="Event" value={message.event} copiable />

          <MessageInfoField
            label="Date"
            value={new Date(message.timestamp).toLocaleString()}
            copiable
          />
        </div>
      </div>

      <div className="flex w-2/3 flex-col gap-6 overflow-y-scroll px-4 pb-10 pt-10">
        {/* Failed Subscriptions */}
        <div className="flex flex-col gap-4">
          <div className="text-sm font-normal text-orange">Consumer groups</div>

          <div className="flex flex-col gap-4">
            {/* <div className="text-sm font-normal text-red-500">
                  Failed Subscriptions ({message.failedSubscriptions.length})
                </div> */}

            <div className="space-y-2">
              {consumerGroups.map((consumerGroups) => (
                <div
                  key={consumerGroups.name}
                  className="flex items-center justify-between border border-gray-200 p-2"
                >
                  <div className="text-xs font-medium text-dark">
                    {consumerGroups.name}
                  </div>
                  {consumerGroups.failed && (
                    <div className="flex space-x-2">
                      <Button
                        size="xs"
                        variant="denim"
                        onClick={() => handleReplay(consumerGroups.name)}
                        // disabled={loading[subscription]}
                      >
                        <span className="text-xs">Replay</span>
                      </Button>
                      <Button
                        size="xs"
                        variant="tertiaryOutline"
                        onClick={() => handleDismiss(consumerGroups.name)}
                      >
                        Dismiss
                      </Button>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Message Body */}
        <div className="flex flex-col gap-4">
          <div className="text-sm font-normal text-orange">Message Body</div>
          <pre className="max-h-96 overflow-auto rounded-md bg-gray-50 p-4 text-xs font-normal text-dark">
            {prettyBody}
          </pre>
        </div>
      </div>
    </div>
  );
}
