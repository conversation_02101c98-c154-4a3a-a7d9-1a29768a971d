'use client';

import { useEffect } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { useAtom } from 'jotai';

import { Capability } from '@willow/auth';
import { useToast } from '@willow/ui/base/use-toast';
import { Loader } from '@willow/ui/loader';
import { apiClient } from '@willow/utils/api/client';

import { MainSidebar } from '~/components/MainSidebar';
import { ProfileMenu } from '~/components/ProfileMenu';
import { useRefreshToken } from '~/hooks/login';
import { useCapabilities } from '~/hooks/useCapabilities';
import { useProfileQuery } from '~/hooks/useProfile';
import {
  ACCESS_TOKEN_LOCAL_STORAGE_KEY,
  accessTokenAtom,
  clearAccessToken,
  interceptorInjected,
} from '~/store/accessToken';

// Navigation items with their required capabilities
const navigationSections = [
  { path: '/patients', capability: Capability.VIEW_PATIENTS },
  { path: '/doctors', capability: Capability.VIEW_DOCTORS },
  { path: '/pharmacies', capability: Capability.VIEW_PHARMACIES },
  { path: '/products', capability: Capability.VIEW_PRODUCTS },
  { path: '/admins', capability: Capability.VIEW_ADMINS },
  { path: '/states', capability: Capability.VIEW_STATES },
];

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const { isPending, error } = useProfileQuery();
  const { hasCapability } = useCapabilities();
  useAxiosInterceptor();

  // Check if user has access token
  useEffect(() => {
    const token = localStorage.getItem(ACCESS_TOKEN_LOCAL_STORAGE_KEY);
    if (!token) return router.push('/login');
  }, [router]);

  // Check if user has dashboard access and redirect if needed
  useEffect(() => {
    // Skip this check if we're still loading the profile or if we're not on the root path
    if (isPending || pathname !== '/') return;

    // If user doesn't have dashboard capability, find the first section they can access
    if (!hasCapability(Capability.VIEW_DASHBOARD)) {
      // Find the first section the user has access to
      const firstAccessibleSection = navigationSections.find((section) =>
        hasCapability(section.capability),
      );

      // If we found an accessible section, redirect to it
      if (firstAccessibleSection) {
        router.push(firstAccessibleSection.path);
      }
    }
  }, [isPending, pathname, hasCapability, router]);

  if (error) return router.push('/logout');
  if (isPending) return <Loader className="h-screen" size="xl" />;

  return (
    <div className="relative flex h-full">
      <MainSidebar />
      <div className="absolute right-7 top-7">
        <ProfileMenu />
      </div>
      <div className="flex-1">{children}</div>
    </div>
  );
}

const useAxiosInterceptor = () => {
  const [signIn, setSignIn] = useAtom(accessTokenAtom);
  const [_, inject] = useAtom(interceptorInjected);
  const { mutateAsync: refresh } = useRefreshToken();
  const { toast } = useToast();

  useEffect(() => {
    const axiosRequestInterceptor = apiClient.interceptors.request.use(
      (config) => {
        if (config.bypassInterceptor) return config;
        if (signIn?.accessToken) {
          config.headers.Authorization = `Bearer ${signIn.accessToken}`;
        }
        return config;
      },
      (error) => Promise.reject(error as Error),
    );

    if (signIn?.accessToken) {
      inject(true);
    }

    return () => {
      apiClient.interceptors.request.eject(axiosRequestInterceptor);
    };
  }, [inject, signIn?.accessToken]);

  useEffect(() => {
    const axiosResponseInterceptor = apiClient.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.config?.bypassInterceptor) {
          toast({
            title: 'Failed to authenticate. Please Login',
            variant: 'destructive',
          });
          return Promise.reject(error);
        }

        if (error.response?.status === 401 && signIn?.refreshToken) {
          try {
            const response = await refresh({
              refreshToken: signIn.refreshToken,
            });

            if (!response.data.accessToken) {
              throw new Error('No access token found in response');
            }

            setSignIn({
              ...signIn,
              accessToken: response.data.accessToken,
            });

            // Clone the original request
            const originalRequest = error.config;
            originalRequest.headers.Authorization = `Bearer ${response.data.accessToken}`;

            // this is to avoid the interceptor to override the new token
            originalRequest.bypassInterceptor = true;

            // Retry the original request with the new token
            return apiClient(originalRequest);
          } catch (refreshError) {
            console.error('Token refresh failed:', refreshError);
            clearAccessToken();
            window.location.href = '/login';
            return Promise.reject(refreshError);
          }
        }

        return Promise.reject(error);
      },
    );

    return () => {
      apiClient.interceptors.response.eject(axiosResponseInterceptor);
    };
  }, [refresh, setSignIn, signIn, toast]);

  return null;
};
