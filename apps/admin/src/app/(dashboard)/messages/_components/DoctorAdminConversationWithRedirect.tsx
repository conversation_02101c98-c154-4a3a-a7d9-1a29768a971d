'use client';

import { useCallback } from 'react';
import { useRouter } from 'next/navigation';

import type { DoctorAdminConversationGroups } from '~/types/doctor-admin-chat';
import { useGetDoctorAdminConversation } from '~/hooks/doctor-admin-chat';
import { DoctorAdminConversation } from '../../patients/_components/DoctorAdminConversation';

interface DoctorAdminConversationWithRedirectProps {
  conversationId: string;
  group: DoctorAdminConversationGroups;
}

export const DoctorAdminConversationWithRedirect = ({
  conversationId,
  group,
}: DoctorAdminConversationWithRedirectProps) => {
  const router = useRouter();
  const { data: conversation } = useGetDoctorAdminConversation(conversationId);

  const handleMessageSent = useCallback(() => {
    // If we're not in myInbox and the conversation is unassigned,
    // redirect to myInbox after sending a message
    if (group !== 'myInbox' && !conversation?.assignedAdminId) {
      router.push(`/messages/myInbox?conversationId=${conversationId}`);
    }
  }, [group, conversation?.assignedAdminId, conversationId, router]);

  return (
    <DoctorAdminConversation
      conversationId={conversationId}
      onMessageSent={handleMessageSent}
    />
  );
};
