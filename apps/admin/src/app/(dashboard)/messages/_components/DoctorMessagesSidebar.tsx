'use client';

import { useParams } from 'next/navigation';

import { cn } from '@willow/ui';

import type { DoctorAdminConversationGroups } from '~/types/doctor-admin-chat';
import PanelLeftIcon from '~/assets/svg/panel-left.svg';
import { NavbarItem, NavbarSection } from '~/components/NavbarItem';
import { useGetDoctorAdminConversationsCounts } from '~/hooks/doctor-admin-chat';

export const doctorAdminConversationGroups: DoctorAdminConversationGroups[] = [
  'myInbox',
  'all',
  'unassigned',
  'closed',
];

export const formatCamelCaseText = (str: string): string => {
  if (!str) return '';
  const firstLetterCapitalized = str.charAt(0).toUpperCase() + str.slice(1);
  return firstLetterCapitalized.replace(/([A-Z])/g, ' $1');
};

export const DoctorMessagesSidebar = () => {
  const params = useParams();
  const selectedGroup = params.group;
  const { data: groupCounts, isLoading } =
    useGetDoctorAdminConversationsCounts();

  return (
    <aside className="scrollbar-hidden h-screen w-[284px] overflow-y-scroll bg-stone-light px-6 py-10">
      <div className="flex flex-col gap-6 text-[13px]">
        <div className="flex justify-between text-denim">
          <h1 className="text-2xl">Messages</h1>
          <div className="flex items-center gap-2">
            <PanelLeftIcon />
          </div>
        </div>

        <NavbarSection title="">
          {doctorAdminConversationGroups.map((group) => (
            <DoctorMessagesSidebarLink
              key={group}
              title={formatCamelCaseText(group)}
              group={group}
              count={groupCounts?.[group]}
              isSelected={selectedGroup === group}
              isLoading={isLoading}
            />
          ))}
        </NavbarSection>
      </div>
    </aside>
  );
};

const DoctorMessagesSidebarLink = ({
  title,
  group,
  count = 0,
  isSelected,
  isLoading,
}: {
  title: string;
  group: DoctorAdminConversationGroups;
  count?: number;
  isSelected: boolean;
  isLoading: boolean;
}) => {
  return (
    <NavbarItem
      href={`/messages/${group}`}
      title={title}
      count={count}
      className={cn('text-xs font-normal text-black', {
        'text-[#FE6232]': isSelected,
      })}
      countClassName={cn('text-xs font-normal text-black', {
        'text-white': isSelected,
        'animate-pulse': isLoading,
        'text-gray-400': count === 0,
      })}
    />
  );
};
