'use client';

import { redirect } from 'next/navigation';

import type { DoctorAdminConversationGroups } from '~/types/doctor-admin-chat';
import { DoctorMessages } from '../_components/DoctorMessages';
import { doctorAdminConversationGroups } from '../_components/DoctorMessagesSidebar';

export default function Page({
  params: { group },
}: {
  params: { group: DoctorAdminConversationGroups };
}) {
  if (!doctorAdminConversationGroups.includes(group))
    return redirect('/messages/myInbox');

  return <DoctorMessages group={group} />;
}
