import { useMemo } from 'react';
import Link from 'next/link';
import { EllipsisIcon } from 'lucide-react';

import { cn } from '@willow/ui';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@willow/ui/base/dropdown-menu';

interface ActionType {
  name: string;
  handleAction?: () => void;
  href?: string;
  className?: string;
}

export const ProductCategoryMiscMenu = ({
  isEnabled,
  setIsEditMode,
  handleToggleStatus,
  handleDelete,
  canDelete = false,
}: {
  isEnabled: boolean;
  setIsEditMode: (value: boolean) => void;
  handleToggleStatus: () => void;
  handleDelete: () => void;
  canDelete?: boolean;
}) => {
  const actions: ActionType[] = useMemo(() => {
    const menuItems = [
      {
        name: 'Edit Category',
        handleAction: () => setIsEditMode(true),
      },
      { name: 'separator-1' },
      {
        name: isEnabled ? 'Deactivate Category' : 'Activate Category',
        className: isEnabled ? 'text-red-500' : undefined,
        handleAction: handleToggleStatus,
      },
    ];

    if (canDelete) {
      menuItems.push({ name: 'separator-2' });
      menuItems.push({
        name: 'Delete Category',
        className: 'text-red-700 font-semibold',
        handleAction: handleDelete,
      });
    }

    return menuItems;
  }, [isEnabled, setIsEditMode, handleToggleStatus, handleDelete, canDelete]);

  return (
    <div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <div className="cursor-pointer">
            <EllipsisIcon size={24} color="#2F4C78" />
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-44 px-0" align="end">
          {actions.map((action) => {
            if (action.name.includes('separator'))
              return (
                <DropdownMenuSeparator
                  key={action.name}
                  className="mx-auto w-10/12"
                />
              );

            if (action.href) {
              return (
                <Link
                  href={action.href}
                  key={action.name}
                  onClick={(e) => {
                    // Stop propagation to prevent drawer from opening
                    e.stopPropagation();
                  }}
                >
                  <DropdownMenuItem
                    className={cn(
                      'cursor-pointer px-4 py-3 text-xs font-normal focus:bg-[#ffecca] focus:text-[#2f4c78]',
                      action.className,
                    )}
                    // Prevent onSelect from firing which could interfere with navigation
                    onSelect={(e) => {
                      e.preventDefault();
                    }}
                  >
                    {action.name}
                  </DropdownMenuItem>
                </Link>
              );
            }

            return (
              <DropdownMenuItem
                key={action.name}
                onSelect={() => {
                  if (action.handleAction) {
                    action.handleAction();
                  }
                }}
                className={cn(
                  'cursor-pointer px-4 py-3 text-xs font-normal focus:bg-[#ffecca] focus:text-[#2f4c78]',
                  action.className,
                )}
              >
                {action.name}
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
