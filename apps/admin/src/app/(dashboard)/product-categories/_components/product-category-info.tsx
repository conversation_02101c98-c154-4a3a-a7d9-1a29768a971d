'use client';

import { useEffect, useRef, useState } from 'react';
import Link from 'next/link';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { XIcon } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@willow/ui/base/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@willow/ui/base/form';
import { Input } from '@willow/ui/base/input';
import { Switch } from '@willow/ui/base/switch';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@willow/ui/base/tabs';
import { Textarea } from '@willow/ui/base/textarea';
import { useToast } from '@willow/ui/base/use-toast';
import { Loader } from '@willow/ui/loader';

import {
  useCreateProductCategory,
  useDeleteProductCategory,
  useGetProductCategory,
  useUpdateProductCategory,
  useUploadProductCategoryImage,
} from '~/hooks/product-categories';
import { useGetProductsByCategory } from '~/hooks/products';
import { ProductCategoryMiscMenu } from './product-category-misc-menu';

interface ProductCategoryInfoProps {
  categoryId: string;
  onClose: () => void;
}

const createCategorySchema = z.object({
  name: z.string().min(1, 'Category name is required'),
  form: z.string().optional().nullable(),
  shortDescription: z.string().optional().nullable(),
  description: z.string().optional().nullable(),
  label: z.string().optional().nullable(),
  tags: z.string().optional().nullable(),
  customCard: z.string().optional().nullable(),
  enabled: z.boolean(),
  order: z.coerce.number(),
});

const updateCategorySchema = createCategorySchema;

interface CategorySidebarProps {
  category: any;
  showMenu?: boolean;
  setIsEditMode?: (isEdit: boolean) => void;
  handleToggleStatus?: () => void;
  handleDelete?: () => void;
}

function CategorySidebar({
  category,
  showMenu = true,
  setIsEditMode,
  handleToggleStatus,
  handleDelete,
}: CategorySidebarProps) {
  return (
    <div className="flex w-1/3 flex-col justify-between overflow-scroll bg-stone-light px-6 py-10">
      <div className="flex flex-col gap-6">
        <div className="flex justify-between">
          <div className="flex flex-row items-center gap-4">
            {category.image ? (
              <div className="relative h-[50px] w-[50px] overflow-hidden rounded-full">
                <img
                  src={category.image}
                  alt={category.name}
                  className="h-full w-full object-cover"
                />
              </div>
            ) : (
              <div className="relative flex h-[50px] w-[50px] items-center justify-center rounded-full bg-denim-light text-xl text-white">
                📁
              </div>
            )}
            <div>
              <div className="text-base font-medium text-dark">
                {category.label || category.name}
              </div>
              <div className="flex flex-col text-[11px] font-medium text-stone/70">
                <span>{category.form || 'No form'}</span>
              </div>
            </div>
          </div>
          {showMenu && setIsEditMode && handleToggleStatus && handleDelete && (
            <div className="flex flex-row items-start gap-4">
              <ProductCategoryMiscMenu
                isEnabled={category.enabled}
                setIsEditMode={setIsEditMode}
                handleToggleStatus={handleToggleStatus}
                handleDelete={handleDelete}
                canDelete={
                  !category._count?.Product || category._count.Product === 0
                }
              />
            </div>
          )}
        </div>

        <div className="flex flex-col gap-4">
          <div className="text-sm font-normal text-orange">
            Basic Information
          </div>
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">Status</div>
            <div
              className={`inline-flex w-fit rounded-full px-2 py-1 text-xs font-medium ${
                category.enabled
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
              }`}
            >
              {category.enabled ? 'Active' : 'Inactive'}
            </div>
          </div>
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">Name</div>
            <div className="text-xs font-normal text-dark">{category.name}</div>
          </div>
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">Form</div>
            <div className="text-xs font-normal text-dark">
              {category.form || 'Not Set'}
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-4">
          <div className="text-sm font-normal text-orange">
            Additional Details
          </div>
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">Label</div>
            <div className="text-xs font-normal text-dark">
              {category.label || 'Not Set'}
            </div>
          </div>
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">Tags</div>
            <div className="text-xs font-normal text-dark">
              {category.tags || 'Not Set'}
            </div>
          </div>
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">Order</div>
            <div className="text-xs font-normal text-dark">
              {category.order}
            </div>
          </div>
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">Custom Card</div>
            <div className="text-xs font-normal text-dark">
              {category.customCard || 'Not Set'}
            </div>
          </div>
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">Products</div>
            <div className="text-xs font-normal text-dark">
              {category._count?.Product || 0} products
            </div>
          </div>
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">Created At</div>
            <div className="text-xs font-normal text-dark">
              {format(new Date(category.createdAt), 'MMM dd, yyyy')}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export function ProductCategoryInfo({
  categoryId,
  onClose,
}: ProductCategoryInfoProps) {
  const isNewCategory = categoryId === 'new';

  // Get the current category data (or empty if new)
  const {
    data: category,
    isLoading,
    refetch,
  } = useGetProductCategory(isNewCategory ? '' : categoryId);

  const { mutateAsync: createCategory } = useCreateProductCategory();
  const { mutateAsync: updateCategory } = useUpdateProductCategory();
  const { mutateAsync: deleteCategory } = useDeleteProductCategory();
  const { mutateAsync: uploadImage, isPending: isUploading } =
    useUploadProductCategoryImage();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isEditMode, setIsEditMode] = useState(isNewCategory);
  const [selectedTab, setSelectedTab] = useState('details');
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const form = useForm<{
    name: string;
    form?: string | null;
    shortDescription?: string | null;
    description?: string | null;
    label?: string | null;
    tags?: string | null;
    customCard?: string | null;
    enabled: boolean;
    order: number;
  }>({
    resolver: zodResolver(
      isNewCategory ? createCategorySchema : updateCategorySchema,
    ),
    defaultValues: isNewCategory
      ? {
          name: '',
          form: '',
          shortDescription: '',
          description: '',
          label: '',
          tags: '',
          customCard: '',
          enabled: true,
          order: 0,
        }
      : undefined,
  });

  // Load category data when available
  useEffect(() => {
    if (category && !isNewCategory) {
      if (category.image) {
        setImageUrl(category.image);
      }
      form.reset({
        name: category.name,
        form: category.form,
        shortDescription: category.shortDescription,
        description: category.description,
        label: category.label,
        tags: category.tags,
        customCard: category.customCard,
        enabled: category.enabled,
        order: category.order,
      });
    }
  }, [category, isNewCategory, form]);

  const handleToggleStatus = async () => {
    if (!category) return;

    try {
      await updateCategory({
        id: category.id,
        data: { enabled: !category.enabled },
      });
      await refetch();
      toast({
        title: category.enabled ? 'Category Deactivated' : 'Category Activated',
        description: `${category.name} has been ${
          category.enabled ? 'deactivated' : 'activated'
        }.`,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update category status',
        variant: 'destructive',
      });
    }
  };

  const handleDelete = async () => {
    if (!category) return;

    try {
      await deleteCategory(category.id);
      toast({
        title: 'Category Deleted',
        description: `${category.name} has been deleted.`,
      });
      onClose();
    } catch (error: any) {
      toast({
        title: 'Error',
        description:
          error?.response?.data?.message || 'Failed to delete category',
        variant: 'destructive',
      });
    }
  };

  const handleImageUpload = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!/^image\/(jpeg|png|jpg|gif)$/.exec(file.type)) {
      toast({
        title: 'Error',
        description: 'Please select a valid image file (JPEG, PNG, or GIF)',
        variant: 'destructive',
      });
      return;
    }

    // Validate file size
    if (file.size > 5 * 1024 * 1024) {
      // 5MB
      toast({
        title: 'Error',
        description: 'Image size exceeds 5MB limit',
        variant: 'destructive',
      });
      return;
    }

    try {
      // Show uploading state
      toast({
        title: 'Processing',
        description: 'Uploading image...',
      });

      // For existing categories, upload directly
      if (!isNewCategory && categoryId !== 'new') {
        const result = await uploadImage({ categoryId, file });

        // Set the image URL in state
        setImageUrl(result.url);

        // Refetch category to get updated data
        await refetch();
      } else {
        // For new categories, we need to save the category first
        toast({
          title: 'Info',
          description:
            'Please save the category first before uploading an image',
        });
        return;
      }

      toast({
        title: 'Success',
        description: 'Image uploaded successfully',
      });
    } catch (error: unknown) {
      console.error('Error uploading image:', error);
      toast({
        title: 'Error',
        description: 'Failed to upload image',
        variant: 'destructive',
      });
    }
  };

  const onSubmit = async (data: z.infer<typeof createCategorySchema>) => {
    setIsSubmitting(true);
    try {
      const submitData = data;

      if (isNewCategory) {
        await createCategory(submitData);
        toast({
          title: 'Category Created',
          description: `${data.name} has been created successfully.`,
        });
        onClose();
      } else {
        await updateCategory({ id: categoryId, data: submitData });
        await refetch();
        toast({
          title: 'Category Updated',
          description: `${data.name} has been updated successfully.`,
        });
        setIsEditMode(false);
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: isNewCategory
          ? 'Failed to create category'
          : 'Failed to update category',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex h-full w-full items-center justify-center bg-white">
        <Loader className="w-full bg-white" />
      </div>
    );
  }

  // Edit mode for existing categories
  if (isEditMode) {
    // For existing categories, use the split layout
    if (!isNewCategory && category) {
      return (
        <div className="relative flex h-full w-full grow bg-white">
          <CategorySidebar category={category} showMenu={false} />
          <div className="flex w-2/3 flex-col overflow-scroll">
            <div className="sticky top-0 z-10 bg-white px-10 pt-10">
              <div className="mb-6 flex flex-row justify-between">
                <h2 className="text-xl font-semibold text-denim">
                  Edit Category
                </h2>
                <XIcon
                  size={24}
                  className="cursor-pointer text-denim"
                  onClick={() => setIsEditMode(false)}
                />
              </div>
            </div>

            <div className="px-10 pb-10">
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-5"
                >
                  <div className="grid grid-cols-1 gap-6">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Name</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Enter category name"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Image Upload Section */}
                    <div>
                      <FormLabel>Category Image</FormLabel>
                      <div className="mt-2 flex flex-col gap-4">
                        {/* Image Preview */}
                        {imageUrl && (
                          <div className="relative h-48 w-48 overflow-hidden rounded-md">
                            <img
                              src={imageUrl}
                              alt="Category Preview"
                              className="h-full w-full object-cover"
                            />
                          </div>
                        )}

                        {/* Upload Button and Field */}
                        <div className="flex flex-col gap-2">
                          <Button
                            type="button"
                            variant="denim"
                            className="w-fit"
                            onClick={() => fileInputRef.current?.click()}
                            disabled={isUploading}
                          >
                            {isUploading
                              ? 'Uploading...'
                              : 'Upload Category Image'}
                          </Button>
                          <input
                            type="file"
                            ref={fileInputRef}
                            className="hidden"
                            accept="image/jpeg,image/png,image/jpg,image/gif"
                            onChange={handleImageUpload}
                          />
                          <p className="text-xs text-gray-500">
                            Accepted formats: JPG, PNG, GIF. Max size: 5MB.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="label"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Label</FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                value={field.value || ''}
                                placeholder="Enter display label"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="form"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Form</FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                value={field.value || ''}
                                placeholder="Enter form"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="tags"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Tags</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              value={field.value || ''}
                              placeholder="Enter tags separated by commas"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="shortDescription"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Short Description</FormLabel>
                          <FormControl>
                            <Textarea
                              {...field}
                              value={field.value || ''}
                              placeholder="Enter a brief description for display"
                              rows={2}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Textarea
                              {...field}
                              value={field.value || ''}
                              placeholder="Enter category description"
                              rows={4}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="customCard"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Custom Card</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              value={field.value || ''}
                              placeholder="Enter custom card value"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="order"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Order</FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                type="number"
                                placeholder="Enter display order"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="enabled"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center space-x-3 space-y-0 rounded-md border p-4">
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>Enabled</FormLabel>
                            </div>
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  <div className="flex justify-between border-t pt-6">
                    <div className="flex gap-2">
                      <Button
                        type="button"
                        variant="tertiary"
                        onClick={() => setIsEditMode(false)}
                        disabled={isSubmitting}
                      >
                        Cancel
                      </Button>
                      <Button type="submit" disabled={isSubmitting}>
                        {isSubmitting ? 'Saving...' : 'Update Category'}
                      </Button>
                    </div>
                  </div>
                </form>
              </Form>
            </div>
          </div>
        </div>
      );
    }

    // For new categories, use a split layout with a thin column
    return (
      <div className="relative flex h-full w-full grow bg-white">
        {/* Left sidebar - thin column */}
        <div className="flex w-[50px] flex-col bg-stone-light">
          {/* Empty sidebar */}
        </div>

        {/* Right content area with form */}
        <div className="flex w-[calc(100%-50px)] flex-col overflow-scroll">
          <div className="sticky top-0 z-10 bg-white px-10 pt-10">
            <div className="mb-6 flex flex-row justify-between">
              <h2 className="text-xl font-semibold text-denim">
                Add New Category
              </h2>
              <XIcon
                size={24}
                className="cursor-pointer text-denim"
                onClick={onClose}
              />
            </div>
          </div>

          <div className="px-10 pb-10">
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-5"
              >
                <div className="grid grid-cols-1 gap-6">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Category Name</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Image Upload Section */}
                  <div>
                    <FormLabel>Category Image</FormLabel>
                    <div className="mt-2 flex flex-col gap-4">
                      {/* Image Preview */}
                      {imageUrl && (
                        <div className="relative h-48 w-48 overflow-hidden rounded-md">
                          <img
                            src={imageUrl}
                            alt="Category Preview"
                            className="h-full w-full object-cover"
                          />
                        </div>
                      )}

                      {/* Upload Button and Field */}
                      <div className="flex flex-col gap-2">
                        <Button
                          type="button"
                          variant="denim"
                          className="w-fit"
                          onClick={() => fileInputRef.current?.click()}
                          disabled={isUploading}
                        >
                          {isUploading
                            ? 'Uploading...'
                            : 'Upload Category Image'}
                        </Button>
                        <input
                          type="file"
                          ref={fileInputRef}
                          className="hidden"
                          accept="image/jpeg,image/png,image/jpg,image/gif"
                          onChange={handleImageUpload}
                        />
                        <p className="text-xs text-gray-500">
                          Accepted formats: JPG, PNG, GIF. Max size: 5MB.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="label"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Label</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              value={field.value || ''}
                              placeholder="Enter display label"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="form"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Form</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              value={field.value || ''}
                              placeholder="e.g., injectable, oral"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="tags"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tags</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            value={field.value || ''}
                            placeholder="Enter tags separated by commas"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="shortDescription"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Short Description</FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            value={field.value || ''}
                            placeholder="Enter a brief description for display"
                            rows={2}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            value={field.value || ''}
                            rows={4}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="customCard"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Custom Card</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            value={field.value || ''}
                            placeholder="Enter custom card value"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="order"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Order</FormLabel>
                          <FormControl>
                            <Input {...field} type="number" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="enabled"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>Enabled</FormLabel>
                          </div>
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <div className="flex justify-between border-t pt-6">
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="tertiary"
                      onClick={onClose}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting ? 'Creating...' : 'Create Category'}
                    </Button>
                  </div>
                </div>
              </form>
            </Form>
          </div>
        </div>
      </div>
    );
  }

  // View mode for existing categories with split layout
  if (!isEditMode && !isNewCategory && category) {
    return (
      <div className="relative flex h-full w-full grow bg-white">
        {/* Use the reusable sidebar component with menu */}
        <CategorySidebar
          category={category}
          setIsEditMode={setIsEditMode}
          handleToggleStatus={handleToggleStatus}
          handleDelete={handleDelete}
        />

        {/* Right content area with tabs */}
        <div className="flex w-2/3 flex-col overflow-scroll">
          <Tabs
            defaultValue={selectedTab}
            onValueChange={setSelectedTab}
            className="relative grid w-full grid-rows-[auto_1fr]"
          >
            <div className="sticky top-0 z-10 bg-white px-10 pt-10">
              <div className="flex flex-row justify-end text-denim">
                <XIcon size={24} className="cursor-pointer" onClick={onClose} />
              </div>
              <TabsList className="h-fit w-full justify-start gap-5 rounded-none border-b-[1px] border-denim-muted bg-white p-0 text-denim">
                <TabsTrigger
                  className="rounded-none text-xs font-normal text-muted-foreground data-[state=active]:border-b-[3px] data-[state=active]:border-denim data-[state=active]:text-denim data-[state=active]:shadow-none"
                  value="details"
                >
                  Details
                </TabsTrigger>
                <TabsTrigger
                  className="rounded-none text-xs font-normal text-muted-foreground data-[state=active]:border-b-[3px] data-[state=active]:border-denim data-[state=active]:text-denim data-[state=active]:shadow-none"
                  value="products"
                >
                  Products
                </TabsTrigger>
              </TabsList>
            </div>

            {/* Details tab content */}
            <TabsContent className="px-10" value="details">
              <div className="mb-5 mt-10 space-y-6">
                <div className="text-2xl font-medium text-denim">
                  Category Details
                </div>

                {/* Category Image */}
                {category.image && (
                  <div className="flex justify-center">
                    <div className="relative h-48 w-48 overflow-hidden rounded-md">
                      <img
                        src={category.image}
                        alt={category.name}
                        className="h-full w-full object-cover"
                      />
                    </div>
                  </div>
                )}

                {/* Short Description */}
                <div className="space-y-2">
                  <h3 className="text-lg font-medium text-denim">
                    Short Description
                  </h3>
                  <div className="rounded-lg border p-4 shadow-sm">
                    <p className="text-sm">
                      {category.shortDescription ||
                        'No short description provided'}
                    </p>
                  </div>
                </div>

                {/* Category Description */}
                <div className="space-y-2">
                  <h3 className="text-lg font-medium text-denim">
                    Description
                  </h3>
                  <div className="rounded-lg border p-4 shadow-sm">
                    <p className="text-sm">
                      {category.description || 'No description provided'}
                    </p>
                  </div>
                </div>

                {/* Category Information */}
                <div className="space-y-2">
                  <h3 className="text-lg font-medium text-denim">
                    Information
                  </h3>
                  <div className="rounded-lg border p-4 shadow-sm">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-xs font-medium text-gray-500">
                          Form
                        </p>
                        <p className="text-sm font-medium">
                          {category.form || 'Not specified'}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs font-medium text-gray-500">
                          Display Order
                        </p>
                        <p className="text-sm font-medium">{category.order}</p>
                      </div>
                      <div>
                        <p className="text-xs font-medium text-gray-500">
                          Status
                        </p>
                        <div
                          className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${
                            category.enabled
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}
                        >
                          {category.enabled ? 'Active' : 'Inactive'}
                        </div>
                      </div>
                      <div>
                        <p className="text-xs font-medium text-gray-500">
                          Products
                        </p>
                        <p className="text-sm font-medium">
                          {category._count?.Product || 0} products
                        </p>
                      </div>
                      <div className="col-span-2">
                        <p className="text-xs font-medium text-gray-500">
                          Tags
                        </p>
                        <p className="text-sm font-medium">
                          {category.tags || 'No tags'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Products tab content */}
            <TabsContent className="px-10" value="products">
              <ProductsInCategory categoryId={categoryId} />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    );
  }

  return null;
}

// Component to show products in a category
function ProductsInCategory({ categoryId }: { categoryId: string }) {
  const { data, isLoading } = useGetProductsByCategory(categoryId, {
    limit: 100, // Fetch all products for this category
    sortBy: 'order',
    direction: 'asc',
  });

  if (isLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <Loader className="w-full bg-white" />
      </div>
    );
  }

  const products = data?.products || [];

  return (
    <div className="mb-5 mt-10 space-y-6">
      <div className="text-2xl font-medium text-denim">
        Products in this Category
      </div>

      {/* Products List */}
      <div className="mb-8">
        {products.length > 0 ? (
          <ul className="space-y-3">
            {products.map((product) => (
              <li key={product.id} className="border-b pb-3">
                <Link
                  href={`/products/all?productId=${product.id}`}
                  className="flex items-center justify-between rounded-md p-2 hover:bg-gray-50"
                >
                  <div className="flex flex-col">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">
                        {product.label || product.name}
                      </span>
                      {!product.active && (
                        <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                          Inactive
                        </span>
                      )}
                    </div>
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <span>{product.form || 'No form'}</span>
                      {product.pharmacy && (
                        <>
                          <span>•</span>
                          <span>{product.pharmacy.name}</span>
                        </>
                      )}
                    </div>
                  </div>
                  {product.defaultPrice && (
                    <span className="text-sm font-semibold">
                      ${(product.defaultPrice.unit_amount / 100).toFixed(2)}
                    </span>
                  )}
                </Link>
              </li>
            ))}
          </ul>
        ) : (
          <p className="mb-4 text-sm text-gray-500">
            No products in this category
          </p>
        )}
      </div>
    </div>
  );
}
