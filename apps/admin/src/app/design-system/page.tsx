'use client';

import { Input } from '@willow/ui/base/input';

import { ButtonsShowcase } from './_components/buttons';

export default function DesignSystemPage() {
  return (
    <div className="p-8">
      <h1 className="pb-8 text-5xl font-semibold">Willow Design System</h1>

      <div className="flex flex-col gap-y-10">
        <div className="">
          <h2 className="pb-4 text-3xl font-medium">Input</h2>

          <table className="min-w-[1200px]">
            <thead>
              <tr className="text-left">
                <th></th>
                <th>Small</th>
                <th>Medium</th>
                <th>Disabled</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="pt-8">denim</td>
                <td className="pr-8 pt-4">
                  <Input size={'sm'} variant={'denim'} />
                </td>
                <td className="pr-8 pt-4">
                  <Input size={'md'} variant={'denim'} />
                </td>
                <td className="pr-8 pt-4">
                  <Input disabled variant={'denim'} />
                </td>
              </tr>

              <tr>
                <td className="pt-8">denim Outline</td>
                <td className="pr-8 pt-8">
                  <Input size={'sm'} variant={'denimOutline'} />
                </td>
                <td className="pr-8 pt-8">
                  <Input size={'md'} variant={'denimOutline'} />
                </td>
                <td className="pr-8 pt-8">
                  <Input disabled variant={'denimOutline'} />
                </td>
              </tr>

              <tr className="bg-slate-200">
                <td className="pt-8">White</td>
                <td className="pr-8 pt-8">
                  <Input size={'sm'} variant={'white'} />
                </td>
                <td className="pr-8 pt-8">
                  <Input size={'md'} variant={'white'} />
                </td>
                <td className="pr-8 pt-8">
                  <Input disabled variant={'white'} />
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <ButtonsShowcase />
      </div>
    </div>
  );
}
