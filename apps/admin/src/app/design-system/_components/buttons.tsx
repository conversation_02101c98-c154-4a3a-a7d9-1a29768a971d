'use client';

import { ArrowRightCircle } from 'lucide-react';

import { But<PERSON> } from '@willow/ui/base/button';

export function ButtonsShowcase() {
  return (
    <div className="">
      <h2 className="pb-4 text-3xl font-medium">Buttons</h2>

      <table className="min-w-[1200px]">
        <thead>
          <tr className="text-left">
            <th></th>
            <th>Extra Small</th>
            <th>Small</th>
            <th>Medium</th>
            <th>Large</th>
            <th>Disabled</th>
          </tr>
        </thead>
        <tbody>
          <tr className="">
            <td className="py-4 text-xl font-semibold">Electric</td>
            <td className="">
              <Button variant="electric" size="xs">
                Label
              </Button>
            </td>
            <td className="">
              <Button variant="electric" size="sm">
                Label
              </Button>
            </td>
            <td className="py-4">
              <Button variant="electric" size="md">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
            <td className="py-4">
              <Button variant="electric" size="lg">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
            <td className="py-4">
              <Button disabled variant="electric" size="md">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
          </tr>

          <tr>
            <td className="py-4 text-xl font-semibold">Electric Outline</td>
            <td className="">
              <Button variant="electricOutline" size="xs">
                Label
              </Button>
            </td>
            <td className="">
              <Button variant="electricOutline" size="sm">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
            <td className="py-4">
              <Button variant="electricOutline" size="md">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
            <td className="py-4">
              <Button variant="electricOutline" size="lg">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
            <td className="py-4">
              <Button disabled variant="electricOutline" size="md">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
          </tr>

          <tr>
            <td className="text-xl font-semibold">Denim</td>
            <td className="py-4">
              <Button variant="denim" size="xs">
                Label
              </Button>
            </td>
            <td className="py-4">
              <Button variant="denim" size="sm">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
            <td className="py-4">
              <Button variant="denim" size="md">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
            <td className="py-4">
              <Button variant="denim" size="lg">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
            <td className="py-4">
              <Button disabled variant="denim" size="md">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
          </tr>

          <tr>
            <td className="text-xl font-semibold">Denim Outline</td>
            <td className="py-4">
              <Button variant="denimOutline" size="xs">
                Label
              </Button>
            </td>
            <td className="py-4">
              <Button variant="denimOutline" size="sm">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
            <td className="py-4">
              <Button variant="denimOutline" size="md">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
            <td className="py-4">
              <Button variant="denimOutline" size="lg">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
            <td className="py-4">
              <Button disabled variant="denimOutline" size="md">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
          </tr>

          <tr>
            <td className="text-xl font-semibold">Tertiary</td>
            <td className="py-4">
              <Button variant="tertiary" size="xs">
                Label
              </Button>
            </td>
            <td className="py-4">
              <Button variant="tertiary" size="sm">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
            <td className="py-4">
              <Button variant="tertiary" size="md">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
            <td className="py-4">
              <Button variant="tertiary" size="lg">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
            <td className="py-4">
              <Button disabled variant="tertiary" size="md">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
          </tr>

          <tr>
            <td className="text-xl font-semibold">Tertiary Outline</td>
            <td className="py-4">
              <Button variant="tertiaryOutline" size="xs">
                Label
              </Button>
            </td>
            <td className="py-4">
              <Button variant="tertiaryOutline" size="sm">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
            <td className="py-4">
              <Button variant="tertiaryOutline" size="md">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
            <td className="py-4">
              <Button variant="tertiaryOutline" size="lg">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
            <td className="py-4">
              <Button disabled variant="tertiaryOutline" size="md">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
          </tr>

          <tr>
            <td className="text-xl font-semibold">Link</td>
            <td className="py-4">
              <Button variant="denimLink" size="xs">
                Label
              </Button>
            </td>
            <td className="py-4">
              <Button variant="denimLink" size="sm">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
            <td className="py-4">
              <Button variant="denimLink" size="md">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
            <td className="py-4">
              <Button variant="denimLink" size="lg">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
            <td className="py-4">
              <Button disabled variant="denimLink" size="md">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
          </tr>

          <tr>
            <td className="text-xl font-semibold">Link</td>
            <td className="py-4">
              <Button variant="tertiaryLink" size="xs">
                Label
              </Button>
            </td>
            <td className="py-4">
              <Button variant="tertiaryLink" size="sm">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
            <td className="py-4">
              <Button variant="tertiaryLink" size="md">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
            <td className="py-4">
              <Button variant="tertiaryLink" size="lg">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
            <td className="py-4">
              <Button disabled variant="tertiaryLink" size="md">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
          </tr>

          <tr>
            <td className="text-xl font-semibold">Destructive</td>
            <td className="py-4">
              <Button variant="destructive" size="xs">
                Label
              </Button>
            </td>
            <td className="py-4">
              <Button variant="destructive" size="sm">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
            <td className="py-4">
              <Button variant="destructive" size="md">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
            <td className="py-4">
              <Button variant="destructive" size="lg">
                Label
                <ArrowRightCircle />
              </Button>
            </td>
          </tr>

          <tr>
            <td className="text-xl font-semibold">Icon</td>
            <td className="py-4">
              <Button variant="electric" size="icon">
                <ArrowRightCircle />
              </Button>
            </td>
            <td className="py-4">
              <Button variant="denim" size="icon">
                <ArrowRightCircle />
              </Button>
            </td>
            <td className="py-4">
              <Button variant="tertiary" size="icon">
                <ArrowRightCircle />
              </Button>
            </td>
            <td className="py-4">
              <Button variant="denimLink" size="icon">
                <ArrowRightCircle />
              </Button>
            </td>
            <td>
              <Button variant="electric" size="icon" disabled>
                <ArrowRightCircle />
              </Button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  );
}
