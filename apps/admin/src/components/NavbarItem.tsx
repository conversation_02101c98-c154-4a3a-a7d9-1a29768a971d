'use client';

import type { LinkProps } from 'next/link';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

import { cn } from '@willow/ui';

import { Chip } from './Chip';

const useIsActiveLink = (href?: string) => {
  const currentPath = usePathname();
  if (!href) return false;
  return currentPath.endsWith(href);
};

export const NavbarSection = ({
  title,
  titleCount,
  href,
  children,
}: {
  title: string;
  titleCount?: number;
  href?: string;
  children: React.ReactNode;
}) => {
  const Comp = href ? Link : 'div';

  const isActive = useIsActiveLink(href);

  return (
    <div className="flex flex-col gap-3">
      <Comp
        href={href ?? ''}
        className="flex items-center justify-between text-denim"
      >
        <h4 className="text-sm font-medium text-orange">{title}</h4>
        {titleCount && (
          <Chip className={cn({ 'bg-denim text-white': isActive })}>
            {titleCount}
          </Chip>
        )}
      </Comp>
      {children}
    </div>
  );
};

export const NavbarItem = ({
  title,
  count,
  href,
  countClassName,
  className,
}: {
  title: string;
  count?: number;
  href?: LinkProps['href'];
  countClassName?: string;
  className?: string;
}) => {
  const Comp = href ? Link : 'div';
  const isActive = useIsActiveLink(href as string);

  return (
    <Comp
      href={href ?? ''}
      className={cn(
        'font-objectSans flex items-center justify-between text-dark',
        { 'line-through': !href },
        className,
      )}
    >
      <span>{title}</span>
      <Chip
        className={cn(
          'px-1 text-center text-black',
          { 'min-w-4 bg-denim text-white': isActive },
          countClassName,
        )}
      >
        {count}
      </Chip>
    </Comp>
  );
};
