import { useRouter } from 'next/navigation';
import { ChevronDown } from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@willow/ui/base/avatar';
import { Button } from '@willow/ui/base/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@willow/ui/base/dropdown-menu';

import { useProfile } from '~/hooks/useProfile';

export function ProfileMenu() {
  const admin = useProfile();

  const router = useRouter();
  function handleLogout() {
    router.push('/logout');
  }

  const fullName = `${admin.firstName} ${admin.lastName}`;
  return (
    <DropdownMenu>
      <DropdownMenuTrigger className="">
        <div className="flex items-center justify-center gap-2">
          <div className="flex items-center gap-2 overflow-hidden text-ellipsis whitespace-nowrap text-base font-medium uppercase text-denim">
            <Avatar className="h-7 w-7">
              <AvatarImage src={'/user-placeholder-avatar.png'} />
              <AvatarFallback className="font-bold text-denim-light">
                {fullName.split(' ').map((_) => _[0])}
              </AvatarFallback>
            </Avatar>
            <span className="hidden md:inline">{fullName}</span>
          </div>
          <ChevronDown className="text-denim" />
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-48 max-w-48">
        <DropdownMenuItem
          className="group cursor-pointer"
          onClick={handleLogout}
        >
          <Button
            variant="denimLink"
            size={'sm'}
            className="!h-auto w-full justify-start font-semibold"
          >
            Log out
          </Button>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
