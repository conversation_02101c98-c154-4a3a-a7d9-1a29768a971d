import type { ReactNode } from 'react';

import type {
  AdminRole,
  WithCapabilityProps as AuthWithCapabilityProps,
  Capability,
} from '@willow/auth';
import { WithCapability as AuthWithCapability } from '@willow/auth';

import { useProfileQuery } from '~/hooks/useProfile';

/**
 * Props for the WithCapabilityWrapper component
 * Omits the profile prop since it's provided internally
 */
export type WithCapabilityWrapperProps = Omit<
  AuthWithCapabilityProps,
  'profile'
>;

/**
 * A wrapper around the WithCapability component from @willow/auth
 * that automatically provides the profile from the useProfileQuery hook
 *
 * Unlike the original useProfile hook, this doesn't throw an error if
 * the user is not logged in, making it safe to use in layouts.
 */
export function WithCapability({
  requiredCapabilities,
  requireAll = false,
  hideWhenDisabled = true,
  children,
  fallback = null,
  disabledClassName = 'opacity-50 pointer-events-none',
}: WithCapabilityWrapperProps) {
  const { data: profile } = useProfileQuery();

  // If no profile exists yet, always render the fallback
  if (!profile) {
    return <>{fallback}</>;
  }

  // Convert string role to AdminRole enum
  const typedProfile = {
    ...profile,
    admin: {
      ...profile.admin,
      role: profile.admin.role as AdminRole,
    },
  };

  return (
    <AuthWithCapability
      profile={typedProfile}
      requiredCapabilities={requiredCapabilities}
      requireAll={requireAll}
      hideWhenDisabled={hideWhenDisabled}
      fallback={fallback}
      disabledClassName={disabledClassName}
    >
      {children}
    </AuthWithCapability>
  );
}
