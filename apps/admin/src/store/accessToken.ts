import { atom } from 'jotai';
import { atomWithStorage } from 'jotai/utils';

export interface SignInResponse {
  accessToken: string;
  refreshToken: string;
}

export const ACCESS_TOKEN_LOCAL_STORAGE_KEY = 'admin-access-tokens';
export const accessTokenAtom = atomWithStorage<SignInResponse | null>(
  ACCESS_TOKEN_LOCAL_STORAGE_KEY,
  null,
);

export const interceptorInjected = atom(false);

export const clearAccessToken = () => {
  localStorage.removeItem(ACCESS_TOKEN_LOCAL_STORAGE_KEY);
};
