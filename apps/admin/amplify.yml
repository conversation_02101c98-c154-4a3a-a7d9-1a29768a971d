version: 1
applications:
  - frontend:
      phases:
        preBuild:
          commands:
            - npm install -g pnpm
            - pnpm install
            - pnpm -F db generate
            - pnpm -F @willow/auth build
        build:
          commands:
            - pnpm -F @willow/admin build
      artifacts:
        baseDirectory: apps/admin/.next
        files:
          - '**/*'
      cache:
        paths:
          - .next/cache/**/*
          - node_modules/**/*
      buildPath: /
    appRoot: apps/admin
