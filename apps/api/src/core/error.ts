export abstract class BaseError<T = unknown> extends Error {
  abstract readonly code: string;
  abstract readonly statusCode: number;
  readonly meta: T;
  constructor(message: string, ...meta: unknown extends T ? [] : [T]) {
    super(message);
    this.meta = meta[0] as T; // only undefined when T is undefined, so the type cast is safe
  }
}

/** Errors which can be safely displayed to clients should extend this class */
export abstract class SafeBaseError<T = undefined> extends BaseError<T> {}

export abstract class DomainError<T = any> extends SafeBaseError<T> {
  protected constructor(message: string, ...meta: any extends T ? [] : [T]) {
    super(message, ...meta);
  }
}

export class TechnicalError<T = any> extends SafeBaseError<T> {
  code = 'Technical' as const;
  statusCode = 500 as const;
}
