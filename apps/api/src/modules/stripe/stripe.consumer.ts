import { SnsConsume } from '@/modules/shared/aws/sns/sns.decorator';
import { Injectable, Logger } from '@nestjs/common';

import { StripeInvoiceUpdatedQueueEvent } from '../shared/events/invoice-topic.definition';
import {
  StripeChargeUpdatedQueueEvent,
  StripeCustomerUpdatedQueueEvent,
} from '../shared/events/stripe-topic.definition';
import { StripeService } from './service/stripe.service';
import { CustomerDefaultPaymentMethodUpdatedUseCase } from './use-cases/customer-default-payment-method-updated.use-case';
import { CustomerSaveNewPaymentMethodUseCase } from './use-cases/customer-new-payment-method.use-case';
import { TrackChargeRefundedUseCase } from './use-cases/track-charge-refunded.use-case';
import { StripeTrackInvoicePaidUseCase } from './use-cases/track-invoice-paid.use-case';
import { StripeTrackInvoicePaymentFailedUseCase } from './use-cases/track-invoice-payment-failed.use-case';
import { StripeTrackInvoiceUncollectibleUseCase } from './use-cases/track-invoice-uncollectible.use-case';

@Injectable()
export class StripeConsumer {
  private readonly logger = new Logger(StripeConsumer.name);

  constructor(
    private readonly stripe: StripeService,
    private readonly stripeTrackInvoicePaidUseCase: StripeTrackInvoicePaidUseCase,
    private readonly stripeTrackInvoicePaymentFailedUseCase: StripeTrackInvoicePaymentFailedUseCase,
    private readonly stripeTrackInvoiceUncollectibleUseCase: StripeTrackInvoiceUncollectibleUseCase,
    private readonly stripeTrackchargeRefundedUseCase: TrackChargeRefundedUseCase,
    private readonly customerDefaultPaymentMethodUpdatedUseCase: CustomerDefaultPaymentMethodUpdatedUseCase,
    private readonly customerSaveNewPaymentMethodUseCase: CustomerSaveNewPaymentMethodUseCase,
  ) {}

  @SnsConsume({
    topic: 'stripe-invoice-updated',
    consumerGroup: 'collect-new-invoice',
    filter: ['finalized'],
  })
  async attempCollectNewInvoice({ payload }: StripeInvoiceUpdatedQueueEvent) {
    if (payload.event !== 'finalized') return;
    this.logger.log('invoice finialized', payload.invoice.id);
    await this.stripe.attemptInvoiceCollect(payload.invoice.id);
  }

  @SnsConsume({
    topic: 'stripe-customer-updated',
    consumerGroup: 'handle-new-credit-card',
    filter: ['payment-method-attached'],
  })
  async newPaymentMethod({ payload }: StripeCustomerUpdatedQueueEvent) {
    if (payload.event !== 'payment-method-attached') return;

    await this.stripe.setPaymentMethodAsDefault(
      payload.customerId,
      payload.paymentMethod.id,
    );
    await this.customerSaveNewPaymentMethodUseCase.execute(
      payload.patientId,
      payload.paymentMethod,
      { isDefault: true },
    );
  }

  @SnsConsume({
    topic: 'stripe-customer-updated',
    consumerGroup: 'track-default-payment-method',
    filter: ['default-payment-method-updated'],
  })
  async defaultPaymentUpdated({ payload }: StripeCustomerUpdatedQueueEvent) {
    if (payload.event !== 'default-payment-method-updated') return;

    await this.customerDefaultPaymentMethodUpdatedUseCase.execute(
      payload.patientId,
      payload.customer,
    );
  }

  @SnsConsume({
    topic: 'stripe-invoice-updated',
    consumerGroup: 'track-invoice-paid',
    filter: ['paid'],
  })
  async trackInvoicePaid({ payload }: StripeInvoiceUpdatedQueueEvent) {
    if (payload.event !== 'paid') return;

    await this.stripeTrackInvoicePaidUseCase.execute(payload.invoice);
  }

  @SnsConsume({
    topic: 'stripe-invoice-updated',
    consumerGroup: 'track-invoice-payment-failed',
    filter: ['payment_failed'],
  })
  async trackInvoicePaymentFailed({ payload }: StripeInvoiceUpdatedQueueEvent) {
    if (payload.event !== 'payment_failed') return;
    await this.stripeTrackInvoicePaymentFailedUseCase.execute(payload.invoice);
  }

  @SnsConsume({
    topic: 'stripe-invoice-updated',
    consumerGroup: 'track-invoice-uncollectible',
    filter: ['uncollectible', 'voided'],
  })
  async trackInvoiceUncollectible({ payload }: StripeInvoiceUpdatedQueueEvent) {
    if (payload.event !== 'uncollectible' && payload.event !== 'voided') return;
    await this.stripeTrackInvoiceUncollectibleUseCase.execute(payload.invoice);
  }

  @SnsConsume({
    topic: 'stripe-charge-updated',
    consumerGroup: 'track-charge-refunded',
    filter: ['refunded'],
  })
  async trackChargeRefunded({ payload }: StripeChargeUpdatedQueueEvent) {
    if (payload.event !== 'refunded') return;
    await this.stripeTrackchargeRefundedUseCase.execute(payload.charge);
  }
}
