import { PrismaService } from '@/modules/prisma/prisma.service';
import { segmentTrackEvents } from '@/modules/shared/events';
import { SegmentTrack } from '@/modules/shared/types/events';
import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Stripe } from 'stripe';

@Injectable()
export class StripeTrackInvoicePaymentFailedUseCase {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async execute(data: Stripe.Invoice) {
    if (data.object !== 'invoice') {
      console.error('object is not invoice');
      return false;
    }

    const { customer } = data;

    // get patient data
    const patient = await this.prismaService.patient.findUnique({
      where: { stripeCustomerId: String(customer) },
      include: {
        user: true,
      },
    });

    if (!patient) {
      console.error('patient not found');
      return false;
    }

    const doctor = await this.prismaService.doctor.findUnique({
      where: {
        id: patient.doctorId,
      },
      include: {
        user: true,
      },
    });

    const totalValue = 0;

    const patientName = `${patient.user.firstName} ${patient.user.lastName}`;
    const doctorName = `${doctor.user.firstName} ${doctor.user.lastName}`;

    const paymentFailedEvent: SegmentTrack = {
      event: segmentTrackEvents.paymentFailed.name,
      userId: patient.id,
      properties: {
        patientID: patient.id,
        patientName: patientName,
        doctorName: doctorName,
        doctorID: doctor.id,
        stripeInvoiceId: data.id,
        value: totalValue,
      },
    };

    this.eventEmitter.emit(
      segmentTrackEvents.paymentFailed.event,
      paymentFailedEvent,
    );
  }
}
