import type { Stripe } from 'stripe';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { segmentTrackEvents } from '@/modules/shared/events';
import { SegmentTrack } from '@/modules/shared/types/events';
import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class StripeChargeDisputeCreatedUseCase {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly eventEmitter: EventEmitter2,
  ) {}
  async execute(event: Stripe.ChargeDisputeCreatedEvent) {
    console.log('StripeChargeDisputeCreatedUseCase');

    const data = event.data.object as Stripe.Dispute;
    const customerEmail = data.evidence.customer_email_address; // no customer id in the event?
    const patient = await this.prismaService.patient.findFirst({
      where: { user: { email: customerEmail } },
    });

    if (!patient) {
      console.error('ChargeFailedUseCase patient not found');
      return false;
    }
    const chargeDisputeEvent: SegmentTrack = {
      event: segmentTrackEvents.disputeFiled.name,
      userId: patient.id,
      properties: {},
    };
    this.eventEmitter.emit(
      segmentTrackEvents.disputeFiled.event,
      chargeDisputeEvent,
    );
    return true;
  }
}
