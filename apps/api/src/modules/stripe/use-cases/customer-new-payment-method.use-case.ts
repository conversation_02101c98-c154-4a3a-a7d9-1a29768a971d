import { PatientPaymentMethodPersistence } from '@/adapters/persistence/database/patient-payment-method.persistence';
import { runInDbTransaction } from '@/helpers/transaction';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable, Logger } from '@nestjs/common';
import Stripe from 'stripe';

@Injectable()
export class CustomerSaveNewPaymentMethodUseCase {
  private readonly logger = new Logger(
    CustomerSaveNewPaymentMethodUseCase.name,
  );

  constructor(
    private readonly prismaService: PrismaService,
    private readonly patientPaymentMethodPersistence: PatientPaymentMethodPersistence,
  ) {}
  async execute(
    patientId: string,
    paymentMethod: Stripe.PaymentMethod,
    options: { isDefault?: boolean } = {},
  ) {
    if (paymentMethod.type !== 'card') return;

    await runInDbTransaction(this.prismaService, async (prisma) => {
      if (options.isDefault) {
        await prisma.patientPaymentMethod.updateMany({
          where: {
            patientId,
          },
          data: {
            default: false,
          },
        });
      }

      await this.patientPaymentMethodPersistence.create(
        {
          patient: {
            connect: { id: patientId },
          },
          stripeId: paymentMethod.id,
          data: paymentMethod,
          type: paymentMethod.type,
          default: options.isDefault ?? false,
        },
        { prisma },
      );
    });
  }
}
