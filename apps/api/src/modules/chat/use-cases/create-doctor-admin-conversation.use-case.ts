import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';

import { CreateConversationUseCase } from './create-conversation.use-case';

@Injectable()
export class CreateDoctorAdminConversationUseCase {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly createConversationUseCase: CreateConversationUseCase,
  ) {}

  async execute({
    patientId,
    adminUserId,
  }: {
    patientId: string;
    adminUserId?: string;
  }) {
    try {
      // Get patient information
      const patient = await this.prismaService.patient.findUnique({
        where: { id: patientId },
        include: { user: true, doctor: true },
      });

      if (!patient) {
        throw new Error(`Patient not found: ${patientId}`);
      }

      if (!patient.doctor) {
        throw new Error(`Patient must be assigned to a doctor: ${patientId}`);
      }

      const doctorUserId = patient.doctor.userId;

      return this.createConversationUseCase.ensureConversationType({
        conversationType: 'doctorAdmin',
        doctorUserId,
        patientUserId: patientId,
        adminUserId,
      });
    } catch (error) {
      throw new Error(error);
    }
  }
}
