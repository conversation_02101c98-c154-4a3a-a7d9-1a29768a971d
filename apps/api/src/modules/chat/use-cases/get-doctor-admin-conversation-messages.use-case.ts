import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class GetDoctorAdminConversationMessagesUseCase {
  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {}

  async execute(conversationId: string, userId: string) {
    // get conversation with validation: must be doctorAdmin and user must be watcher or assigned
    const conversation = await this.prisma.conversation.findFirst({
      where: {
        id: conversationId,
        type: 'doctorAdmin',
      },
      include: {
        messages: {
          orderBy: { createdAt: 'asc' },
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                type: true,
              },
            },
          },
        },
        watcher: {
          select: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                type: true,
              },
            },
          },
        },
        patient: {
          include: {
            user: true,
            doctor: { include: { user: true } },
          },
        },
        assignedAdmin: {
          select: { id: true, firstName: true, lastName: true },
        },
      },
    });

    // Build participants (admin, doctor, patient)
    // Paciente se obtiene directamente de conversation.patient
    const doctorIds = conversation.watcher
      .filter((w) => w.user.type === 'doctor')
      .map((w) => w.user.id);

    const adminIds = conversation.watcher
      .filter((w) => w.user.type === 'admin')
      .map((w) => w.user.id);

    // Paciente
    const patientUser = conversation.patient?.user;
    const patientId = conversation.patient?.id;
    const patientFacePhoto = conversation.patient?.facePhoto;

    const [doctors, admins] = await Promise.all([
      this.prisma.doctor.findMany({
        where: { userId: { in: doctorIds } },
        select: {
          id: true,
          image: true,
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              type: true,
            },
          },
        },
      }),
      this.prisma.user.findMany({
        where: { id: { in: adminIds }, type: 'admin' },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          type: true,
        },
      }),
    ]);

    const timestamp = new Date().getTime();
    const participants = {
      ...doctors.reduce(
        (acc, curr) => ({
          ...acc,
          [curr.user.id]: {
            id: curr.id,
            user: curr.user,
            imageUrl: `${this.configService.get('NEXT_PUBLIC_API_S3_URL')}/${curr.image}`,
          },
        }),
        {},
      ),
      ...admins.reduce(
        (acc, curr) => ({
          ...acc,
          [curr.id]: {
            id: curr.id,
            user: curr,
            imageUrl: '', // Puedes agregar lógica para imagen de admin si existe
          },
        }),
        {},
      ),
    };

    return {
      ...conversation,
      participants,
    };
  }
}
