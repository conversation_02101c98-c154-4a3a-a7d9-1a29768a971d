import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class GetDoctorAdminConversationsGroupCounts {
  constructor(private readonly prismaService: PrismaService) {}

  async execute(adminUserId) {
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

    const [myInboxResult, allResult, unassignedResult, closedResult] =
      await Promise.all([
        this.prismaService.readReplica().$queryRaw<{ count: number }[]>`
    SELECT COUNT(*) as "count" FROM "Conversation" c
      WHERE c."type" = 'doctorAdmin'
      AND (c."status" = 'open' OR c."status" = 'active')
      AND c."assignedAdminId" = ${adminUserId}
      AND EXISTS (SELECT 1 FROM "ConversationMessage" m WHERE m."conversationId" = c."id")
    `,
        this.prismaService.readReplica().$queryRaw<{ count: number }[]>`
    SELECT COUNT(*) as "count" FROM "Conversation" c
      WHERE c."type" = 'doctorAdmin'
      AND (c."status" = 'open' OR c."status" = 'active')
      AND EXISTS (SELECT 1 FROM "ConversationMessage" m WHERE m."conversationId" = c."id")
    `,
        this.prismaService.readReplica().$queryRaw<{ count: number }[]>`
    SELECT COUNT(*) as "count" FROM "Conversation" c
      WHERE c."type" = 'doctorAdmin'
      AND (c."status" = 'open' OR c."status" = 'active')
      AND c."assignedAdminId" IS NULL
      AND EXISTS (SELECT 1 FROM "ConversationMessage" m WHERE m."conversationId" = c."id")
    `,
        this.prismaService.readReplica().$queryRaw<{ count: number }[]>`
    SELECT COUNT(*) as "count" FROM "Conversation" c
      WHERE c."type" = 'doctorAdmin'
      AND c."status" = 'closed'
      AND c."closedAt" >= ${sevenDaysAgo}
      AND EXISTS (SELECT 1 FROM "ConversationMessage" m WHERE m."conversationId" = c."id")
    `,
      ]);

    return {
      myInbox: myInboxResult[0]?.count ? Number(myInboxResult[0].count) : 0,
      all: allResult[0]?.count ? Number(allResult[0].count) : 0,
      unassigned: unassignedResult[0]?.count
        ? Number(unassignedResult[0].count)
        : 0,
      closed: closedResult[0]?.count ? Number(closedResult[0].count) : 0,
    };
  }
}
