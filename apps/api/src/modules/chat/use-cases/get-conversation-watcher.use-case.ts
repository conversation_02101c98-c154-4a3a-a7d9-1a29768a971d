import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class GetConversationWatcherUseCase {
  constructor(private readonly prismaService: PrismaService) {}

  async execute(conversationId: string, userId: string) {
    return this.prismaService.conversationWatcher.findFirstOrThrow({
      where: {
        conversationId: conversationId,
        userId,
      },
    });
  }
}
