import { runInDbTransaction } from '@/helpers/transaction';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { OrchestrationService } from '@/modules/shared/orchestration/orchestration.service';
import { SqsService } from '@modules/shared/aws/sqs/sqs.service';
import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';

@Injectable()
export class PatientMessageRouterQueue {
  private disabled: boolean = false;

  constructor(
    private readonly prismaService: PrismaService,
    private readonly sqsService: SqsService,
    private readonly orchestrationService: OrchestrationService,
  ) {
    // Check IS_CLI first, if true, disable this queue
    this.disabled =
      process.env.IS_CLI === 'true' ||
      (process.env.ENVIRONMENT !== 'local' &&
        !!process.env.NEXT_PUBLIC_ENVIRONMENT);
  }

  @Cron(CronExpression.EVERY_5_MINUTES) // every 5 minutes
  async processPendingTasks() {
    if (this.disabled) return;

    await this.orchestrationService.runWithLock(
      {
        lockKey: 'patientMessageRouterQueue-cron',
        ttl: 1000 * 60 * 4,
        thisInstanceMustBePrimary: true,
      },
      async () => {
        try {
          await runInDbTransaction(this.prismaService, async (prisma) => {
            //Possible race condition: if a new patient message arrives while we are getting the tasks
            const tasks = await prisma.conversationRouter.findMany({
              where: { delayedUntil: { lte: new Date() }, status: 'pending' },
              orderBy: { delayedUntil: 'asc' },
              take: 20,
            });

            for (const task of tasks) {
              void this.sqsService.sendMessage({
                queueName: 'patient-message-router',
                message: {
                  metadata: {
                    eventId: `transfer-${Date.now()}`,
                    timestamp: new Date().toISOString(),
                  },
                  payload: { conversationRouterId: task.id },
                },
              });
              await prisma.conversationRouter.update({
                where: { id: task.id },
                data: { status: 'processing' },
              });
            }
          });
        } catch (error: any) {
          console.log(
            `Error when running the patient notification Cron: ${error.message}`,
          );
          throw error;
        }
      },
    );
  }
}
