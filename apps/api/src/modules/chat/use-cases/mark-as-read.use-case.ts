import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class MarkAsReadUseCase {
  constructor(private readonly prismaService: PrismaService) {}

  async execute(conversationId: string, userId: string) {
    await this.prismaService.conversationWatcher.updateMany({
      where: {
        conversationId,
        userId,
      },
      data: {
        unreadMessages: 0,
      },
    });

    return null;
  }
}
