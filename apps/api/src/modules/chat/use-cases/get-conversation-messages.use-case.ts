import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class GetConversationMessagesUseCase {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly configService: ConfigService,
  ) {}

  async execute(conversationId: string, userId: string) {
    // Find the user to get their role
    const user = await this.prismaService.user.findUnique({
      where: { id: userId },
      select: { type: true },
    });

    // Determine if the user is a patient
    const isPatient = user?.type === 'patient';

    // get conversation with the patient and doctor for validation
    const conversation = await this.prismaService.conversation.findFirstOrThrow(
      {
        where: {
          id: conversationId,
          OR: [{ watcher: { some: { userId } } }],
        },
        include: {
          messages: {
            orderBy: { createdAt: 'asc' },
            // Filter out doctorNote messages for patients
            ...(isPatient && {
              where: { type: { not: 'doctorNote' } },
            }),
            include: {
              conversationRouter: {
                select: {
                  id: true,
                  relevantForDoctor: true,
                  relevantForPatientServices: true,
                },
              },
            },
          },
          watcher: {
            select: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  type: true,
                },
              },
            },
          },
        },
      },
    );

    const doctorIds = conversation.watcher
      .filter((user) => user.user.type === 'doctor')
      .map((user) => user.user.id);
    const patientIds = conversation.watcher
      .filter((user) => user.user.type === 'patient')
      .map((user) => user.user.id);

    const doctors = await this.prismaService.doctor.findMany({
      where: { userId: { in: doctorIds } },
      select: {
        id: true,
        image: true,
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            type: true,
          },
        },
      },
    });

    const patients = await this.prismaService.patient.findMany({
      where: { userId: { in: patientIds } },
      select: {
        id: true,
        facePhoto: true,
        user: {
          select: { id: true, firstName: true, type: true, lastName: true },
        },
      },
    });
    const timestamp = new Date().getTime();
    const participants = [...doctors, ...patients].reduce(
      (acc, curr) => ({
        ...acc,
        [curr.user.id]: {
          id: curr.id,
          user: {
            id: curr.user.id,
            firstName: curr.user.firstName,
            lastName: curr.user.lastName,
            type: curr.user.type,
          },
          imageUrl:
            curr.user.type === 'doctor'
              ? `${this.configService.get('NEXT_PUBLIC_API_S3_URL')}/${curr['image']}`
              : `${this.configService.get('NEXT_PUBLIC_API_PATIENTS_PHOTOS_URL')}/${
                  (
                    curr as unknown as {
                      facePhoto: string;
                    }
                  ).facePhoto
                }?${timestamp}`,
        } satisfies ChatParticipant,
      }),
      {} satisfies Record<string, ChatParticipant>,
    );

    return {
      ...conversation,
      participants,
    };
  }
}

type ChatParticipant = {
  id: string;
  user: {
    id: string;
    firstName: string;
    lastName: string;
    type: string;
  };
  imageUrl: string;
};
