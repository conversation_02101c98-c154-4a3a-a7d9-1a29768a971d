import { AuthService } from '@modules/auth/auth.service';
import { JwtService } from '@modules/auth/jwt.service';
import { WebsocketAuthMiddleware } from '@modules/auth/middleware/websocket.middleware';
import { ChatEvent } from '@modules/chat/events/chat.events';
import { OnModuleInit } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { WebSocketGateway, WebSocketServer } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';

@WebSocketGateway({ namespace: 'chat', cors: { origin: '*' } })
export class ChatGateway implements OnModuleInit {
  constructor(
    private readonly jwtService: JwtService,
    private readonly authService: AuthService,
  ) {}

  @WebSocketServer()
  server: Server;

  afterInit(client: Socket) {
    client.use(
      WebsocketAuthMiddleware(this.jwtService, this.authService) as any,
    );
  }

  async onModuleInit() {
    if (process.env.IS_CLI !== 'true') {
      this.initializeWebSocket();
    }
  }

  private initializeWebSocket() {
    if (this.server) {
      this.server.on('connection', (socket) => {
        void socket.join((socket as any).user['userId'] as string);
      });
    }
  }

  @OnEvent('chat.message')
  async message(event: ChatEvent) {
    if (this.server) {
      event.recipients.forEach((recipient) =>
        this.server.in(recipient).emit('message', event.message),
      );
    }
  }
}
