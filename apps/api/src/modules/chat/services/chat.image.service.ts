import { S3Service } from '@modules/shared/services/s3.service';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class ChatImageService {
  private imageBucket: string;

  constructor(
    private readonly s3Service: S3Service,
    private readonly configService: ConfigService,
  ) {
    this.imageBucket = this.configService.getOrThrow<string>(
      'AWS_S3_GENERAL_BUCKETNAME',
    );
  }

  async getPutPreSignedUrl(conversationId: string, fileName: string) {
    const key = this.generateTemporalKey(conversationId, fileName);
    const PUTPreSignedURL = await this.s3Service.generatePUTPreSignedURL(
      this.imageBucket,
      key,
      600,
    );
    return {
      key,
      PUTPreSignedURL,
    };
  }

  async moveFileToPermanentStorage(
    conversationId: string,
    messageId: string,
    fileName: string,
  ) {
    //move image to chats/${conversationId}/${messageId}/${fileName}
    const sourceKey: string = this.generateTemporalKey(
      conversationId,
      fileName,
    );
    const destinationKey: string = this.generatePermanentKey(
      conversationId,
      messageId,
      fileName,
    );
    await this.s3Service.moveObject(
      this.imageBucket,
      sourceKey,
      this.imageBucket,
      destinationKey,
    );

    return {
      destinationBucket: this.imageBucket,
      destinationKey,
      region: process.env.AWS_REGION,
    };
  }

  private generateTemporalKey(conversationId: string, fileName: string) {
    return `chats/${conversationId}/_temp/${fileName}`;
  }

  private generatePermanentKey(
    conversationId: string,
    messageId: string,
    fileName: string,
  ) {
    return `chats/${conversationId}/${messageId}/${fileName}`;
  }
}
