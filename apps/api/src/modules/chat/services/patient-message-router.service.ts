import { PrismaService } from '@/modules/prisma/prisma.service';
import { segmentTrackEvents } from '@/modules/shared/events';
import { AiService, BedrockModel } from '@modules/ai/ai.service';
import { IntercomService } from '@modules/intercom/intercom.service';
import { SqsConsume } from '@modules/shared/aws/sqs/sqs.decorator';
import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { addMinutes } from 'date-fns';
import { z } from 'zod';

const validInquiryTypes = [
  'SIDE_EFFECTS',
  'DOSAGE_QUESTION',
  'EFFICACY_CONCERN',
  'SYMPTOM_REPORT',
  'MEDICAL_QUESTION',
  'PRESCRIPTION_RENEWAL',
  'TREATMENT_APPROVAL',
  'SHIPPING_INQUIRY',
  'BILLING_QUESTION',
  'ACCOUNT_ISSUE',
  'SCHEDULING',
  'CONTACT_UPDATE',
  'GENERAL_INQUIRY',
  'ACKNOWLEDGMENT',
  'GREETING',
  'OTHER',
] as const;

const inferredMessageTargetResponseSchema = z.object({
  relevantForDoctor: z.boolean(),
  relevantForPatientServices: z.boolean(),
  reason: z.string(),
  inquiryTypes: z.array(z.enum(validInquiryTypes)),
  relevantMessageIndices: z
    .array(z.number().int().min(0))
    .describe(
      'Indices of ALL messages that were analyzed to make the routing decision (not just patient services relevant ones)',
    ),
  messageSummary: z.string(),
  continuationConfidence: z
    .union([z.number(), z.string()])
    .transform((val) => {
      // Handle both number and string inputs
      const num = typeof val === 'string' ? parseFloat(val) : val;
      return isNaN(num) ? 0 : Math.max(0, Math.min(1, num));
    })
    .describe(
      'Confidence (0-1) that new messages are a continuation of the existing Intercom conversation topic',
    )
    .optional()
    .default(0),
});

export interface MessageData {
  user: string;
  message: string;
  date: string;
  id?: string;
  previousRouting?: {
    conversationRouterId: string;
    status: string;
    relevantForDoctor: boolean;
    relevantForPatientServices: boolean;
    reason: string;
    inquiryTypes: string[];
    intercomId: string | null;
    processedAt: string;
  };
}

interface PatientMessagesResponse {
  messages: MessageData[];
  count: number;
}

export interface ConversationHistoryWithRouting {
  messages: MessageData[];
  previousRoutings: Array<{
    id: string;
    status: string;
    relevantForDoctor: boolean;
    relevantForPatientServices: boolean;
    reason: string;
    inquiryTypes: string[];
    intercomId: string | null;
    processedAt: Date;
    messageIds: string[];
  }>;
  hasActiveIntercomConversation: boolean;
  lastIntercomId: string | null;
}

// Define all possible queue topics including those not in the QueueEvent type
type AllQueueTopics =
  | 'patient-updated'
  | 'treatment-updated'
  | 'follow-up-updated'
  | 'invoice-updated'
  | 'subscription-updated'
  | 'charge-updated'
  | 'patient-message-router'
  | 'patient-transfers';

const topic = (name: AllQueueTopics) => name as any;

@Injectable()
export class PatientMessageRouterService {
  private readonly logger = new Logger(PatientMessageRouterService.name);

  constructor(
    private readonly prismaService: PrismaService,
    private readonly aiService: AiService,
    private readonly intercomService: IntercomService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  @SqsConsume(topic('patient-message-router'), {
    batchSize: 1,
    maxRetries: 3,
    deadLetterQueueName: 'patient-message-router-dlq',
  })
  async handleSqsMessage(message: any) {
    const conversationRouterId = message.payload.conversationRouterId;
    const conversationRouter =
      await this.prismaService.conversationRouter.findFirst({
        where: { id: conversationRouterId },
        include: {
          conversation: {
            include: {
              patient: {
                include: { doctor: { include: { user: true } }, user: true },
              },
            },
          },
        },
      });

    // Get full conversation history with routing metadata
    const conversationHistory = await this.getConversationHistoryWithRouting(
      conversationRouter.conversation.id,
    );

    // Analyze the conversation with full context
    // Pass the specific message IDs that need to be analyzed
    const messagesTarget = await this.analyzePatientMessages(
      conversationHistory,
      conversationRouter.messages as string[],
    );

    // Get only NEW patient messages that haven't been processed
    const newPatientMessages = conversationHistory.messages.filter(
      (msg) =>
        msg.user === 'patient' && !msg.previousRouting && msg.id !== undefined,
    );
    const patientMessageIds = newPatientMessages.map((msg) => msg.id as string);

    let intercomId = null;
    if (messagesTarget.relevantForPatientServices) {
      const user = conversationRouter.conversation.patient.user;

      // For Intercom, we only want to forward the actual patient messages (not all analyzed messages)
      // Since relevantMessageIndices now includes ALL analyzed messages, we just use the new patient messages
      let messageContent = newPatientMessages
        .map((msg) => msg.message)
        .join('\n\n');

      // Get the doctor's last name
      const doctorLastName =
        conversationRouter.conversation.patient.doctor?.user?.lastName ||
        'the doctor';

      // Prepare content for Intercom with summary at the top if it exists and is relevant
      if (messagesTarget.messageSummary) {
        // Different forwarded message based on relevance
        const forwardedText = messagesTarget.relevantForDoctor
          ? `[ Forwarded to/from Dr. ${doctorLastName} ]`
          : `[ Forwarded from Dr. ${doctorLastName} ]`;

        messageContent = `${messagesTarget.messageSummary}\n\n${forwardedText}\n\nOriginal Message:\n${messageContent}`;
      } else if (
        conversationHistory.hasActiveIntercomConversation &&
        conversationHistory.lastIntercomId
      ) {
        // When appending to existing conversation, add minimal indication
        const timestamp = new Date().toLocaleString('en-US', {
          timeZone: 'America/New_York',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
        });
        messageContent = `[ New message from app - ${timestamp} ]\n\n${messageContent}`;
      }

      // Check if we should append to existing conversation or create a new one
      const continuationThreshold = 0.7; // Confidence threshold for appending
      const confidence = Number(messagesTarget.continuationConfidence || 0);
      const shouldAppend =
        conversationHistory.hasActiveIntercomConversation &&
        conversationHistory.lastIntercomId &&
        confidence >= continuationThreshold;

      if (shouldAppend) {
        try {
          this.logger.log(
            `Appending message to existing Intercom conversation ${conversationHistory.lastIntercomId} (confidence: ${messagesTarget.continuationConfidence})`,
          );
          await this.intercomService.addMessageToConversation(
            conversationHistory.lastIntercomId,
            user,
            messageContent,
          );
          // Keep the existing intercom ID since we're appending to the same conversation
          intercomId = conversationHistory.lastIntercomId;
        } catch (error) {
          // If appending fails (e.g., conversation was closed), create a new conversation
          this.logger.warn(
            `Failed to append to existing conversation ${conversationHistory.lastIntercomId}, creating new conversation: ${error.message}`,
          );
          const result = await this.intercomService.createConversation(
            user,
            messageContent,
          );
          intercomId = result?.message?.conversation_id || null;
        }
      } else {
        // No active conversation, low confidence, or create a new one
        if (conversationHistory.hasActiveIntercomConversation) {
          this.logger.log(
            `Creating new Intercom conversation instead of appending to ${conversationHistory.lastIntercomId} (confidence: ${messagesTarget.continuationConfidence}, threshold: ${continuationThreshold})`,
          );
        }
        const result = await this.intercomService.createConversation(
          user,
          messageContent,
        );
        intercomId = result?.message?.conversation_id || null;
      }
    }
    if (messagesTarget.relevantForDoctor) {
      const doctorUserId: string =
        conversationRouter.conversation.patient.doctor.userId;
      // Create a PatientMessagesResponse for the doctor watcher update
      const patientMessagesForDoctor: PatientMessagesResponse = {
        messages: newPatientMessages,
        count: newPatientMessages.length,
      };

      await this.updateDoctorConversationWatchers(
        doctorUserId,
        conversationRouter.conversation.id,
        patientMessagesForDoctor,
      );
    }

    // Update the conversation router record
    await this.prismaService.conversationRouter.update({
      where: { id: conversationRouterId },
      data: {
        status: 'processed',
        intercomId,
        messages: patientMessageIds,
        reason: messagesTarget.reason,
        relevantForPatientServices: messagesTarget.relevantForPatientServices,
        relevantForDoctor: messagesTarget.relevantForDoctor,
        inquiryTypes: messagesTarget.inquiryTypes,
      },
    });

    // Emit message routed tracking event
    const doctor = conversationRouter.conversation.patient.doctor;
    const doctorName = doctor?.user
      ? `${doctor.user.firstName} ${doctor.user.lastName}`
      : 'Unknown Doctor';

    // Determine routing result
    let result = 'Doctor (No Action)';
    if (
      messagesTarget.relevantForDoctor &&
      messagesTarget.relevantForPatientServices
    ) {
      result = 'Both';
    } else if (messagesTarget.relevantForDoctor) {
      result = 'Doctor';
    } else if (messagesTarget.relevantForPatientServices) {
      result = 'Patient Services';
    }

    this.eventEmitter.emit(segmentTrackEvents.messageRouted.event, {
      event: segmentTrackEvents.messageRouted.name,
      userId: conversationRouter.conversation.patient.userId,
      properties: {
        result,
        doctorName,
        inquiryType: messagesTarget.inquiryTypes.join(', ') || 'UNKNOWN',
        reason: messagesTarget.reason,
      },
    });

    // Get the actual message IDs for the relevant messages
    // The relevantMessageIndices from the LLM response directly correspond to the indices
    // in the lastPatientMessages.messages array that we sent to the LLM
    if (messagesTarget.relevantMessageIndices.length > 0) {
      // Extract the message IDs directly from the messages array using the indices
      const relevantMessageIds = messagesTarget.relevantMessageIndices
        .filter(
          (index) => index >= 0 && index < conversationHistory.messages.length,
        )
        .map((index) => conversationHistory.messages[index].id)
        .filter((id) => id !== undefined) as string[];

      if (relevantMessageIds.length === 0) {
        this.logger.log('No valid relevant message IDs found');
        return;
      }

      // Update the conversation messages with the conversation router ID
      await this.prismaService.conversationMessage.updateMany({
        where: {
          id: { in: relevantMessageIds },
        },
        data: {
          conversationRouterId: conversationRouterId,
        },
      });

      // Log if we couldn't find IDs for some indices
      const missingIndices = messagesTarget.relevantMessageIndices.filter(
        (index) =>
          index >= 0 &&
          index < conversationHistory.messages.length &&
          !conversationHistory.messages[index].id,
      );

      if (missingIndices.length > 0) {
        this.logger.warn(
          `Missing IDs for message indices: ${missingIndices.join(', ')}`,
        );
      }
    }
  }

  async flagForProcessing(conversationId: string) {
    const routerUpdate = await this.prismaService.conversationRouter.updateMany(
      {
        where: { conversationId: conversationId, status: 'pending' },
        data: { delayedUntil: addMinutes(new Date(), 5) },
      },
    );

    if (routerUpdate.count > 0) return;
    await this.prismaService.conversationRouter.create({
      data: {
        conversationId: conversationId as string,
        status: 'pending',
        delayedUntil: addMinutes(new Date(), 5),
      },
    });
  }

  /**
   * Marks a conversation router as closed when an Intercom conversation is closed
   * @param intercomId The Intercom conversation ID
   * @returns The updated conversation router record, or null if not found
   */
  async markConversationRouterAsClosed(intercomId: string) {
    this.logger.log(
      `Marking conversation router as closed for Intercom ID: ${intercomId}`,
    );

    try {
      // Find the conversation router with the given Intercom ID
      const conversationRouter =
        await this.prismaService.conversationRouter.findFirst({
          where: { intercomId },
        });

      if (!conversationRouter) {
        this.logger.warn(
          `No conversation router found with Intercom ID: ${intercomId}`,
        );
        return null;
      }

      // Update the conversation router status to 'closed'
      const updatedRouter = await this.prismaService.conversationRouter.update({
        where: { id: conversationRouter.id },
        data: { status: 'closed' },
      });

      this.logger.log(
        `Successfully marked conversation router ${updatedRouter.id} as closed`,
      );
      return updatedRouter;
    } catch (error) {
      this.logger.error(
        `Error marking conversation router as closed for Intercom ID ${intercomId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Reopens a closed conversation router when an Intercom conversation is reopened
   * Only changes the status if the current status is 'closed'
   * @param intercomId The Intercom conversation ID
   * @returns The updated conversation router record, or null if not found or not closed
   */
  async reopenClosedConversationRouter(intercomId: string) {
    this.logger.log(
      `Reopening closed conversation router for Intercom ID: ${intercomId}`,
    );

    try {
      // Find the conversation router with the given Intercom ID
      const conversationRouter =
        await this.prismaService.conversationRouter.findFirst({
          where: { intercomId },
        });

      if (!conversationRouter) {
        this.logger.warn(
          `No conversation router found with Intercom ID: ${intercomId}`,
        );
        return null;
      }

      // Only update if the current status is 'closed'
      if (conversationRouter.status !== 'closed') {
        this.logger.warn(
          `Conversation router ${conversationRouter.id} is not closed (current status: ${conversationRouter.status}), skipping reopen`,
        );
        return conversationRouter;
      }

      // Update the conversation router status to 'processed'
      const updatedRouter = await this.prismaService.conversationRouter.update({
        where: { id: conversationRouter.id },
        data: { status: 'processed' },
      });

      this.logger.log(
        `Successfully reopened conversation router ${updatedRouter.id} (changed status from 'closed' to 'processed')`,
      );
      return updatedRouter;
    } catch (error) {
      this.logger.error(
        `Error reopening conversation router for Intercom ID ${intercomId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  public async analyzePatientMessages(
    conversationHistory: ConversationHistoryWithRouting,
    messageIdsToAnalyze?: string[],
    model?: BedrockModel,
  ) {
    const systemPrompt = `You are an AI assistant specialized in routing patient messages in a healthcare platform.

CORE RESPONSIBILITY:
Analyze patient-doctor conversations to determine if messages should be routed to:
1. The Doctor (medical concerns)
2. Patient Services (administrative issues)
3. Both departments

CRITICAL RULES:
- Patient safety is paramount - when uncertain, classify as medical
- Consider previously ran analysis to avoid duplicate tickets if routing to patient services
- Build upon existing summaries rather than recreating them
- All patient responses to doctor questions are relevant to the doctor, regardless of content

ROUTING AWARENESS:
- Messages may have been previously analyzed and routed
- If an active Intercom conversation _open_ exists, consider appending to it rather than creating duplicates
- Focus on NEW patient messages that haven't been processed yet
- Assess whether new messages are continuations of existing topics

CONTINUATION DETECTION:
When the exact previous Routing job was for patient services evaluate:
- Topic similarity between previous summary and new messages
- Time proximity (messages within 24-48 hours more likely to be related)
- Direct references to previous discussion
- Similar inquiry types as previous routing

Set continuationConfidence (0-1):
- 0.8-1.0: Strong continuation (same topic, direct references)
- 0.5-0.7: Likely continuation (related topic, same category)
- 0.2-0.4: Possible continuation (same general area)
- 0-0.2: New topic (unrelated to previous discussion)

RESPONSE FORMAT:
You must provide a structured JSON response with specific fields as defined in the schema.
`;

    // Move detailed classification criteria to user prompt
    const classificationGuidelines = `
    ## Medical Issues (Relevant for Doctor):
    - Questions or concerns about medical treatment or medication
    - Reports of side effects or symptoms
    - Questions about dosage adjustments or side effects
    - Medical advice requests
    - Progress updates on treatment efficacy
    - New health concerns that may relate to treatment
    - Any mention of how the patient is feeling physically
    - Direct responses to doctor's questions or confirmations of any kind
    - Requests for additional refills (beyond simple administrative refills)
    - Any questions relating to dosage or side effects of their treatment
    - Patient would like to restore their account
    - Patient would like to continue or stop or change their treatment
    - Patient has updated their payment method
    - Patient requests to pause medication refill and 
    - Patient seeking guidance on maintenance dosage after achieving weight goal
    - Patient confirms upcoming medication shipment will include new dosage and plans to provide feedback
    - Patient approves dosage increase as proposed by doctor, requiring administrative processing for new prescription and billing
    - Patient requests to expedite medication refill processing due to travel plans, confirming urgency and need for timely delivery

    ## Administrative Issues (Relevant for Patient Services):
    - Scheduling or appointment logistics ONLY
    - Medication shipping or delivery inquiries WITHOUT medical concerns
    - Billing, insurance, or payment questions
    - Address or contact information changes
    - Technical issues with patient portal or apps
    - Simple refill requests with NO medical concerns mentioned
    - General service inquiries
    - Pharmacy destinations or prescription transfers to different pharmacies

    ## Neither Category:
    - Simple acknowledgments (e.g., "Thank you", "OK")
    - Basic greetings without substance
    - Empty messages or purely social exchanges
    - Messages with no actionable content

    # Strong Indicators:

    ## Strong Medical Indicators:
    Keywords: pain, symptoms, side effects, bleeding, reaction, feel, worse, better, dizzy, nausea, concern, worried, dosage, dose, restore, account, continue, stop, change, treatment, payment, updated
    Phrases: "is it normal", "should I be concerned", "when will it work", "I'm experiencing", "since I started", "yes doctor", "confirmed", "I understand", "additional refill", "restore my account", "continue treatment", "stop treatment", "change treatment", "updated payment", "payment method"

    ## Strong Administrative Indicators:
    Keywords: tracking, invoice, portal, password, delivery, address, ship, billing, payment, insurance, pharmacy, transfer
    Phrases: "hasn't arrived", "billing department", "update my information", "track my package", "login issues", "send to pharmacy", "different pharmacy", "transfer prescription"

    # Common Edge Cases:
    - Prescription refills: If ONLY requesting a refill → Administrative. If mentioning ANY symptoms, side effects, or dosage concerns → Medical. Additional refills beyond standard → Medical.
    - "Not working" complaints: If about medication efficacy → Medical. If about website/portal → Administrative.
    - Shipping delays affecting medication: Both categories (patient needs medication AND shipping help)
    - Treatment approval with logistics: Both categories (medical decision AND administrative action)
    - Direct responses to doctor questions: Always Medical (even simple "yes" or "I confirm")
    - Pharmacy transfers: If ONLY about sending to different pharmacy → Administrative. If includes medical questions → Both.
    - Dosage questions: Always Medical, regardless of context

    # Examples:

    ## Example 1: MEDICAL ONLY
    "I've been taking the medication for 3 days and I'm getting headaches"
    → "Headaches can be a side effect. Try taking it with food"
    Result: relevantForDoctor: true, relevantForPatientServices: false
    Reason: Patient reporting side effects requires medical attention

    ## Example 2: ADMINISTRATIVE ONLY
    "My medication hasn't arrived yet. Can you check the tracking number?"
    Result: relevantForDoctor: false, relevantForPatientServices: true
    Reason: Pure shipping inquiry with no medical concerns

    ## Example 3: BOTH CATEGORIES
    "I'm ready to start the treatment the doctor recommended, but I need it shipped to my new address at 123 Main St?"
    Result: relevantForDoctor: true, relevantForPatientServices: true
    Reason: Contains treatment approval (medical) and address update (administrative)

    ## Example 4: MEDICAL (Disguised as Administrative)
    "I need a refill of my medication. I've been feeling more anxious lately and running out"
    Result: relevantForDoctor: true, relevantForPatientServices: false
    Reason: Refill request includes symptom report (anxiety) requiring medical review

    ## Example 5: MEDICAL (Doctor Response)
    "Have you been taking the medication as prescribed?"
    → "Yes, I have"
    Result: relevantForDoctor: true, relevantForPatientServices: false
    Reason: Direct response to doctor's question requires medical attention

    ## Example 6: ADMINISTRATIVE (Pharmacy Transfer)
    "Can you send my prescription to CVS on Main Street instead of Walgreens?"
    Result: relevantForDoctor: false, relevantForPatientServices: true
    Reason: Pure pharmacy transfer request with no medical concerns

    ## Example 7: MEDICAL (Dosage Question)
    "Should I take the medication with food or on an empty stomach?"
    Result: relevantForDoctor: true, relevantForPatientServices: false
    Reason: Question about dosage and medication administration requires medical guidance

    ## Example 8: MEDICAL (Account Restoration)
    [
      {"user": "patient", "message": "I'd like to restore my account. I stopped the treatment a few months ago but I'm ready to start again", "date": "2024-01-15T10:00:00Z"}
    ]
    Result: relevantForDoctor: true, relevantForPatientServices: false
    Reason: Account restoration involves resuming medical treatment which requires doctor review

    ## Example 9: MEDICAL (Treatment Change)
    [
      {"user": "patient", "message": "I want to stop my current treatment", "date": "2024-01-15T10:00:00Z"}
    ]
    Result: relevantForDoctor: true, relevantForPatientServices: false
    Reason: Stopping or changing treatment is a medical decision requiring doctor oversight

    ## Example 10: MEDICAL (Payment Update)
    [
      {"user": "patient", "message": "I've updated my payment method. Can we continue with my treatment now?", "date": "2024-01-15T10:00:00Z"}
    ]
    Result: relevantForDoctor: true, relevantForPatientServices: false
    Reason: Payment updates that relate to continuing treatment require medical review

    # Message Analysis Instructions:

    You will analyze a JSON array of messages with this structure:
    [
      {
        "user": "patient" or "doctor",
        "message": "the message content",
        "date": "ISO date string"
      }
    ]

      ## Example 11: MEDICAL (Pharmacy Information)
    Original Message:
    Savon pharmacy
    2000 Marcolla Rd
    Springfield, OR
    97477
    Result: relevantForDoctor: true, relevantForPatientServices: false
    Reason: The patient is providing pharmacy information for Savon pharmacy located at 2000 Marcolla Rd, Springfield, OR 97477, requesting their prescription to be sent to this local pharmacy location.

    ## Example 12: MEDICAL (Shipment Quantity Request)
    Original Message:
    Could you send 2 vials this next shipment please?
    Just checking on possibility of sending 2 vials
    Result: relevantForDoctor: true, relevantForPatientServices: false
    Reason: The patient is requesting 2 vials to be sent in their next shipment and following up to check on the possibility of this request being fulfilled.

    ## Example 13: MEDICAL (Supply Preference)
    Original Message:
    Thank you. Received today. Also I don’t need anymore syringes. (For future reference) I have plenty!
    Result: relevantForDoctor: true, relevantForPatientServices: false
    Reason: The patient is confirming receipt of their medication and requesting that syringes not be included in future shipments as they have plenty in stock. This is a supply preference update for their account.

    ## Example 14: MEDICAL (Pharmacy Change Due to Issues)
    Original Message:
    Hello - are you able to send me my prescription? I’m have issues with orders from Willow and want to try a different pharmacy.
    Result: relevantForDoctor: true, relevantForPatientServices: false
    Reason: The patient is requesting to have their prescription sent to a different pharmacy because they are experiencing issues with orders from their current pharmacy and want to try an alternative pharmacy for fulfillment.

    ## Example 15: MEDICAL (Equipment Request)
    Original Message:
    Thank you, Can you please make sure that the syringe and stopper fit the bottle? It's tough to access the medication without this.
    Result: relevantForDoctor: true, relevantForPatientServices: false
    Reason: The patient is requesting that the correct syringe and stopper be included with their upcoming Semaglutide refill shipment, as they are having difficulty accessing the medication without the proper equipment.

    ## Example 16: MEDICAL (Patient Confirmation)
    Original Message:
    Yes, I am. Thank you!!
    Result: relevantForDoctor: true, relevantForPatientServices: false
    Reason: The patient is confirming they are a current Winona patient in response to the doctor's inquiry about setting something up for them.

    Consider:
    - Message sender (patient vs doctor) and conversation flow
    - Time gaps: >48 hours may indicate new topic
    - Recent messages (last 72 hours) carry more weight
    - Focus on the most recent patient inquiry

    # Inquiry Type Classification:

    ## Medical Inquiry Types:
    - "SIDE_EFFECTS": Patient reporting side effects or adverse reactions
    - "DOSAGE_QUESTION": Questions about medication dosage or adjustments
    - "EFFICACY_CONCERN": Concerns about whether the treatment is working
    - "SYMPTOM_REPORT": Reporting new or ongoing symptoms
    - "MEDICAL_QUESTION": General medical questions about the treatment
    - "PRESCRIPTION_RENEWAL": Requests for prescription renewal
    - "TREATMENT_APPROVAL": Patient approving or requesting to proceed with treatment

    ## Administrative Inquiry Types:
    - "SHIPPING_INQUIRY": Questions about medication shipping or delivery
    - "BILLING_QUESTION": Questions about billing, payments, or insurance
    - "ACCOUNT_ISSUE": Problems with patient portal or account access
    - "SCHEDULING": Appointment scheduling or changes
    - "CONTACT_UPDATE": Updates to contact information or address
    - "GENERAL_INQUIRY": General administrative questions

    ## Other Types:
    - "ACKNOWLEDGMENT": Simple acknowledgments or thank you messages
    - "GREETING": Basic greetings without substantive content
    - "OTHER": Any inquiry that doesn't fit the above categories
    
    DO NOT create new inquiry types. Use only the exact values listed above.

    # Response Requirements:

    For relevantMessageIndices:
    - Return the EXACT indices of messages you analyzed (as specified in "MESSAGES TO ANALYZE" section)
    - These indices correspond to the NEW messages that triggered this routing job
    - Use 0-based indexing into the full conversation history
    - This should match the indices provided in "MESSAGES TO ANALYZE" section
    - Include these indices regardless of routing decision (doctor, patient services, both, or neither)

    For messageSummary:
    - Write in third person ("The patient is asking about...")
    - If relevant for patient services, be sure to be descriptive and don't lose any of the information intent from the patient.
    - If relevant for doctor, write a concise summary of the patient's question, unless it's just a simple greeting or acknowledgment.
    - If relevant for both, mix the previous two points, patient services first.
    - If not relevant for either, return an empty string.

    REMEMBER: When uncertain, classify as medical. Patient safety is the top priority.`;

    // Build the comprehensive user prompt
    const buildUserPrompt = (
      history: ConversationHistoryWithRouting,
      targetMessageIds?: string[],
    ) => {
      // Identify which messages to analyze
      let messagesToAnalyze: number[] = [];
      if (targetMessageIds && targetMessageIds.length > 0) {
        // Find indices of the specific messages to analyze
        messagesToAnalyze = targetMessageIds
          .map((id) => history.messages.findIndex((msg) => msg.id === id))
          .filter((index) => index !== -1);
      } else {
        // Fallback: analyze unprocessed messages
        messagesToAnalyze = history.messages
          .map((msg, index) => (!msg.previousRouting ? index : -1))
          .filter((index) => index !== -1);
      }

      // If no messages to analyze, this is an error condition
      if (messagesToAnalyze.length === 0) {
        throw new Error(
          'No messages to analyze - routing should not have been triggered',
        );
      }

      // Find the earliest message to analyze to determine history cutoff
      const earliestAnalyzeIndex = Math.min(...messagesToAnalyze);

      // Split conversation into history (before messages to analyze) and messages to analyze
      const conversationHistoryUpToAnalysis = history.messages.slice(
        0,
        earliestAnalyzeIndex,
      );
      const messagesToAnalyzeContent = messagesToAnalyze.map(
        (index) => history.messages[index],
      );

      return `
CONVERSATION CONTEXT:
${history.hasActiveIntercomConversation ? `⚠️ ACTIVE INTERCOM CONVERSATION EXISTS (ID: ${history.lastIntercomId}) - Append to existing ticket if relevant` : 'No active Intercom conversation'}

PREVIOUS ROUTING SUMMARIES:
${
  history.previousRoutings.length > 0
    ? history.previousRoutings
        .map(
          (routing) =>
            `- ${routing.processedAt.toISOString()}: ${routing.reason} (Doctor: ${routing.relevantForDoctor}, Patient Services: ${routing.relevantForPatientServices})`,
        )
        .join('\n')
    : 'No previous routings'
}

CONVERSATION HISTORY (UP TO NEW MESSAGES):
${
  conversationHistoryUpToAnalysis.length > 0
    ? JSON.stringify(conversationHistoryUpToAnalysis, null, 2)
    : 'No previous conversation history'
}

MESSAGES TO ANALYZE:
${JSON.stringify(messagesToAnalyzeContent, null, 2)}

ANALYSIS FOCUS:
- Total messages in conversation: ${history.messages.length}
- Historical context messages: ${conversationHistoryUpToAnalysis.length}
- New messages to analyze: ${messagesToAnalyze.length}
- Message indices being analyzed: [${messagesToAnalyze.join(', ')}]

${classificationGuidelines}

Please analyze this conversation, focusing ONLY on the messages at the specified indices above. These are the NEW messages that need routing decisions. Use the full conversation history for context, but your routing decision should be based on analyzing these specific messages.

For relevantMessageIndices in your response, include the indices of the messages you analyzed (should match the "MESSAGES TO ANALYZE" indices above).

${
  history.hasActiveIntercomConversation
    ? `
IMPORTANT: There is an active Intercom conversation (ID: ${history.lastIntercomId}). 
Carefully evaluate the continuationConfidence score:
- Compare the new patient messages with the previous adjacent routing summary, if exists.
- Consider if this is a follow-up question, clarification, or entirely new topic
- Messages within 24-48 hours about the same issue should have high confidence (0.7+)
- New unrelated issues should have low confidence (<0.7) to create a new ticket
`
    : ''
}`;
    };

    const prompt = buildUserPrompt(conversationHistory, messageIdsToAnalyze);

    return await this.aiService.structuredPromptWithToolUse(
      systemPrompt,
      prompt,
      inferredMessageTargetResponseSchema,
      0,
      model,
    );
  }

  /**
   * Retrieves full conversation history with routing metadata
   * This provides complete context including previous routing decisions
   */
  public async getConversationHistoryWithRouting(
    conversationId: string,
  ): Promise<ConversationHistoryWithRouting> {
    // Get all messages from the conversation
    const messages = await this.prismaService.conversationMessage.findMany({
      where: { conversationId },
      orderBy: { createdAt: 'asc' },
      include: {
        user: true,
        conversationRouter: true,
      },
    });

    // Get all conversation routers for this conversation
    const conversationRouters =
      await this.prismaService.conversationRouter.findMany({
        where: {
          conversationId,
          status: { in: ['processed', 'closed'] },
        },
        orderBy: { createdAt: 'asc' },
      });

    // Create a map of message IDs to their routing info
    const messageRoutingMap = new Map<
      string,
      (typeof conversationRouters)[0]
    >();

    for (const router of conversationRouters) {
      if (router.messages && Array.isArray(router.messages)) {
        for (const messageId of router.messages as string[]) {
          messageRoutingMap.set(messageId, router);
        }
      }
    }

    // Format messages with routing metadata
    const formattedMessages: MessageData[] = messages.map((message) => {
      const routing = messageRoutingMap.get(message.id);

      return {
        user: message.user?.type || 'unknown',
        message: message.content,
        date: message.createdAt.toISOString(),
        id: message.id,
        ...(routing && {
          previousRouting: {
            conversationRouterId: routing.id,
            status: routing.status,
            relevantForDoctor: routing.relevantForDoctor || false,
            relevantForPatientServices:
              routing.relevantForPatientServices || false,
            reason: routing.reason || '',
            inquiryTypes: (routing.inquiryTypes as string[]) || [],
            intercomId: routing.intercomId,
            processedAt: routing.createdAt.toISOString(),
          },
        }),
      };
    });

    // Check for active Intercom conversation - should be the last (most recent) router
    const lastRouter = conversationRouters[conversationRouters.length - 1];
    const activeIntercomRouter =
      lastRouter?.intercomId && lastRouter?.status !== 'closed'
        ? lastRouter
        : null;

    return {
      messages: formattedMessages,
      previousRoutings: conversationRouters.map((router) => ({
        id: router.id,
        status: router.status,
        relevantForDoctor: router.relevantForDoctor || false,
        relevantForPatientServices: router.relevantForPatientServices || false,
        reason: router.reason || '',
        inquiryTypes: (router.inquiryTypes as string[]) || [],
        intercomId: router.intercomId,
        processedAt: router.createdAt,
        messageIds: (router.messages as string[]) || [],
      })),
      hasActiveIntercomConversation: !!activeIntercomRouter,
      lastIntercomId: activeIntercomRouter?.intercomId || null,
    };
  }

  private async updateDoctorConversationWatchers(
    doctorUserId: string,
    conversationId: string,
    lastPatientMessages: PatientMessagesResponse,
  ) {
    await this.prismaService.conversationWatcher.updateMany({
      where: {
        userId: doctorUserId,
        conversationId: conversationId,
      },
      data: {
        updatedAt: new Date(),
        unreadMessages: { increment: lastPatientMessages.count },
      },
    });
  }
}
