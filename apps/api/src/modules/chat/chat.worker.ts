import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';

import { OrchestrationService } from '../shared/orchestration/orchestration.service';
import { CreateConversationUseCase } from './use-cases/create-conversation.use-case';

@Injectable()
export class ChatWorker {
  private readonly logger = new Logger(ChatWorker.name);
  private readonly disabled: boolean;

  constructor(
    private readonly prisma: PrismaService,
    private readonly orchestrationService: OrchestrationService,
    private readonly createConversationUseCase: CreateConversationUseCase,
  ) {
    this.disabled =
      process.env.IS_CLI === 'true' ||
      (process.env.ENVIRONMENT !== 'local' &&
        !!process.env.NEXT_PUBLIC_ENVIRONMENT);
  }

  @Cron(CronExpression.EVERY_5_MINUTES)
  async fixMissingConversationsCron() {
    if (this.disabled) return;

    try {
      await this.orchestrationService.runWithLock(
        {
          lockKey: 'fixMissingConversationsCron-cron',
          ttl: 1000 * 60 * 4, // 5 minutes
          thisInstanceMustBePrimary: true,
        },
        async () => {
          await this.fixMissingConversations();
        },
      );
    } catch (error) {
      this.logger.error('Error in fixMissingConversationsCron', error);
    }
  }

  async fixMissingConversations() {
    const patients = await this.prisma.patient.findMany({
      where: {
        doctorId: { not: null },
        status: { notIn: ['cancelled', 'deleted'] },
        OR: [
          // 1. no conversation of type patientDoctor
          { conversations: { none: { type: 'patientDoctor' } } },
          // 2. has conversation of patientDoctor but some watcher (doctor or patient) is missing
          {
            conversations: {
              some: {
                type: 'patientDoctor',
                watcher: {
                  none: {
                    OR: [
                      { user: { doctor: { isNot: null } } },
                      { user: { patient: { isNot: null } } },
                    ],
                  },
                },
              },
            },
          },
        ],
      },
      include: {
        user: true,
        doctor: true,
        conversations: {
          include: {
            watcher: true,
          },
        },
      },
    });

    this.logger.debug(
      `Found ${patients.length} patients with missing patientDoctor conversation`,
    );

    for (const patient of patients) {
      this.logger.debug(
        `Fixing patient ${patient.user?.email ?? patient.userId} status ${patient.status}`,
      );

      await this.createConversationUseCase.ensureConversationType({
        conversationType: 'patientDoctor',
        doctorUserId: patient.doctorId,
        patientUserId: patient.userId,
      });
    }
  }
}
