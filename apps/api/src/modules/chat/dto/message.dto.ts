import { ConversationMessageContentType } from '@prisma/client';
import { Transform } from 'class-transformer';
import { IsIn, IsNotEmpty, IsOptional } from 'class-validator';

export class MessageDto {
  @IsNotEmpty()
  @IsIn(['text', 'image', 'file'])
  contentType: ConversationMessageContentType;

  @IsNotEmpty()
  @Transform(({ value }) => value.trim())
  content: string;

  @IsOptional()
  needsReply: boolean;
}
