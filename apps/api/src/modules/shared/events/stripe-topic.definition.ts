import Stripe from 'stripe';

import { BaseEvent, BaseMessage } from './base.definition';

export type StripeChargeUpdatedEvent = BaseEvent<
  'refunded' | 'disputed',
  { patientId: string; charge: Stripe.Charge }
>;

export type StripeChargeUpdatedQueueEvent = BaseMessage<
  'stripe-charge-updated',
  StripeChargeUpdatedEvent
>;

export type StripeCustomerUpdatedEvent =
  | BaseEvent<
      'payment-method-attached',
      {
        patientId: string;
        customerId: string;
        paymentMethod: Stripe.PaymentMethod;
      }
    >
  | BaseEvent<
      'default-payment-method-updated',
      {
        patientId: string;
        customerId: string;
        customer: Stripe.Customer;
      }
    >;

export type StripeCustomerUpdatedQueueEvent = BaseMessage<
  'stripe-customer-updated',
  StripeCustomerUpdatedEvent
>;
