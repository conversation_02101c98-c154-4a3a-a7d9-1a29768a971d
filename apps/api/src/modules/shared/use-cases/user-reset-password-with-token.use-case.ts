import { UserResetPasswordWithTokenDto } from '@/modules/shared/dto/user-reset-password-with-token.dto';
import { AuditService } from '@modules/audit-log/audit-log.service';
import { CognitoService } from '@modules/auth/cognito.service';
import { PrismaService } from '@modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class UserResetPasswordWithTokenUseCase {
  constructor(
    private readonly cognitoService: CognitoService,
    private readonly prismaService: PrismaService,
    private readonly auditService: AuditService,
  ) {}

  async execute(data: UserResetPasswordWithTokenDto): Promise<{
    message: string;
    status: number;
  }> {
    const { token, password } = data;

    // Reset password using the custom token
    const result = await this.cognitoService.resetPasswordWithCustomToken(
      token,
      password,
    );

    // If successful, find the user and audit the action (only for patients)
    if (result.status === 200 && result.email) {
      const user = await this.prismaService.user.findUnique({
        where: { email: result.email },
        include: { patient: true },
      });

      if (user && user.type === 'patient') {
        void this.auditService.append({
          action: 'USER_PASSWORD_RESET',
          actorType: 'PATIENT',
          actorId: user.patient.id,
          patientId: user.patient.id,
          resourceId: user.patient.id,
          resourceType: 'PATIENT',
          details: {
            date: new Date().toISOString(),
          },
        });
      }
    }

    return {
      message: result.message,
      status: result.status,
    };
  }
}
