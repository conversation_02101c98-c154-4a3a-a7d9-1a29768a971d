import { PrismaModule } from '@/modules/prisma/prisma.module';
import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';

import { AppCacheModule } from '../../cache/cache.module';
import { OrchestrationService } from './orchestration.service';
import { OrchestrationWorker } from './orchestration.worker';

@Module({
  imports: [PrismaModule, AppCacheModule, ScheduleModule.forRoot()],
  providers: [OrchestrationService, OrchestrationWorker],
  exports: [OrchestrationService],
})
export class OrchestrationModule {}
