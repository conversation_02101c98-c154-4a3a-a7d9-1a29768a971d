import { CacheService } from '@/modules/cache/cache.service';
import { RedlockService } from '@/modules/cache/redlock.service';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import ShortUniqueId from 'short-unique-id';

export const PRIMARY_INSTANCE_KEY = 'primary_instance';

@Injectable()
export class OrchestrationService {
  private readonly logger = new Logger(OrchestrationService.name);
  private instanceId: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly cache: CacheService,
    private readonly redlock: RedlockService,
  ) {
    this.instanceId =
      configService.get('API_INSTANCE_ID') ?? this.generateInstanceId();
    this.logger.log(`Instance initialized with ID: ${this.instanceId}`);
  }

  getInstanceId(): string {
    return this.instanceId;
  }

  private generateInstanceId(): string {
    const uid = new ShortUniqueId({ length: 10 });
    return uid.rnd();
  }

  async getPrimaryInstanceId() {
    const primaryInstance = await this.cache.get<string>(PRIMARY_INSTANCE_KEY);
    return primaryInstance || null;
  }

  async isPrimaryInstance() {
    const primaryInstance = await this.cache.get<string>(PRIMARY_INSTANCE_KEY);
    return primaryInstance === this.instanceId;
  }

  async runWithLock<T>(
    options: {
      lockKey: string;
      ttl: number;
      thisInstanceMustBePrimary?: boolean;
    },
    fn: () => Promise<T>,
  ) {
    if (options.thisInstanceMustBePrimary) {
      const isPrimary = await this.isPrimaryInstance();
      if (!isPrimary) {
        this.logger.warn(
          `Cannot run function with lock: ${options.lockKey} because this instance is not the primary instance.`,
        );
        return;
      }
    }

    return this.redlock.lockWhileRunning(
      `lock:orchestration-run:${options.lockKey}`,
      options.ttl,
      fn,
    );
  }
}
