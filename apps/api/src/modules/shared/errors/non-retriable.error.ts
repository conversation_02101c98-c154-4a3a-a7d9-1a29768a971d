/**
 * Error thrown when an operation fails and should NOT be retried.
 * Used primarily for SQS consumers to determine if a message should NOT be retried.
 */
export class NonRetriableError extends Error {
  constructor(message: string) {
    super(message);
    this.name = this.constructor.name;

    // https://stackoverflow.com/questions/41102060/typescript-extending-error-class/48342359#48342359
    Object.setPrototypeOf(this, new.target.prototype);
  }
}
