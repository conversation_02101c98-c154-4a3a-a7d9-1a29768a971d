import { RequireCapabilities } from '@modules/auth/decorators/require-capabilities.decorator';
import { CapabilityGuard } from '@modules/auth/guards/capability.guard';
import { roles } from '@modules/auth/types/roles';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { FileInterceptor } from '@nestjs/platform-express';

import { Capability } from '@willow/auth';

import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { CreateProductCategoryDto } from './dto/create-product-category.dto';
import { UpdateProductCategoryDto } from './dto/update-product-category.dto';
import { ProductCategoryService } from './services/product-category.service';

@Controller('product-category')
@UseGuards(AuthGuard('jwt'), RolesGuard, CapabilityGuard)
@Roles(roles.Admin)
export class ProductCategoryController {
  constructor(
    private readonly productCategoryService: ProductCategoryService,
  ) {}

  @Get()
  findAll(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('search') search?: string,
    @Query('sortBy') sortBy?: string,
    @Query('direction') direction?: 'asc' | 'desc',
  ) {
    return this.productCategoryService.findAll({
      page: page ? Number(page) : undefined,
      limit: limit ? Number(limit) : undefined,
      search,
      sortBy,
      direction,
    });
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.productCategoryService.findOne(id);
  }

  @Post()
  @RequireCapabilities(Capability.CREATE_PRODUCT_CATEGORIES)
  create(@Body() createProductCategoryDto: CreateProductCategoryDto) {
    return this.productCategoryService.create(createProductCategoryDto);
  }

  @Patch(':id')
  @RequireCapabilities(Capability.EDIT_PRODUCT_CATEGORIES)
  update(
    @Param('id') id: string,
    @Body() updateProductCategoryDto: UpdateProductCategoryDto,
  ) {
    return this.productCategoryService.update(id, updateProductCategoryDto);
  }

  @Delete(':id')
  @RequireCapabilities(Capability.DELETE_PRODUCT_CATEGORIES)
  delete(@Param('id') id: string) {
    return this.productCategoryService.delete(id);
  }

  @Post(':id/upload-image')
  @RequireCapabilities(Capability.EDIT_PRODUCT_CATEGORIES)
  @UseInterceptors(FileInterceptor('image'))
  async uploadImage(
    @Param('id') id: string,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.productCategoryService.uploadImage(id, file);
  }
}
