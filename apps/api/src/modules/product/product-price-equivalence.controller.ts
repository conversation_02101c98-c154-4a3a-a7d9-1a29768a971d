import { RequireCapabilities } from '@modules/auth/decorators/require-capabilities.decorator';
import { SuperAdminGuard } from '@modules/auth/decorators/super-admin.decorator';
import { CapabilityGuard } from '@modules/auth/guards/capability.guard';
import { roles } from '@modules/auth/types/roles';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

import { Capability } from '@willow/auth';

import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { CreateProductPriceEquivalenceDto } from './dto/create-product-price-equivalence.dto';
import { UpdateProductPriceEquivalenceDto } from './dto/update-product-price-equivalence.dto';
import { ProductPriceEquivalenceService } from './services/product-price-equivalence.service';

@Controller('product-price-equivalence')
@UseGuards(AuthGuard('jwt'), RolesGuard, CapabilityGuard)
@Roles(roles.Admin)
export class ProductPriceEquivalenceController {
  constructor(
    private readonly productPriceEquivalenceService: ProductPriceEquivalenceService,
  ) {}

  @Get()
  findAll(
    @Query('pharmacyId') pharmacyId: string,
    @Query('search') search?: string,
  ) {
    return this.productPriceEquivalenceService.findAll({
      pharmacyId,
      search,
    });
  }

  @Get('pharmacy/:pharmacyId')
  findAllByPharmacy(
    @Param('pharmacyId') pharmacyId: string,
    @Query('search') search?: string,
  ) {
    return this.productPriceEquivalenceService.findAll({
      search,
      pharmacyId,
    });
  }

  @Get(':id')
  findOne(@Param('id') id: string, @Query('pharmacyId') pharmacyId: string) {
    return this.productPriceEquivalenceService.findOne(id, { pharmacyId });
  }

  @Get('by-product-price/:productPriceId')
  findByProductPrice(@Param('productPriceId') productPriceId: string) {
    return this.productPriceEquivalenceService.findOneByProductPriceId(
      productPriceId,
    );
  }

  @Post()
  create(
    @Body() createProductPriceEquivalenceDto: CreateProductPriceEquivalenceDto,
  ) {
    return this.productPriceEquivalenceService.create(
      createProductPriceEquivalenceDto,
    );
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateProductPriceEquivalenceDto: UpdateProductPriceEquivalenceDto,
  ) {
    return this.productPriceEquivalenceService.update(
      id,
      updateProductPriceEquivalenceDto,
    );
  }

  @Delete(':id')
  delete(@Param('id') id: string) {
    return this.productPriceEquivalenceService.delete(id);
  }

  @Post(':id/product-price/:productPriceId')
  addProductPrice(
    @Param('id') id: string,
    @Param('productPriceId') productPriceId: string,
  ) {
    return this.productPriceEquivalenceService.addProductPrice(
      id,
      productPriceId,
    );
  }

  @Delete(':id/product-price/:productPriceId')
  removeProductPrice(
    @Param('id') id: string,
    @Param('productPriceId') productPriceId: string,
  ) {
    return this.productPriceEquivalenceService.removeProductPrice(
      id,
      productPriceId,
    );
  }

  @Post(':id/replace-product-prices')
  replaceProductPrices(
    @Param('id') id: string,
    @Body() data: { productPriceIds: string[]; name?: string },
  ) {
    return this.productPriceEquivalenceService.update(id, {
      productPriceIds: data.productPriceIds,
      name: data.name,
    });
  }
}
