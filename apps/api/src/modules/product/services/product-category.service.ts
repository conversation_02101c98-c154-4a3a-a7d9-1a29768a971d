import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { Prisma } from '@prisma/client';

import { PrismaService } from '../../prisma/prisma.service';
import { CreateProductCategoryDto } from '../dto/create-product-category.dto';
import { UpdateProductCategoryDto } from '../dto/update-product-category.dto';

@Injectable()
export class ProductCategoryService {
  constructor(private readonly prisma: PrismaService) {}

  async findAll(options?: {
    page?: number;
    limit?: number;
    search?: string;
    sortBy?: string;
    direction?: 'asc' | 'desc';
  }) {
    const {
      page = 1,
      limit = 10,
      search = '',
      sortBy = 'order',
      direction = 'asc',
    } = options || {};

    let where: Prisma.ProductCategoryWhereInput = {};

    if (search) {
      where = {
        OR: [
          {
            name: {
              contains: search,
              mode: Prisma.QueryMode.insensitive,
            },
          },
          {
            description: {
              contains: search,
              mode: Prisma.QueryMode.insensitive,
            },
          },
          {
            label: {
              contains: search,
              mode: Prisma.QueryMode.insensitive,
            },
          },
        ],
      };
    }

    const total = await this.prisma.productCategory.count({ where });

    let orderBy: Prisma.ProductCategoryOrderByWithRelationInput = {};

    if (sortBy === 'name' || sortBy === 'form' || sortBy === 'enabled') {
      orderBy = { [sortBy]: direction };
    } else if (sortBy === 'order') {
      orderBy = { order: direction };
    } else if (sortBy === 'createdAt' || sortBy === 'updatedAt') {
      orderBy = { [sortBy]: direction };
    }

    const categories = await this.prisma.productCategory.findMany({
      where,
      include: {
        _count: {
          select: { products: true },
        },
      },
      orderBy,
      skip: (page - 1) * limit,
      take: limit,
    });

    return {
      categories,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string) {
    const category = await this.prisma.productCategory.findUnique({
      where: { id },
      include: {
        _count: {
          select: { products: true },
        },
      },
    });

    if (!category) {
      throw new NotFoundException(`Product category with ID ${id} not found`);
    }

    return category;
  }

  async create(createProductCategoryDto: CreateProductCategoryDto) {
    return this.prisma.productCategory.create({
      data: createProductCategoryDto,
    });
  }

  async update(id: string, updateProductCategoryDto: UpdateProductCategoryDto) {
    const category = await this.prisma.productCategory.findUnique({
      where: { id },
    });

    if (!category) {
      throw new NotFoundException(`Product category with ID ${id} not found`);
    }

    return this.prisma.productCategory.update({
      where: { id },
      data: updateProductCategoryDto,
    });
  }

  async delete(id: string) {
    const category = await this.prisma.productCategory.findUnique({
      where: { id },
      include: {
        _count: {
          select: { products: true },
        },
      },
    });

    if (!category) {
      throw new NotFoundException(`Product category with ID ${id} not found`);
    }

    if (category._count.products > 0) {
      throw new NotFoundException(
        `Cannot delete product category with ID ${id} because it has ${category._count.products} associated products`,
      );
    }

    return this.prisma.productCategory.delete({
      where: { id },
    });
  }

  async uploadImage(categoryId: string, file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('No file provided');
    }

    // Verify category exists
    const category = await this.prisma.productCategory.findUnique({
      where: { id: categoryId },
    });

    if (!category) {
      throw new NotFoundException('Product category not found');
    }

    // Validate file type
    const allowedMimeTypes = [
      'image/jpeg',
      'image/png',
      'image/jpg',
      'image/gif',
    ];
    if (!allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException(
        'Invalid file type. Only JPEG, PNG, and GIF are allowed.',
      );
    }

    // Validate file size (5MB max)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      throw new BadRequestException('File size exceeds 5MB limit');
    }

    try {
      // Use category ID as the key, with appropriate extension
      const fileExtension =
        file.originalname.split('.').pop()?.toLowerCase() || 'png';
      const key = `product-categories/${categoryId}.${fileExtension}`;

      // Upload to S3 and get the public URL
      const url = await this.uploadImageToS3(key, file.buffer, file.mimetype);

      // Update the category with the new image URL
      await this.prisma.productCategory.update({
        where: { id: categoryId },
        data: { image: url },
      });

      return { url };
    } catch (error) {
      console.error('Error uploading image:', error);
      throw new BadRequestException('Failed to upload image');
    }
  }

  /**
   * Helper method to upload an image to S3 and return the public URL
   * Uses the same bucket as product images
   */
  private async uploadImageToS3(
    key: string,
    imageBuffer: Buffer,
    contentType: string,
  ): Promise<string> {
    try {
      const { S3 } = await import('aws-sdk');
      const s3 = new S3();
      const bucket = process.env.AWS_S3_PRODUCTS_PHOTOS_BUCKETNAME;

      if (!bucket) {
        throw new Error('AWS_S3_PRODUCTS_PHOTOS_BUCKETNAME is not configured');
      }

      // Calculate MD5 hash for content verification
      const { createHash } = await import('crypto');
      const contentMD5 = createHash('md5').update(imageBuffer).digest('base64');

      // Check if image already exists and is identical
      try {
        const headObjectParams = { Bucket: bucket, Key: key };
        const existingObject = await s3.headObject(headObjectParams).promise();

        const etag = existingObject.ETag.replace(/"/g, '');
        const imageHashMD5 = createHash('md5')
          .update(imageBuffer)
          .digest('hex');

        if (etag === imageHashMD5) {
          console.debug(`Image with key ${key} is already up to date in S3`);
          // Image exists and is identical, return the URL
          const baseUrl = process.env.CLOUDFRONT_PRODUCTS_PHOTOS_URL;
          return baseUrl
            ? `${baseUrl}/${key}`
            : `https://${bucket}.s3.amazonaws.com/${key}`;
        }
      } catch (error: any) {
        if (error.code !== 'NotFound') {
          throw error;
        }
        // NotFound errors are expected when the object doesn't exist yet
      }

      // Upload to S3
      await s3
        .putObject({
          Bucket: bucket,
          Key: key,
          Body: imageBuffer,
          ContentType: contentType,
          ContentMD5: contentMD5,
        })
        .promise();

      console.log(
        `Successfully uploaded product category image with key ${key} to S3`,
      );

      // Format the URL using CloudFront if configured, otherwise use S3 direct URL
      const baseUrl = process.env.CLOUDFRONT_PRODUCTS_PHOTOS_URL;
      return baseUrl
        ? `${baseUrl}/${key}`
        : `https://${bucket}.s3.amazonaws.com/${key}`;
    } catch (error) {
      console.error(
        `Error uploading product category image to S3 with key ${key}:`,
        error,
      );
      throw error;
    }
  }
}
