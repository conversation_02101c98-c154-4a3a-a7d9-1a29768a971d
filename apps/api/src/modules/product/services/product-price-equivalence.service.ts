import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';

import { PrismaService } from '../../prisma/prisma.service';
import { CreateProductPriceEquivalenceDto } from '../dto/create-product-price-equivalence.dto';
import { UpdateProductPriceEquivalenceDto } from '../dto/update-product-price-equivalence.dto';

@Injectable()
export class ProductPriceEquivalenceService {
  constructor(private readonly prisma: PrismaService) {}

  async findAll(options: { pharmacyId: string; search?: string }) {
    const { search = '', pharmacyId } = options;

    // Create the where clause for filtering
    const where: Prisma.ProductPriceEquivalenceGroupWhereInput = {};

    // Add search functionality if provided
    if (search) {
      where.name = { contains: search, mode: 'insensitive' };
    }

    // Get total count
    const total = await this.prisma.productPriceEquivalenceGroup.count({
      where,
    });

    // Fetch all equivalence groups with relationships
    const equivalenceGroups =
      await this.prisma.productPriceEquivalenceGroup.findMany({
        where,
        include: {
          productPrices: {
            include: {
              product: true,
              externalMappings: {
                where: {
                  productPrice: {
                    product: {
                      pharmacyId: pharmacyId,
                    },
                  },
                },
              },
            },
          },
        },
        orderBy: { name: 'asc' },
      });

    return {
      equivalenceGroups,
      total,
    };
  }

  async findOne(id: string, options: { pharmacyId: string }) {
    const { pharmacyId } = options;

    return this.prisma.productPriceEquivalenceGroup.findUnique({
      where: { id },
      include: {
        productPrices: {
          include: {
            product: true,
            externalMappings: {
              where: {
                productPrice: {
                  product: {
                    pharmacyId: pharmacyId,
                  },
                },
              },
            },
          },
        },
      },
    });
  }

  async findOneByProductPriceId(productPriceId: string) {
    return this.prisma.productPriceEquivalenceGroup.findFirst({
      where: {
        productPrices: {
          some: {
            id: productPriceId,
          },
        },
      },
      include: {
        productPrices: {
          include: {
            product: {
              include: { pharmacy: true },
            },
          },
        },
      },
    });
  }

  async create(data: CreateProductPriceEquivalenceDto) {
    try {
      // Verify that all product prices exist
      if (data.productPriceIds && data.productPriceIds.length > 0) {
        const productPrices = await this.prisma.productPrice.findMany({
          where: {
            id: { in: data.productPriceIds },
          },
        });

        if (productPrices.length !== data.productPriceIds.length) {
          const foundIds = productPrices.map((pp) => pp.id);
          const missingIds = data.productPriceIds.filter(
            (id) => !foundIds.includes(id),
          );
          throw new Error(
            `ProductPrice with ID(s) ${missingIds.join(', ')} not found`,
          );
        }
      }

      // Create the equivalence group with connected product prices
      return this.prisma.productPriceEquivalenceGroup.create({
        data: {
          name: data.name,
          productPrices: {
            connect: data.productPriceIds.map((id) => ({ id })),
          },
        },
        include: {
          productPrices: {
            include: {
              product: true,
            },
          },
        },
      });
    } catch (error) {
      console.error('Error creating product price equivalence group:', error);
      throw new Error(
        `Failed to create product price equivalence group: ${error.message}`,
      );
    }
  }

  async update(id: string, data: UpdateProductPriceEquivalenceDto) {
    try {
      // Get the group to ensure it exists
      const group = await this.prisma.productPriceEquivalenceGroup.findUnique({
        where: { id },
        include: {
          productPrices: true,
        },
      });

      if (!group) {
        throw new Error(`ProductPriceEquivalenceGroup with ID ${id} not found`);
      }

      // Verify that all product prices exist if provided
      if (data.productPriceIds && data.productPriceIds.length > 0) {
        const productPrices = await this.prisma.productPrice.findMany({
          where: {
            id: { in: data.productPriceIds },
          },
        });

        if (productPrices.length !== data.productPriceIds.length) {
          const foundIds = productPrices.map((pp) => pp.id);
          const missingIds = data.productPriceIds.filter(
            (id) => !foundIds.includes(id),
          );
          throw new Error(
            `ProductPrice with ID(s) ${missingIds.join(', ')} not found`,
          );
        }
      }

      // First, disconnect all existing product prices
      await this.prisma.productPriceEquivalenceGroup.update({
        where: { id },
        data: {
          productPrices: {
            disconnect: group.productPrices.map((pp) => ({ id: pp.id })),
          },
        },
      });

      // Then update the group with new data and connected product prices
      return this.prisma.productPriceEquivalenceGroup.update({
        where: { id },
        data: {
          ...(data.name !== undefined && { name: data.name }),
          ...(data.productPriceIds !== undefined && {
            productPrices: {
              connect: data.productPriceIds.map((id) => ({ id })),
            },
          }),
        },
        include: {
          productPrices: {
            include: {
              product: true,
            },
          },
        },
      });
    } catch (error) {
      console.error('Error updating product price equivalence group:', error);
      throw new Error(
        `Failed to update product price equivalence group: ${error.message}`,
      );
    }
  }

  async delete(id: string) {
    try {
      // Get the group to ensure it exists
      const group = await this.prisma.productPriceEquivalenceGroup.findUnique({
        where: { id },
      });

      if (!group) {
        throw new Error(`ProductPriceEquivalenceGroup with ID ${id} not found`);
      }

      // Delete the group
      return this.prisma.productPriceEquivalenceGroup.delete({
        where: { id },
      });
    } catch (error) {
      console.error('Error deleting product price equivalence group:', error);
      throw new Error(
        `Failed to delete product price equivalence group: ${error.message}`,
      );
    }
  }

  async addProductPrice(groupId: string, productPriceId: string) {
    try {
      // Verify that the group exists
      const group = await this.prisma.productPriceEquivalenceGroup.findUnique({
        where: { id: groupId },
      });

      if (!group) {
        throw new Error(
          `ProductPriceEquivalenceGroup with ID ${groupId} not found`,
        );
      }

      // Verify that the product price exists
      const productPrice = await this.prisma.productPrice.findUnique({
        where: { id: productPriceId },
      });

      if (!productPrice) {
        throw new Error(`ProductPrice with ID ${productPriceId} not found`);
      }

      // Add the product price to the group
      return this.prisma.productPriceEquivalenceGroup.update({
        where: { id: groupId },
        data: {
          productPrices: {
            connect: { id: productPriceId },
          },
        },
        include: {
          productPrices: {
            include: {
              product: true,
            },
          },
        },
      });
    } catch (error) {
      console.error('Error adding product price to equivalence group:', error);
      throw new Error(
        `Failed to add product price to equivalence group: ${error.message}`,
      );
    }
  }

  async removeProductPrice(groupId: string, productPriceId: string) {
    try {
      // Verify that the group exists
      const group = await this.prisma.productPriceEquivalenceGroup.findUnique({
        where: { id: groupId },
        include: {
          productPrices: true,
        },
      });

      if (!group) {
        throw new Error(
          `ProductPriceEquivalenceGroup with ID ${groupId} not found`,
        );
      }

      // Verify that the product price exists in the group
      const productPriceExists = group.productPrices.some(
        (pp) => pp.id === productPriceId,
      );

      if (!productPriceExists) {
        throw new Error(
          `ProductPrice with ID ${productPriceId} not found in the group`,
        );
      }

      // Remove the product price from the group
      return this.prisma.productPriceEquivalenceGroup.update({
        where: { id: groupId },
        data: {
          productPrices: {
            disconnect: { id: productPriceId },
          },
        },
        include: {
          productPrices: {
            include: {
              product: true,
            },
          },
        },
      });
    } catch (error) {
      console.error(
        'Error removing product price from equivalence group:',
        error,
      );
      throw new Error(
        `Failed to remove product price from equivalence group: ${error.message}`,
      );
    }
  }
}
