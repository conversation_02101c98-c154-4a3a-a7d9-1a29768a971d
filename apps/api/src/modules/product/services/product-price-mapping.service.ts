import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';

import { PrismaService } from '../../prisma/prisma.service';
import { CreateProductPriceMappingDto } from '../dto/create-product-price-mapping.dto';
import { UpdateProductPriceMappingDto } from '../dto/update-product-price-mapping.dto';

@Injectable()
export class ProductPriceMappingService {
  constructor(private readonly prisma: PrismaService) {}

  async findAll(options: {
    productPriceId?: string;
    productId?: string;
    search?: string;
  }) {
    const { productPriceId, productId, search = '' } = options || {};

    // Validation: require either productPriceId or productId
    if (!productPriceId && !productId) {
      throw new Error(
        'Either productPriceId or productId query parameter is required',
      );
    }

    // Create the where clause for filtering
    const where: Prisma.ProductPriceMappingWhereInput = {};

    // Filter by productPriceId if provided
    if (productPriceId) {
      where.productPriceId = productPriceId;
    }

    // Filter by productId if provided
    if (productId) {
      where.productPrice = {
        productId,
      };
    }

    // Add search functionality if provided
    if (search) {
      where.OR = [
        { externalId: { contains: search, mode: 'insensitive' } },
        { name: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Get total count
    const total = await this.prisma.productPriceMapping.count({ where });

    // Fetch all mappings with relationships, sorted by null name first, then by name ascending
    const mappings = await this.prisma.productPriceMapping.findMany({
      where,
      include: {
        productPrice: true,
      },
      orderBy: [
        {
          name: 'asc',
        },
      ],
    });

    // Sort manually to ensure null names come first
    const sortedMappings = [...mappings].sort((a, b) => {
      // If both have names or both don't have names, sort alphabetically
      if ((a.name === null && b.name === null) || (a.name && b.name)) {
        return a.name?.localeCompare(b.name || '') || 0;
      }
      // If a has no name but b does, a comes first
      if (a.name === null && b.name !== null) {
        return -1;
      }
      // If b has no name but a does, b comes first
      return 1;
    });

    return {
      mappings: sortedMappings,
      total,
    };
  }

  async findByPharmacy(pharmacyId: string) {
    // Find all mappings for products that belong to this pharmacy
    const mappings = await this.prisma.productPriceMapping.findMany({
      where: {
        productPrice: {
          product: {
            pharmacyId: pharmacyId,
          },
        },
      },
      include: {
        productPrice: {
          include: {
            product: true,
          },
        },
      },
      orderBy: [
        {
          name: 'asc',
        },
      ],
    });

    // Sort manually to ensure null names come first
    const sortedMappings = [...mappings].sort((a, b) => {
      // If both have names or both don't have names, sort alphabetically
      if ((a.name === null && b.name === null) || (a.name && b.name)) {
        return a.name?.localeCompare(b.name || '') || 0;
      }
      // If a has no name but b does, a comes first
      if (a.name === null && b.name !== null) {
        return -1;
      }
      // If b has no name but a does, b comes first
      return 1;
    });

    return {
      mappings: sortedMappings,
      total: mappings.length,
    };
  }

  async findOne(id: string) {
    return this.prisma.productPriceMapping.findUnique({
      where: { id },
      include: {
        productPrice: true,
      },
    });
  }

  async create(data: CreateProductPriceMappingDto) {
    try {
      // Verify that the product price exists
      const productPrice = await this.prisma.productPrice.findUnique({
        where: { id: data.productPriceId },
      });

      if (!productPrice) {
        throw new Error(
          `ProductPrice with ID ${data.productPriceId} not found`,
        );
      }

      // Create the mapping
      return this.prisma.productPriceMapping.create({
        data: {
          externalId: data.externalId,
          name: data.name,
          metadata: data.metadata,
          productPrice: {
            connect: { id: data.productPriceId },
          },
        },
        include: {
          productPrice: true,
        },
      });
    } catch (error) {
      console.error('Error creating product price mapping:', error);
      throw new Error(
        `Failed to create product price mapping: ${error.message}`,
      );
    }
  }

  async update(id: string, data: UpdateProductPriceMappingDto) {
    try {
      // Get the mapping to ensure it exists
      const mapping = await this.prisma.productPriceMapping.findUnique({
        where: { id },
      });

      if (!mapping) {
        throw new Error(`ProductPriceMapping with ID ${id} not found`);
      }

      // Verify that the product price exists if provided
      if (data.productPriceId) {
        const productPrice = await this.prisma.productPrice.findUnique({
          where: { id: data.productPriceId },
        });

        if (!productPrice) {
          throw new Error(
            `ProductPrice with ID ${data.productPriceId} not found`,
          );
        }
      }

      // Update the mapping
      return this.prisma.productPriceMapping.update({
        where: { id },
        data: {
          ...(data.externalId !== undefined && { externalId: data.externalId }),
          ...(data.name !== undefined && { name: data.name }),
          ...(data.metadata !== undefined && { metadata: data.metadata }),
          ...(data.productPriceId !== undefined && {
            productPrice: {
              connect: { id: data.productPriceId },
            },
          }),
        },
        include: {
          productPrice: true,
        },
      });
    } catch (error) {
      console.error('Error updating product price mapping:', error);
      throw new Error(
        `Failed to update product price mapping: ${error.message}`,
      );
    }
  }

  async delete(id: string) {
    try {
      // Get the mapping to ensure it exists
      const mapping = await this.prisma.productPriceMapping.findUnique({
        where: { id },
      });

      if (!mapping) {
        throw new Error(`ProductPriceMapping with ID ${id} not found`);
      }

      // Delete the mapping
      return this.prisma.productPriceMapping.delete({
        where: { id },
      });
    } catch (error) {
      console.error('Error deleting product price mapping:', error);
      throw new Error(
        `Failed to delete product price mapping: ${error.message}`,
      );
    }
  }
}
