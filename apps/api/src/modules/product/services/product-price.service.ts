import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';

import { PrismaService } from '../../prisma/prisma.service';
import { StripeService } from '../../stripe/service/stripe.service';
import { CreateProductPriceDto } from '../dto/create-product-price.dto';
import { UpdateProductPriceDto } from '../dto/update-product-price.dto';

@Injectable()
export class ProductPriceService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly stripeService: StripeService,
  ) {}

  async findAll(options: {
    productId?: string;
    pharmacyId?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    direction?: 'asc' | 'desc';
  }) {
    const {
      productId,
      pharmacyId,
      page = 1,
      limit = 10,
      sortBy = 'phase',
      direction = 'asc',
    } = options || {};

    // Create the where clause for filtering
    const where: Prisma.ProductPriceWhereInput = {};

    // Build product relation filter for either productId or pharmacyId or both
    const productFilter: Prisma.ProductWhereInput = {};

    if (productId) {
      productFilter.id = productId;
    }

    if (pharmacyId) {
      productFilter.pharmacy = {
        is: {
          id: pharmacyId,
        },
      };
    }

    // Only add the product filter if we have either productId or pharmacyId
    if (productId || pharmacyId) {
      where.product = productFilter;
    }

    // Get total count for pagination
    const total = await this.prisma.productPrice.count({ where });

    // Determine sort field and direction
    let orderBy: Prisma.ProductPriceOrderByWithRelationInput = {
      phase: direction,
    };

    // Handle different sort fields
    if (sortBy === 'name' || sortBy === 'type') {
      orderBy = { [sortBy]: direction };
    } else if (sortBy === 'unit_amount') {
      orderBy = { unit_amount: direction };
    } else if (sortBy === 'active') {
      orderBy = { active: direction };
    } else if (sortBy === 'phase') {
      orderBy = { phase: direction };
    }

    // Fetch paginated prices
    const productPrices = await this.prisma.productPrice.findMany({
      where,
      include: {
        product: true,
      },
      orderBy,
      skip: (page - 1) * limit,
      take: limit,
    });

    return {
      productPrices,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string) {
    // Get the product price details
    const productPrice = await this.prisma.productPrice.findUnique({
      where: { id },
      include: {
        product: {
          include: { pharmacy: true },
        },
      },
    });

    if (!productPrice) {
      return null;
    }

    // Check if this product price is used in any prescriptions
    const prescription = await this.prisma.prescription.findFirst({
      where: {
        productPriceId: id,
      },
      select: { id: true },
    });

    // Add flags indicating if the product price can be deleted and if it's the default price
    return {
      ...productPrice,
      canBeDeleted: !prescription,
      isDefaultPrice: productPrice.product.defaultPriceId === id,
    };
  }

  async create(data: CreateProductPriceDto) {
    try {
      // Check if the product exists
      const product = await this.prisma.product.findUnique({
        where: { id: data.productId },
      });

      if (!product) {
        throw new Error(`Product with ID ${data.productId} not found`);
      }

      // Generate metadata from dedicated fields
      const metadata = {
        description: data.description,
        dosageDescription: data.dosageDescription,
        dosageLabel: data.dosageLabel,
        dosageTimeframe: data.dosageTimeframe,
        dosageAdditionalMessage: data.dosageAdditionalMessage,
        label: data.label,
        milligrams: data.milligrams,
        phase: data.phase || 1,
        form: product.form,
        type: product.type,
        additiveBenefit: data.additiveBenefit,
      };

      // Create the price in Stripe
      const stripePrice = await this.stripeService.client().prices.create({
        product: data.productId,
        unit_amount: data.unit_amount,
        currency: 'usd',
        active: data.active !== undefined ? data.active : true,
        nickname: data.name, // Stripe uses nickname instead of name for prices
      });

      // Use the Stripe price ID
      const priceId = stripePrice.id;

      // First create the price in our database - without transaction
      const productPrice = await this.prisma.productPrice.create({
        data: {
          id: priceId,
          name: data.name,
          unit_amount: data.unit_amount,
          active: data.active !== undefined ? data.active : true,
          // New dedicated fields
          description: data.description,
          dosageDescription: data.dosageDescription,
          dosageLabel: data.dosageLabel,
          dosageTimeframe: data.dosageTimeframe,
          dosageAdditionalMessage: data.dosageAdditionalMessage,
          compoundName: data.compoundName,
          patientDirections: data.patientDirections,
          label: data.label,
          milligrams: data.milligrams,
          phase: data.phase || 1,
          additiveBenefit: data.additiveBenefit,
          // Keep metadata in sync with dedicated fields
          metadata: metadata,
          product: {
            connect: { id: data.productId },
          },
        },
        select: {
          id: true,
          name: true,
          unit_amount: true,
          active: true,
          description: true,
          dosageLabel: true,
          phase: true,
          product: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      // Only after the price is created in our database, set it as default if needed
      if (data.isDefaultPrice) {
        // Update in our database
        await this.prisma.product.update({
          where: { id: data.productId },
          data: {
            defaultPriceId: priceId,
          },
        });

        // Update in Stripe
        await this.stripeService.client().products.update(data.productId, {
          default_price: priceId,
        });
      }

      return productPrice;
    } catch (error) {
      console.error('Error creating product price:', error);
      throw new Error(`Failed to create product price: ${error.message}`);
    }
  }

  async update(id: string, data: UpdateProductPriceDto) {
    try {
      // Get the price first to know its product
      const price = await this.prisma.productPrice.findUnique({
        where: { id },
        include: { product: true },
      });

      if (!price) {
        throw new Error(`Price with ID ${id} not found`);
      }

      const productId = price.productId;
      const isDefaultPrice = price.product.defaultPriceId === id;

      // Check if we're trying to deactivate a default price
      if (data.active === false && isDefaultPrice) {
        // Find another active price for this product that can become the default
        const alternativePrice = await this.prisma.productPrice.findFirst({
          where: {
            productId: productId,
            active: true,
            id: { not: id }, // Not the current price
          },
          orderBy: { phase: 'desc' }, // Get the most recent one
        });

        if (alternativePrice) {
          // Update the product to use the alternative price as default in our database
          await this.prisma.product.update({
            where: { id: productId },
            data: { defaultPriceId: alternativePrice.id },
          });

          // Update in Stripe as well
          await this.stripeService.client().products.update(productId, {
            default_price: alternativePrice.id,
          });
        } else {
          // If no alternative price exists, we can't deactivate this price
          throw new Error(
            'Cannot deactivate the default price without an active alternative price',
          );
        }
      }

      const existingMetadata = price.metadata || {};

      // Create a clean metadata object only from the fields that are defined in the data
      const metadataUpdate: Record<string, any> = {};
      if (data.description !== undefined)
        metadataUpdate.description = data.description;
      if (data.dosageDescription !== undefined)
        metadataUpdate.dosageDescription = data.dosageDescription;
      if (data.dosageLabel !== undefined)
        metadataUpdate.dosageLabel = data.dosageLabel;
      if (data.dosageTimeframe !== undefined)
        metadataUpdate.dosageTimeframe = data.dosageTimeframe;
      if (data.dosageAdditionalMessage !== undefined)
        metadataUpdate.dosageAdditionalMessage = data.dosageAdditionalMessage;
      if (data.label !== undefined) metadataUpdate.label = data.label;
      if (data.milligrams !== undefined)
        metadataUpdate.milligrams = data.milligrams;
      if (data.phase !== undefined) metadataUpdate.phase = data.phase;
      if (data.additiveBenefit !== undefined)
        metadataUpdate.additiveBenefit = data.additiveBenefit;

      const mergedMetadata = { ...existingMetadata, ...metadataUpdate };

      // For Stripe prices, we can only update metadata, active status, and nickname
      // As prices are immutable in Stripe, we can't change the amount
      // Clean metadata object from the update data - convert numbers to strings for Stripe
      const stripeMetadata: Record<string, string> = {};

      if (data.description !== undefined)
        stripeMetadata.description = data.description;
      if (data.dosageDescription !== undefined)
        stripeMetadata.dosageDescription = data.dosageDescription;
      if (data.dosageLabel !== undefined)
        stripeMetadata.dosageLabel = data.dosageLabel;
      if (data.dosageTimeframe !== undefined)
        stripeMetadata.dosageTimeframe = data.dosageTimeframe;
      if (data.dosageAdditionalMessage !== undefined)
        stripeMetadata.dosageAdditionalMessage = data.dosageAdditionalMessage;
      if (data.label !== undefined) stripeMetadata.label = data.label;
      if (data.milligrams !== undefined)
        stripeMetadata.milligrams = data.milligrams?.toString();
      if (data.phase !== undefined)
        stripeMetadata.phase = data.phase?.toString();
      if (data.additiveBenefit !== undefined)
        stripeMetadata.additiveBenefit = data.additiveBenefit;

      // Always update Stripe with any changes to metadata, active status or name
      await this.stripeService.client().prices.update(id, {
        ...(data.active !== undefined && { active: data.active }),
        ...(data.name !== undefined && { nickname: data.name }), // Update nickname if name changes
        ...(Object.keys(stripeMetadata).length > 0 && {
          metadata: stripeMetadata,
        }),
      });

      // Update our database
      const updatedPrice = await this.prisma.productPrice.update({
        where: { id },
        data: {
          ...(data.name !== undefined && { name: data.name }),
          ...(data.unit_amount !== undefined && {
            unit_amount: data.unit_amount,
          }),
          ...(data.active !== undefined && { active: data.active }),
          // New dedicated fields - only update if provided in the payload
          ...(data.description !== undefined && {
            description: data.description,
          }),
          ...(data.dosageDescription !== undefined && {
            dosageDescription: data.dosageDescription,
          }),
          ...(data.compoundName !== undefined && {
            compoundName: data.compoundName,
          }),
          ...(data.patientDirections !== undefined && {
            patientDirections: data.patientDirections,
          }),
          ...(data.dosageLabel !== undefined && {
            dosageLabel: data.dosageLabel,
          }),
          ...(data.dosageTimeframe !== undefined && {
            dosageTimeframe: data.dosageTimeframe,
          }),
          ...(data.dosageAdditionalMessage !== undefined && {
            dosageAdditionalMessage: data.dosageAdditionalMessage,
          }),
          ...(data.label !== undefined && { label: data.label }),
          ...(data.milligrams !== undefined && { milligrams: data.milligrams }),
          ...(data.phase !== undefined && { phase: data.phase }),
          ...(data.additiveBenefit !== undefined && {
            additiveBenefit: data.additiveBenefit,
          }),
          // Keep metadata in sync with dedicated fields
          // Only include if we have metadata fields to update
          ...(Object.keys(metadataUpdate).length > 0 && {
            metadata: mergedMetadata,
          }),
        },
        include: {
          product: true,
        },
      });

      // Handle explicit default price setting/unsetting
      if (data.isDefaultPrice !== undefined) {
        if (data.isDefaultPrice) {
          // Set this price as the default for the product in our database
          await this.prisma.product.update({
            where: { id: productId },
            data: { defaultPriceId: id },
          });

          // Update Stripe product default price
          await this.stripeService.client().products.update(productId, {
            default_price: id,
          });
        } else if (isDefaultPrice) {
          // If we're explicitly unsetting this as default, find another price
          const alternativePrice = await this.prisma.productPrice.findFirst({
            where: {
              productId: productId,
              active: true,
              id: { not: id },
            },
            orderBy: { phase: 'desc' },
          });

          if (alternativePrice) {
            // Set the alternative price as default
            await this.prisma.product.update({
              where: { id: productId },
              data: { defaultPriceId: alternativePrice.id },
            });

            await this.stripeService.client().products.update(productId, {
              default_price: alternativePrice.id,
            });
          } else {
            // If no alternative exists, just update our database but warn
            await this.prisma.product.update({
              where: { id: productId },
              data: { defaultPriceId: null },
            });
            console.warn(
              `Product ${productId} has no default price in our database, but Stripe requires one`,
            );
          }
        }
      }

      return updatedPrice;
    } catch (error) {
      console.error('Error updating product price:', error);
      throw new Error(`Failed to update product price: ${error.message}`);
    }
  }

  async delete(id: string) {
    try {
      // Get the price to check if it's a default price
      const price = await this.prisma.productPrice.findUnique({
        where: { id },
        include: { product: true },
      });

      if (!price) {
        throw new Error(`Price with ID ${id} not found`);
      }

      // In Stripe, we can't delete prices, we can only archive them
      await this.stripeService.client().prices.update(id, {
        active: false,
      });

      // Delete from our database
      return this.prisma.productPrice.delete({
        where: { id },
      });
    } catch (error) {
      console.error('Error handling price deletion:', error);

      // If the deletion fails, try to mark it as inactive in our DB
      if (
        error.message.includes('cannot be deleted') ||
        error.statusCode === 400
      ) {
        console.log(
          `Price ${id} could not be deleted, marking as inactive in our database`,
        );
        return this.prisma.productPrice.update({
          where: { id },
          data: { active: false },
        });
      }

      throw new Error(`Failed to delete price: ${error.message}`);
    }
  }

  async replacePrice(id: string, newUnitAmount: number) {
    try {
      // Get the existing price with all its data
      const existingPrice = await this.prisma.productPrice.findUnique({
        where: { id },
        include: {
          product: true,
          externalMappings: true,
        },
      });

      if (!existingPrice) {
        throw new Error(`Price with ID ${id} not found`);
      }

      // Validate that the new price is different from the current price
      if (existingPrice.unit_amount === newUnitAmount) {
        throw new Error('New price must be different from the current price');
      }

      const productId = existingPrice.productId;
      const isDefaultPrice = existingPrice.product.defaultPriceId === id;

      // Create metadata for the new price from existing price data
      const metadata = {
        description: existingPrice.description,
        dosageDescription: existingPrice.dosageDescription,
        dosageLabel: existingPrice.dosageLabel,
        dosageTimeframe: existingPrice.dosageTimeframe,
        dosageAdditionalMessage: existingPrice.dosageAdditionalMessage,
        label: existingPrice.label,
        milligrams: existingPrice.milligrams,
        phase: existingPrice.phase,
        form: existingPrice.product.form,
        type: existingPrice.product.type,
        additiveBenefit: existingPrice.additiveBenefit,
      };

      // Create the new price in Stripe with all metadata from the old price
      const stripePrice = await this.stripeService.client().prices.create({
        product: productId,
        unit_amount: newUnitAmount,
        currency: 'usd',
        active: existingPrice.active, // Maintain the same active status
        nickname: existingPrice.name, // Keep the same name
        metadata: Object.entries(metadata).reduce((acc, [key, value]) => {
          if (value !== null && value !== undefined) {
            acc[key] = String(value);
          }
          return acc;
        }, {}),
      });

      // If the old price is the default, update the product to use the new price as default
      // This must be done BEFORE archiving the old price to avoid Stripe errors
      if (isDefaultPrice) {
        await this.stripeService.client().products.update(productId, {
          default_price: stripePrice.id,
        });
      }

      // Archive the old price in Stripe (now safe to do after updating default if needed)
      await this.stripeService.client().prices.update(id, {
        active: false,
      });

      // Create new price record in our database with all data from the existing price
      const newPrice = await this.prisma.productPrice.create({
        data: {
          id: stripePrice.id,
          name: existingPrice.name,
          unit_amount: newUnitAmount,
          active: existingPrice.active,
          description: existingPrice.description,
          dosageDescription: existingPrice.dosageDescription,
          dosageLabel: existingPrice.dosageLabel,
          dosageTimeframe: existingPrice.dosageTimeframe,
          dosageAdditionalMessage: existingPrice.dosageAdditionalMessage,
          compoundName: existingPrice.compoundName,
          patientDirections: existingPrice.patientDirections,
          label: existingPrice.label,
          milligrams: existingPrice.milligrams,
          phase: existingPrice.phase,
          additiveBenefit: existingPrice.additiveBenefit,
          metadata: metadata,
          product: {
            connect: { id: productId },
          },
          ...(existingPrice.equivalenceGroupId && {
            equivalenceGroup: {
              connect: { id: existingPrice.equivalenceGroupId },
            },
          }),
        },
        include: {
          product: true,
        },
      });

      // Archive the old price in our database
      await this.prisma.productPrice.update({
        where: { id },
        data: { active: false },
      });

      // Duplicate ProductPriceMapping records for the new price
      if (
        existingPrice.externalMappings &&
        existingPrice.externalMappings.length > 0
      ) {
        await this.prisma.productPriceMapping.createMany({
          data: existingPrice.externalMappings.map((mapping) => ({
            externalId: mapping.externalId,
            productPriceId: stripePrice.id,
            name: mapping.name,
            metadata: mapping.metadata,
          })),
        });
      }

      // If the old price was the default, update our database to reflect the change
      // (Stripe was already updated before archiving the old price)
      if (isDefaultPrice) {
        await this.prisma.product.update({
          where: { id: productId },
          data: {
            defaultPriceId: stripePrice.id,
          },
        });
      }

      return newPrice;
    } catch (error) {
      console.error('Error replacing product price:', error);
      throw new Error(`Failed to replace product price: ${error.message}`);
    }
  }
}
