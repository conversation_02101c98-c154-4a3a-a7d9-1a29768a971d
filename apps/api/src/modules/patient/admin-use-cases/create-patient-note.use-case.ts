import { AuditService } from '@/modules/audit-log/audit-log.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class CreatePatientNoteUseCase {
  constructor(private readonly auditService: AuditService) {}

  async execute(data: {
    patientId: string;
    createdBy: {
      userId: string;
      id: string;
      type: 'ADMIN';
    };
    note: string;
  }) {
    return this.auditService.append({
      patientId: data.patientId,
      action: 'PATIENT_NOTE_CREATED',
      actorType: data.createdBy.type,
      actorId: data.createdBy.id,
      resourceType: 'PATIENT',
      resourceId: data.patientId,
      details: {
        note: data.note,
      },
    });
  }
}
