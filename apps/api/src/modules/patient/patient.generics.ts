import PDFDocument from 'pdfkit';

/**
 * NOTE: due to a bug in te pdfkit code you may need to call
 *  doc.restore() after the function invocation
 * */
export function addSvgWillowLogo(doc: PDFDocument, x, y, hexColor) {
  const scale = 0.15;
  const scaleFactor = 1 / scale;
  doc.save();
  doc.scale(scale);
  doc.translate(x * scaleFactor, y * scaleFactor);
  doc.fillColor(hexColor);

  doc
    .path(
      'M200.499 96.9973C195.57 90.1877 192.05 84.0825 192.05 78.2121C192.05 65.0624 203.785 54.4957 221.857 54.4957C240.164 54.4957 251.195 72.1069 251.195 126.114C251.195 171.903 222.561 240 172.1 240C137.129 240 129.853 214.875 149.099 174.252L166.702 136.916C167.641 135.037 167.875 132.689 167.875 131.046C167.875 128.463 166.702 125.88 162.946 125.88C159.895 125.88 156.844 128.932 154.497 132.22L89.0144 224.033C81.2691 234.834 69.7686 240 60.1456 240C40.8998 240 26.8175 227.79 26.8175 204.543C26.8175 200.316 27.0523 195.855 27.9911 190.924L48.8798 86.1958C50.5227 77.038 47.2369 71.8721 41.3693 71.8721C35.971 71.8721 30.5728 75.3943 29.3993 84.3173L23.7664 127.054C22.5929 137.151 18.1335 141.377 10.6229 141.377C6.39821 141.377 1 137.62 1 131.515C1 99.1107 24.0011 54.4957 61.7886 54.4957C88.3102 54.4957 99.1067 63.8884 99.1067 77.9773C99.1067 88.0744 93.239 100.05 84.555 113.669L50.5227 167.677C44.8898 176.365 41.604 181.531 41.604 187.871C41.604 194.915 45.8286 199.847 54.278 199.847C60.3804 199.847 64.3703 195.62 71.8809 184.349L125.394 104.277C132.904 93.0055 142.058 85.0217 152.619 85.0217C161.538 85.0217 167.406 89.7181 170.926 104.746L187.825 179.183C190.642 191.158 195.805 198.907 205.428 198.907C212 198.907 217.398 196.794 223.97 184.584L233.123 167.677C236.174 162.276 238.052 157.815 238.052 153.588C238.052 150.066 236.644 146.778 234.062 143.256L200.499 96.9973Z',
    )
    .fill();

  doc
    .path(
      'M266.556 235.123V234.175C268.135 227.222 273.189 208.261 273.189 189.299L273.505 123.566C273.505 104.604 268.135 85.6428 266.556 78.6903V77.7422H305.724V189.299C305.724 208.261 311.094 227.222 312.358 234.175V235.123H266.556Z',
    )
    .fill();

  doc
    .path(
      'M334.25 235.123V234.175C335.829 227.222 341.199 208.261 341.199 189.299V50.248C341.199 31.2865 335.829 12.957 333.302 6.63652V5.37242L339.935 4.42435L374.05 0V189.299C374.05 208.261 379.104 227.222 380.683 234.175V235.123H334.25Z',
    )
    .fill();

  doc
    .path(
      'M399.535 235.123V234.175C401.114 227.222 406.484 208.261 406.484 189.299V50.248C406.484 31.2865 401.114 12.957 398.587 6.63652V5.37242L405.221 4.42435L439.335 0V189.299C439.335 208.261 444.389 227.222 445.969 234.175V235.123H399.535Z',
    )
    .fill();

  doc
    .path(
      'M538.103 237.651C495.46 237.651 460.398 207.629 460.398 156.116C460.398 104.92 495.46 75.846 538.103 75.846C580.747 75.846 616.441 104.92 616.441 156.116C616.441 207.629 580.747 237.651 538.103 237.651ZM538.103 226.59C564.953 226.59 579.799 193.407 579.799 156.116C579.799 119.141 564.953 86.9069 538.103 86.9069C511.254 86.9069 497.039 119.141 497.039 156.116C497.039 193.091 511.254 226.59 538.103 226.59Z',
    )
    .fill();

  doc
    .path(
      'M845.979 77.7422V79.0063C841.557 85.9588 834.923 97.3358 829.554 112.189L788.49 235.123H765.115L730.052 137.155L696.254 235.123H674.142L633.078 120.09C626.761 102.392 617.601 85.6428 612.862 78.6903V77.7422H664.666V78.6903C662.771 85.6428 662.771 103.972 668.772 122.302L695.622 202.888L723.735 119.458C717.417 102.076 708.573 85.6428 703.835 78.6903V77.7422H755.638V78.6903C753.743 85.6428 753.743 103.972 759.745 122.302L786.594 202.572L813.128 124.83C819.446 105.552 816.603 86.2749 814.076 79.0063V77.7422H845.979Z',
    )
    .fill();

  doc.save();
  doc.scale(1);
  doc.translate(0, 0);
  doc.restore();
}
