import { NonRetriableError } from '@/modules/shared/errors/non-retriable.error';
import { RetriableError } from '@/modules/shared/errors/retriable.error';
import { PrismaService } from '@modules/prisma/prisma.service';
import { SqsConsume } from '@modules/shared/aws/sqs/sqs.decorator';
import { Injectable, Logger } from '@nestjs/common';

import { PharmacyService } from './pharmacy.service';

// To bypass TypeScript type checking for the decorator
const topic = (name: string) => name as any;

@Injectable()
export class PharmacyTransferConsumer {
  private readonly logger = new Logger(PharmacyTransferConsumer.name);

  constructor(
    private readonly pharmacyService: PharmacyService,
    private readonly prismaService: PrismaService,
  ) {
    this.logger.log('PharmacyTransferConsumer initialized');
  }

  /**
   * Consumes messages from the pharmacy-transfers queue
   * Each message contains a pharmacy transfer request for a single patient
   */
  @SqsConsume(topic('pharmacy-transfers'), {
    batchSize: 1,
    maxRetries: 3,
    deadLetterQueueName: 'pharmacy-transfers-dlq',
  })
  async handlePharmacyTransferMessages(message: any) {
    const payload = message.payload;
    const { targetPharmacyId, patientId, bulkTransferId } = payload;
    try {
      if (!payload) {
        this.logger.error('Invalid message format - missing payload');
        return true;
      }

      if (!targetPharmacyId || !patientId) {
        this.logger.error(
          'Invalid message payload format - missing required fields',
          payload,
        );
        return true;
      }

      this.logger.log(
        `Processing pharmacy transfer: Patient ${patientId} to Pharmacy ${targetPharmacyId}`,
      );

      // Execute the actual transfer
      await this.pharmacyService.transferPatient(
        targetPharmacyId,
        patientId,
        bulkTransferId,
      );

      this.logger.log(
        `Successfully transferred patient ${patientId} to pharmacy ${targetPharmacyId}`,
      );
      return true;
    } catch (error) {
      // Handle errors based on their type
      if (error instanceof NonRetriableError) {
        // For non-retriable errors, log them but don't rethrow
        // This effectively acknowledges the message and prevents retries
        this.logger.error(
          `Pharmacy transfer of patient ${payload.patientId} to pharmacy ${payload.targetPharmacyId} failed with non-retriable error: ${error.message}`,
        );
        if (bulkTransferId) {
          // Change: increment completedJobs instead of decrementing queuedJobs
          await this.prismaService.bulkTransfer.update({
            where: {
              id: bulkTransferId,
              type: 'pharmacy',
            },
            data: { completedJobs: { increment: 1 } },
          });
        }

        return true; // Indicate the message was processed (won't be retried)
      } else if (error instanceof RetriableError) {
        // For retriable errors, log and rethrow to trigger retry mechanism
        this.logger.error(
          `Pharmacy transfer of patient ${payload.patientId} to pharmacy ${payload.targetPharmacyId} failed with retriable error (WILL retry): ${error.message}`,
        );
        throw error; // Rethrow to trigger retry
      } else {
        // For unclassified errors, log and treat as non-retriable for safety
        this.logger.error(
          `Pharmacy transfer of patient ${payload.patientId} to pharmacy ${payload.targetPharmacyId} failed with unclassified error (will NOT retry): ${error.message}`,
        );
        return true;
      }
    }
  }
}
