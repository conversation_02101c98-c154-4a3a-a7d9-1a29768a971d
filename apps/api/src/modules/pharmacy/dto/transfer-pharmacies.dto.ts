import {
  <PERSON><PERSON><PERSON>y,
  IsBoolean,
  <PERSON>Not<PERSON><PERSON>y,
  <PERSON><PERSON><PERSON>al,
  IsString,
  IsUUI<PERSON>,
} from 'class-validator';

export class TransferPharmaciesDto {
  /**
   * Source pharmacy ID whose patients will be transferred
   */
  @IsUUID()
  @IsNotEmpty()
  sourcePharmacyId: string;

  /**
   * Target pharmacy ID to which patients will be transferred
   */
  @IsUUID()
  @IsNotEmpty()
  targetPharmacyId: string;

  /**
   * Array of state IDs to include in the transfer
   * Only patients in these states will be transferred
   */
  @IsArray()
  @IsUUID(undefined, { each: true })
  @IsNotEmpty()
  stateIds: string[];

  /**
   * Array of medication forms to include in the transfer
   * Only treatments with these forms will be transferred
   */
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  forms?: string[];

  /**
   * Array of generic names to include in the transfer
   * Only treatments with these generic names will be transferred
   */
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  genericNames?: string[];

  /**
   * Optional reason for the transfer
   */
  @IsString()
  @IsOptional()
  reason?: string;

  /**
   * Include patients without active treatments
   * When true and product classes are selected, includes both matching active treatments and inactive patients
   */
  @IsBoolean()
  @IsOptional()
  includeInactive?: boolean;
}
