import { AiService } from '@modules/ai/ai.service';
import { Injectable } from '@nestjs/common';

const PROMPT_TYPES = {
  questions: 'Do you have any questions for the doctor?',
  moreInfo: 'Is there anything else the doctor should know?',
} as const;

type PromptType = keyof typeof PROMPT_TYPES;
type PromptResponse = { isEmpty: boolean; reason: string };

@Injectable()
export class AnalyzeFollowUpUseCase {
  private readonly PROMPT_TYPES = PROMPT_TYPES;

  constructor(private readonly aiService: AiService) {}

  async execute(type: PromptType, response: string) {
    // system prompt for steering the context of the AI model
    const system = `You are a doctor, and need to analyze patient
    questionnaires in a medical context, to review if the patient needs to
    continue the treatment or not. The messages are from follow up
    questionnaires asking how well the medication is performing, side effects,
    and if they want to continue the treatment or not. Your task is to
    determine for some open ended questions if the answer contains actual
    questions or concerns for the doctor, versus simple acknowledgments or
    pleasantries. You ALWAYS respond in the _VALID_ json format provided below,
    and NO other text:
    { "isEmpty": boolean, "reason": string }.
    Reason is your reasoning for the decision, in a short sentence if possible,
    but no more than 60 words.`;

    const message = `Analyze the message from the patient regarding for the 
    following question:
    ${this.PROMPT_TYPES[type]}. 
    
    Consider empty if it's just pleasantries, thank you notes, or simple 
    acknowledgments without actual content. If not 100% sure, consider it as
    NOT EMPTY. 
    If the message is considered empty, respond with "true"; otherwise, respond 
    with "false".

    Message:
    ${response}`;

    try {
      const response = await this.aiService.singlePrompt(system, message);
      const payload = JSON.parse(response) satisfies PromptResponse;
      // @todo might want to actually log this somewhere where we can analyze it
      console.log(
        'Prompt response:',
        `${payload.isEmpty ? 'Empty' : 'Not empty'}, ${payload.reason}`,
      );
      return payload;
    } catch (e) {
      if (e instanceof SyntaxError) {
        console.error('Invalid JSON response in Analyze Follow Up:', e);
      } else {
        console.error('Unexpected response structure in Analyze Follow Up:', e);
      }
      // in case of error, consider the message as not empty to be safe
      return { isEmpty: false };
    }
  }
}
