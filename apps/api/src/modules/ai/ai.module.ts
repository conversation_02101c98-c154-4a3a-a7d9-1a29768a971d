import { <PERSON><PERSON><PERSON>roller } from '@modules/ai/ai.controller';
import { AiService } from '@modules/ai/ai.service';
import { AnalyzeFollowUpUseCase } from '@modules/ai/use-cases/analyze-follow-up.use-case';
import { Module } from '@nestjs/common';

@Module({
  imports: [],
  controllers: [AiController],
  providers: [AiService, AnalyzeFollowUpUseCase],
  exports: [AiService, AnalyzeFollowUpUseCase],
})
//
export class AiModule {}
