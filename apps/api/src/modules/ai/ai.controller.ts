import { AnalyzeFollowUpUseCase } from '@modules/ai/use-cases/analyze-follow-up.use-case';
import { Body, Controller, Post } from '@nestjs/common';

@Controller('ai')
export class AiController {
  constructor(private readonly analyzeFollowUp: AnalyzeFollowUpUseCase) {}

  @Post('test-follow-up-autocomplete')
  async testAutoComplete(
    @Body() body: { type: 'questions' | 'moreInfo'; response: string },
  ) {
    return this.analyzeFollowUp.execute(body.type, body.response);
  }
}
