import {
  BedrockRuntimeClient,
  ConverseCommand,
  ConverseCommandInput,
  InvokeModelCommand,
} from '@aws-sdk/client-bedrock-runtime';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { z } from 'zod';

export enum BedrockModel {
  CLAUDE_3_5_HAIKU = 'anthropic.claude-3-5-haiku-20241022-v1:0',
  CLAUDE_4_0_SONNET = 'us.anthropic.claude-sonnet-4-20250514-v1:0',
}

@Injectable()
export class AiService {
  private readonly client: BedrockRuntimeClient;
  private readonly defaultModel = BedrockModel.CLAUDE_4_0_SONNET;
  private readonly anthropicVersion = 'bedrock-2023-05-31';

  constructor(private readonly configService: ConfigService) {
    const region = this.configService.get('AWS_REGION') || 'us-west-2';
    const accessKeyId = this.configService.getOrThrow('AWS_ACCESS_KEY_ID');
    const secretAccessKey = this.configService.getOrThrow(
      'AWS_SECRET_ACCESS_KEY',
    );

    this.client = new BedrockRuntimeClient({
      region,
      credentials:
        accessKeyId && secretAccessKey
          ? { accessKeyId, secretAccessKey }
          : undefined,
    });
  }

  async singlePrompt(
    system: string,
    message: string,
    temperature: number = 0.2,
    model: BedrockModel = this.defaultModel,
  ): Promise<string> {
    const prompt = {
      anthropic_version: this.anthropicVersion,
      system,
      messages: [{ role: 'user', content: message }],
      temperature,
      max_tokens: 200,
    };

    try {
      const command = new InvokeModelCommand({
        modelId: model,
        body: JSON.stringify(prompt),
        contentType: 'application/json',
        accept: 'application/json',
      });

      const response = await this.client.send(command);
      const responseData = JSON.parse(new TextDecoder().decode(response.body));
      return responseData.content[0].text as string;
    } catch (error) {
      console.error('Bedrock API error:', error);
      throw error;
    }
  }

  async structuredPromptWithToolUse<T>(
    system: string,
    message: string,
    schema: z.ZodSchema<T>,
    temperature: number = 0,
    model: BedrockModel = this.defaultModel,
  ): Promise<T> {
    // Convert Zod schema to JSON Schema
    const jsonSchema = this.zodToJsonSchema(schema);

    const input: ConverseCommandInput = {
      modelId: model,
      system: [{ text: system }],
      messages: [
        {
          role: 'user',
          content: [{ text: message }],
        },
      ],
      toolConfig: {
        tools: [
          {
            toolSpec: {
              name: 'respond_with_structure',
              description: 'Respond with structured data',
              inputSchema: {
                json: jsonSchema,
              },
            },
          },
        ],
        toolChoice: {
          tool: {
            name: 'respond_with_structure',
          },
        },
      },
      inferenceConfig: {
        temperature,
        maxTokens: 4096,
        topP: 1,
      },
    };

    try {
      const command = new ConverseCommand(input);
      const response = await this.client.send(command);

      // Extract the tool use response
      const toolUse = response.output?.message?.content?.find(
        (content) => content.toolUse,
      );

      if (toolUse?.toolUse?.input) {
        const result = toolUse.toolUse.input;
        // Validate with Zod
        return schema.parse(result);
      }

      throw new Error('No tool use response received');
    } catch (error) {
      console.error('Bedrock Converse API error:', error);
      throw error;
    }
  }

  private zodToJsonSchema(schema: z.ZodSchema<any>): Record<string, any> {
    // For production, use a library like zod-to-json-schema
    // This is a simplified version
    if (schema instanceof z.ZodObject) {
      const shape = schema.shape;
      return {
        type: 'object',
        properties: Object.entries(shape).reduce(
          (acc, [key, value]) => {
            acc[key] = this.zodToJsonSchema(value as z.ZodSchema<any>);
            return acc;
          },
          {} as Record<string, any>,
        ),
        required: Object.keys(shape).filter((key) => !shape[key].isOptional()),
      };
    } else if (schema instanceof z.ZodString) {
      return { type: 'string' };
    } else if (schema instanceof z.ZodNumber) {
      return { type: 'number' };
    } else if (schema instanceof z.ZodBoolean) {
      return { type: 'boolean' };
    } else if (schema instanceof z.ZodArray) {
      return {
        type: 'array',
        items: this.zodToJsonSchema(schema.element),
      };
    }
    // Add more type mappings as needed
    return { type: 'string' };
  }
}
