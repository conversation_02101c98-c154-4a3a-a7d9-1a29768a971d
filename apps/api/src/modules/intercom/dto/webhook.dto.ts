import {
  IsNotEmpty,
  <PERSON><PERSON><PERSON>ber,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';

export class IntercomWebhookDto {
  @IsString()
  @IsNotEmpty()
  type: string;

  @IsString()
  @IsNotEmpty()
  app_id: string;

  @IsObject()
  data: {
    type: string;
    item: Record<string, any>;
  };

  @IsObject()
  links: Record<string, any>;

  @IsString()
  @IsOptional()
  id: string | null;

  @IsString()
  @IsNotEmpty()
  topic: string;

  @IsString()
  @IsOptional()
  delivery_status: string | null;

  @IsNumber()
  delivery_attempts: number;

  @IsNumber()
  delivered_at: number;

  @IsNumber()
  first_sent_at: number;

  @IsNumber()
  created_at: number;

  @IsString()
  @IsOptional()
  self: string | null;
}
