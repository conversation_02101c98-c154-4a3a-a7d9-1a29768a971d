import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { User } from '@prisma/client';
import { IntercomClient } from 'intercom-client';

export interface ContactUpdateProp {
  name?: string;
  email?: string;
}
@Injectable()
export class IntercomService {
  private client: IntercomClient;
  private readonly logger = new Logger(IntercomService.name);

  constructor(private readonly configService: ConfigService) {
    this.client = new IntercomClient({
      token: this.configService.get<string>('INTERCOM_ACCESS_TOKEN'),
    });
  }

  /**
   * Find a contact by email. Returns undefined if not found.
   * Throws only on API errors (auth, network, etc.)
   */
  async findContact(email: string) {
    try {
      const response = await this.client.contacts.search({
        query: { field: 'email', operator: '=', value: email },
      });
      // No error is thrown if contact is not found, just an empty array
      return response.data[0];
    } catch (error) {
      // This will only happen on API errors (auth, network issues, etc)
      this.logger.error(
        `API error finding contact with email ${email}:`,
        error,
      );
      throw error;
    }
  }
  /**
   * Find a contact by email. Returns undefined if not found.
   * Throws only on API errors (auth, network, etc.)
   */
  async findContactById(intercomContactId: string) {
    try {
      const contact = await this.client.contacts.find({
        contact_id: intercomContactId,
      });
      return contact;
    } catch (error) {
      // This will only happen on API errors (auth, network issues, etc)
      this.logger.error(
        `API error finding contact with id ${intercomContactId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Create a new contact
   * May throw on API errors or if contact already exists
   */
  async createContact(user: User) {
    try {
      return await this.client.contacts.create({
        email: user.email,
        external_id: user.id,
        name: `${user.firstName} ${user.lastName}`,
      });
    } catch (error) {
      // Could be conflict error (if contact exists) or other API issues
      this.logger.error(`Error creating contact for user ${user.id}:`, error);
      throw error;
    }
  }

  /**
   * Create a conversation with Intercom
   * Handles lookup, creation and unarchiving as needed
   */
  async createConversation(user: User, body: string) {
    let contact = await this.findContact(user.email);
    let contactId;

    if (!contact) {
      try {
        // No contact found, try to create one
        contact = await this.createContact(user);
        contactId = contact.id;
      } catch (error: any) {
        // Contact creation might fail if it exists but is archived
        const firstError = error.body?.errors?.[0];
        const isConflictError =
          error?.statusCode === 409 && firstError?.code === 'conflict';

        if (!isConflictError) {
          this.logger.error(
            `Unexpected error creating contact for ${user.email}:`,
            error,
          );
          throw error;
        }

        // Handle the case where contact exists but is archived
        contactId = firstError?.message.match(/id=(.+)$/)?.[1];
        if (!contactId) {
          this.logger.error(
            'Could not extract contact ID from conflict error message',
            error,
          );
          throw error;
        }

        try {
          // Try to unarchive the existing contact
          await this.client.contacts.unarchive({ contact_id: contactId });
          this.logger.log(
            `Unarchived contact ${contactId} for user ${user.id}`,
          );
        } catch (unarchiveError) {
          this.logger.error(
            `Failed to unarchive contact ${contactId} for user ${user.id}:`,
            unarchiveError,
          );
          throw unarchiveError;
        }
      }
    } else {
      contactId = contact.id;
    }

    if (!contactId) {
      throw new Error('Could not determine contact ID for conversation');
    }

    try {
      // Create the conversation with the contact ID
      const message = await this.client.conversations.create({
        from: {
          type: 'user',
          id: contactId,
        },
        body: body,
      });

      return { message };
    } catch (error) {
      this.logger.error(
        `Error creating conversation for user ${user.id} with contact ${contactId}:`,
        error,
      );
      throw error;
    }
  }

  async updateContactEmailByEmail(email: string, data: ContactUpdateProp) {
    try {
      const contact = await this.findContact(email);
      const updatedContact = await this.updateContactEmailByContactId(
        contact.id,
        data,
      );
      return updatedContact;
    } catch (error) {
      this.logger.error(
        `[Intercom] Error updating contact ${email} email`,
        error,
      );
      throw error;
    }
  }
  async updateContactEmailByContactId(
    contactId: string,
    data: ContactUpdateProp,
  ) {
    try {
      const contact = await this.client.contacts.update({
        contact_id: contactId,
        email: data.email || undefined,
        name: data.name || undefined,
      });
      return contact;
    } catch (error) {
      this.logger.error(
        `[Intercom] Error updating contact ${contactId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Add a message to an existing Intercom conversation
   * @param conversationId The Intercom conversation ID
   * @param user The user who is sending the message
   * @param body The message content
   * @returns The updated conversation
   */
  async addMessageToConversation(
    conversationId: string,
    user: User,
    body: string,
  ) {
    try {
      // First, find the contact to get their Intercom ID
      let contact = await this.findContact(user.email);

      if (!contact) {
        // If contact doesn't exist, create one
        try {
          contact = await this.createContact(user);
        } catch (error: any) {
          // Handle the case where contact exists but is archived
          const firstError = error.body?.errors?.[0];
          const isConflictError =
            error?.statusCode === 409 && firstError?.code === 'conflict';

          if (!isConflictError) {
            throw error;
          }

          // Extract contact ID from error message and unarchive
          const contactId = firstError?.message.match(/id=(.+)$/)?.[1];
          if (!contactId) {
            this.logger.error(
              'Could not extract contact ID from conflict error message',
              error,
            );
            throw error;
          }

          await this.client.contacts.unarchive({ contact_id: contactId });
          contact = await this.findContactById(contactId);
        }
      }

      if (!contact?.id) {
        throw new Error(
          'Could not determine contact ID for conversation reply',
        );
      }

      // Reply to the conversation on behalf of the user
      const conversation = await this.client.conversations.reply({
        conversation_id: conversationId,
        body: {
          message_type: 'comment',
          type: 'user',
          body: body,
          intercom_user_id: contact.id,
        },
      });

      this.logger.log(
        `Successfully added message to conversation ${conversationId} for user ${user.id}`,
      );

      return { conversation };
    } catch (error) {
      this.logger.error(
        `Error adding message to conversation ${conversationId} for user ${user.id}:`,
        error,
      );
      throw error;
    }
  }
}
