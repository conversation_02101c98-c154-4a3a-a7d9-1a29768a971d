export interface IntercomWebhookPayload {
  type: string;
  app_id: string;
  data: {
    type: string;
    item: Record<string, any>;
  };
  links: Record<string, any>;
  id: string | null;
  topic: string;
  delivery_attempts: number;
  delivery_status: string | null;
  delivered_at: number;
  first_sent_at: number;
  created_at: number;
  self: string | null;
}

export interface IntercomConversationEvent extends IntercomWebhookPayload {
  data: {
    type: string;
    item: {
      type: 'conversation';
      id: string;
      created_at: number;
      updated_at: number;
      waiting_since: number;
      snoozed_until: number;
      source: {
        type: string;
        id: string;
        delivered_as: string;
      };
      contacts: {
        type: 'contact';
        id: string;
      }[];
      teammates: {
        type: 'admin';
        id: string;
      }[];
      title: string;
      admin_assignee_id: string;
      team_assignee_id: string;
      custom_attributes: Record<string, any>;
      open: boolean;
      state: string;
      read: boolean;
      tags: {
        type: 'tag';
        id: string;
        name: string;
      }[];
    };
  };
}
