import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';

import { IntercomService } from '../intercom.service';

interface PartialContact {
  type: 'contact';
  id: string;
  external_id?: string;
}

@Injectable()
export class IntercomConversationEventUseCase {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly intercomService: IntercomService,
  ) {}
  async execute(conversationEvent: any) {
    const action = 'INTERCOM_CONVERSATION_MESSAGE_CREATED';

    const patientId = await this.getPatientId(conversationEvent);

    if (!patientId) {
      //Unable to get the patientId, nothing to do here
      return;
    }

    const message = this.getMessage(conversationEvent);

    if (!message) {
      //Unable to get the message data, nothing to do here
      return;
    }

    const conversationId = conversationEvent.data.item.id;
    const auditEntry = {
      actorType: 'SYSTEM',
      actorId: 'INTERCOM',
      actorExtraDetails: {},
      resourceType: 'CONVERSATION',
      resourceId: conversationId,
      action,
      details: {
        content: message.content,
        author: message.author,
        isFirstConversationMessage:
          this.isConversationCreator(conversationEvent),
      },
      patientId: patientId,
      createdAt: new Date(Number(conversationEvent.created_at) * 1000),
    };

    await this.prismaService.auditLog.create({
      data: auditEntry,
    });
  }

  private isConversationReply(event: any) {
    return (
      event.topic == 'conversation.admin.replied' ||
      event.topic == 'conversation.user.replied'
    );
  }

  private isConversationCreator(event: any) {
    return (
      event.topic == 'conversation.admin.single.created' ||
      event.topic == 'conversation.user.created'
    );
  }

  private async getPatientId(conversationEvent: any): Promise<string | null> {
    const partialContact = conversationEvent.data.item.contacts.contacts[0];

    if (partialContact.external_id) {
      return partialContact.external_id;
    }

    const patientByIntercomId = await this.prismaService.patient.findFirst({
      where: { intercomContactId: partialContact.id },
    });
    if (patientByIntercomId) {
      return patientByIntercomId.id;
    }

    const intercomContact = await this.intercomService.findContactById(
      partialContact.id,
    );
    const email = intercomContact?.email;
    if (!email) {
      return null;
    }

    const patientByEmail = await this.prismaService.patient.findFirst({
      where: { user: { email } },
    });

    if (!patientByEmail) {
      return null;
    }

    if (patientByEmail.intercomContactId !== partialContact.id) {
      await this.prismaService.patient.update({
        where: { id: patientByEmail.id },
        data: { intercomContactId: partialContact.id },
      });
    }

    return patientByEmail.id;
  }

  private getMessage(conversationEvent: any) {
    if (this.isConversationReply(conversationEvent)) {
      const messageData =
        conversationEvent.data.item.conversation_parts.conversation_parts.find(
          (part) => part.part_type == 'comment',
        );
      if (!messageData) return;
      return {
        content: messageData.body,
        author: messageData.author,
      };
    } else if (this.isConversationCreator(conversationEvent)) {
      const messageData = conversationEvent.data.item.source;
      if (!messageData) return;
      return {
        content: `${messageData.subject ? messageData.subject + ' ' : ''}${messageData.body}`,
        author: messageData.author,
      };
    } else {
      return null;
    }
  }
}
