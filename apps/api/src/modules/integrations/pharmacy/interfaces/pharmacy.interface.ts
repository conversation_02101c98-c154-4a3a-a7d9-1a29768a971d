export interface PharmacyPrescriptionProduct {
  id: string;
  name: string;
  dose?: string;
  form?: string;
  externalId: string;
  drugDescription: string;
  quantity: number;
  daysSupply?: number;
  sig: string;
  refills?: number;
  originalProductDetails?: any;
  metadata?: Record<string, any>;
}

export interface PharmacyPatient {
  id: string;
  externalId?: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: 'male' | 'female' | 'other' | 'unknown';
  address: {
    street1: string;
    street2?: string;
    city: string;
    state: string;
    zipCode: string;
    country?: string;
  };
  phoneNumber: string;
  email?: string;
  knownAllergies?: string[];
}

export interface PharmacyPrescriber {
  id: string;
  externalId?: string;
  npi: string;
  firstName: string;
  lastName: string;
  deaNumber?: string;
  stateLicenseNumber?: string;
  address?: {
    street1: string;
    street2?: string;
    city: string;
    state: string;
    zipCode: string;
    country?: string;
  };
  phoneNumber?: string;
}

export interface PrescriptionRequest {
  treatmentId: string;
  prescriptionId: string;
  patient: PharmacyPatient;
  prescriber: PharmacyPrescriber;
  products: PharmacyPrescriptionProduct[];
  pharmacyIdentifier?: string;
  prescriptionIssueDate?: Date;
  prescriptionAttachmentBase64?: string;
  poNumber?: string;
  deliveryService?: string;
  referenceFields?: Record<string, string>;
  originalTreatmentDetails?: any;
}

export interface MultiplePrescriptionRequest {
  prescriptions: PrescriptionRequest[];
}

export interface MultiplePrescriptionResponse {
  success: boolean;
  message?: string;
  results: Array<{
    treatmentId: string;
    prescriptionId: string;
    success: boolean;
    orderId?: string;
    pharmacyOrderId?: string;
    message?: string;
    errors?: Array<{ field?: string; message: string }>;
  }>;
  rawResponse?: any;
}

export interface IPharmacyService {
  submitPrescriptions(
    request: MultiplePrescriptionRequest,
  ): Promise<MultiplePrescriptionResponse>;
}
