import type { IPharmacyService } from '@modules/integrations/pharmacy';
import { Injectable } from '@nestjs/common';

import { EmpowerPharmacyService } from './empower.pharmacy.service';
import { EpiqPharmacyService } from './epiq.pharmacy.service';
import { RedRockPharmacyService } from './redrock.pharmacy.service';

export enum KnownPharmacyMappings {
  EMPOWER = 'empower',
  EPIQ = 'epiqScripts',
  REDROCK = 'redRock',
}

@Injectable()
export class PharmacyServiceFactory {
  constructor(
    private readonly empowerPharmacyService: EmpowerPharmacyService,
    private readonly epiqPharmacyService: EpiqPharmacyService,
    private readonly redRockPharmacyService: RedRockPharmacyService,
  ) {}

  getService(mappingSourceIdentifier: string): IPharmacyService {
    switch (mappingSourceIdentifier) {
      case KnownPharmacyMappings.EMPOWER:
        return this.empowerPharmacyService;

      case KnownPharmacyMappings.EPIQ:
        return this.epiqPharmacyService;

      case KnownPharmacyMappings.REDROCK:
        return this.redRockPharmacyService;

      default:
        throw new Error(
          `Unsupported pharmacy mapping source: ${mappingSourceIdentifier}. ` +
            `Supported sources: ${Object.values(KnownPharmacyMappings).join(', ')}`,
        );
    }
  }
}
