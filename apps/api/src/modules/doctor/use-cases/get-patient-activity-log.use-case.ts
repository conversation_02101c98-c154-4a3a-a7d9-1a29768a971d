import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class GetPatientActivityLogUseCase {
  constructor(private readonly prismaService: PrismaService) {}

  async execute(
    patientId: string,
    queries: { includes?: string; excludes?: string },
  ) {
    const includes = queries.includes ? queries.includes.split(',') : [];
    const excludes = queries.excludes ? queries.excludes.split(',') : [];

    return this.prismaService.auditLog.findMany({
      where: {
        patientId,
        action: {
          in: includes.length > 0 ? includes : undefined,
          notIn: excludes.length > 0 ? excludes : undefined,
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }
}
