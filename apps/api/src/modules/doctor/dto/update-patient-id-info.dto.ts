import {
  IsOptional,
  IsString,
  Matches,
  MaxLength,
  Validate,
  ValidationArguments,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';
import { differenceInCalendarYears, isValid, parse } from 'date-fns';

@ValidatorConstraint({ name: 'isValidDate', async: false })
export class IsValidBirthDayFormat implements ValidatorConstraintInterface {
  validate(value: string, _args?: ValidationArguments): boolean {
    const dateRegex = /^(0[1-9]|1[0-2])\/(0[1-9]|[12]\d|3[01])\/\d{4}$/;
    if (!dateRegex.test(value)) {
      return false;
    }
    const [month, day, year] = value.split('/').map(Number);
    const date = new Date(year, month - 1, day);
    return (
      date.getFullYear() === year &&
      date.getMonth() === month - 1 &&
      date.getDate() === day
    );
  }
  defaultMessage(_args?: ValidationArguments): string {
    return 'Invalid date format. Please use MM/DD/YYYY format with a valid date.';
  }
}

@ValidatorConstraint({ name: 'isDateAbove18Bellow100', async: false })
export class IsDateAbove18Bellow100 implements ValidatorConstraintInterface {
  validate(value: string, args?: ValidationArguments): boolean {
    const date = parse(value, 'MM/dd/yyyy', new Date());

    if (!isValid(date)) return false;

    const isOverage = differenceInCalendarYears(date, new Date()) > 100;
    const isMinor = differenceInCalendarYears(new Date(), date) < 18;

    return !isOverage && !isMinor;
  }
  defaultMessage(args?: ValidationArguments): string {
    return 'Invalid date format. Please use MM/DD/YYYY format with a valid date.';
  }
}

export const specialCharsRegex =
  // eslint-disable-next-line no-useless-escape
  /^[a-zA-Z0-9!"#$%&'()*+,\-.\/:;<=>?@[\\\]^_`{|}~\s]*$/;

export class UpdatePatientIdInfoDto {
  @IsOptional()
  @Matches(specialCharsRegex, {
    message: 'First name contains invalid characters',
  })
  @MaxLength(35)
  firstName?: string;

  @IsOptional()
  @Matches(specialCharsRegex, {
    message: 'Last name contains invalid characters',
  })
  @MaxLength(35)
  lastName?: string;

  @IsOptional()
  @IsString()
  @Validate(IsValidBirthDayFormat)
  @Validate(IsDateAbove18Bellow100)
  birthDate?: string;
}
