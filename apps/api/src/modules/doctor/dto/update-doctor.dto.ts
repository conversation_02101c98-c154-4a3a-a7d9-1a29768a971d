import { <PERSON><PERSON><PERSON> } from '@prisma/client';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEmail,
  IsEnum,
  IsNumberString,
  IsOptional,
  IsString,
  IsStrongPassword,
  ValidateNested,
} from 'class-validator';

import { StateLicenseDto } from './create-doctor.dto';

export class UpdateDoctorDto {
  @IsString()
  @IsOptional()
  firstName?: string;

  @IsString()
  @IsOptional()
  middleName?: string;

  @IsString()
  @IsOptional()
  lastName?: string;

  @IsEmail()
  @IsOptional()
  email?: string;

  @IsOptional()
  @IsStrongPassword({
    minLength: 10,
    minLowercase: 1,
    minUppercase: 1,
    minNumbers: 1,
    minSymbols: 1,
  })
  password?: string;

  @IsString()
  @IsOptional()
  dateOfBirth?: string;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  states?: string[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StateLicenseDto)
  @IsOptional()
  stateLicenses?: StateLicenseDto[];

  @IsNumberString()
  @IsOptional()
  npiNumber?: string;

  @IsString()
  @IsOptional()
  primaryPhone?: string;

  @IsString()
  @IsOptional()
  primaryFax?: string;

  @IsString()
  @IsOptional()
  doseSpotClinicianId?: string;

  @IsString()
  @IsOptional()
  temporaryImageKey?: string;

  @IsString()
  @IsOptional()
  address1?: string;

  @IsString()
  @IsOptional()
  address2?: string;

  @IsString()
  @IsOptional()
  city?: string;

  @IsString()
  @IsOptional()
  state?: string;

  @IsString()
  @IsOptional()
  zip?: string;

  @IsEnum(DoctorRole)
  @IsOptional()
  role?: DoctorRole;
}
