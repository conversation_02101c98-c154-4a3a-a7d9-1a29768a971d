import { RolesGuard } from '@modules/auth/guards/roles.guard';
import { Roles } from '@modules/auth/roles.decorator';
import { roles } from '@modules/auth/types/roles';
import { AllQueuesInsightsUseCase } from '@modules/insights/use-cases/all-queues-insights.use-case';
import { InsightsPurgeCacheUseCase } from '@modules/insights/use-cases/insights-purge-cache.use-case';
import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

@Controller('insights')
@UseGuards(AuthGuard('jwt'), RolesGuard)
@Roles(roles.Admin)
export class InsightsController {
  constructor(
    private readonly allQueuesInsightsUseCase: AllQueuesInsightsUseCase,
    private readonly insightsPurgeCacheUseCase: InsightsPurgeCacheUseCase,
  ) {}

  @Get('topics-global')
  async queuesInsights(@Query('forceRefresh') forceRefresh: boolean) {
    return this.allQueuesInsightsUseCase.execute({ forceRefresh });
  }

  @Get('purge-cache')
  async purgeCache() {
    return this.insightsPurgeCacheUseCase.execute();
  }
}
