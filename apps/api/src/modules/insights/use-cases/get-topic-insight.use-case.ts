import { SnsService } from '@/modules/shared/aws/sns/sns.service';
import { SqsService } from '@/modules/shared/aws/sqs/sqs.service';
import { BaseMessage } from '@/modules/shared/events/base.definition';
import { CacheService } from '@modules/cache/cache.service';
import { Injectable } from '@nestjs/common';

export type Topic = {
  name: string;
  topicArn: string;
  backupQueue: {
    arn: string;
    url: string;
  } | null;
  subscriptions: {
    arn: string;
    name: string;
    protocol: string;
    endpoint: string;
    isConfirmed: boolean;
    dlrQueueUrl: string | null;
    failedMessages: {
      messageId: string;
    }[];
  }[];
  messages: Record<string, QueueMessage>;
  messagesCount: number;
};

export type QueueMessage = {
  messageId: string;
  body: string;
  timestamp: number;
  patientId: string;
  topicName: string;
  event: string;
};

@Injectable()
export class GetTopicInsightUseCase {
  constructor(
    private readonly sns: SnsService,
    private readonly sqs: SqsService,
    private readonly cacheService: CacheService,
  ) {}

  async execute({ topicArn }: { topicArn: string }) {
    const topicName = this.sns.getTopicNameFromArn(topicArn);

    const topic: Topic = {
      name: topicName,
      topicArn,
      backupQueue: null,
      subscriptions: [],
      messages: {},
      messagesCount: 0,
    };

    const _subscriptions = await this.sns.listSubscriptionsByTopic(topicArn);

    // get subscriptiosns
    const subscription = await Promise.all(
      _subscriptions
        .filter((s) => s.protocol.toLowerCase() === 'https')
        .map((subscription) => this.getHttpSubscriptionInfo(subscription)),
    );
    topic.subscriptions = subscription;

    // get all messages from backup queue
    const backupQueue = _subscriptions.find(
      (s) =>
        s.protocol.toLowerCase() === 'sqs' && s.endpoint.endsWith('_backup'),
    );
    if (backupQueue) {
      topic.backupQueue = {
        arn: backupQueue.endpoint,
        url: this.sqs.arnToUrl(backupQueue.endpoint),
      };
      const backupQueueMessages = await this.getAllMessagesFromQueue(
        topicName,
        topic.backupQueue.url,
      );
      topic.messages = backupQueueMessages;
      topic.messagesCount = Object.keys(backupQueueMessages).length;
    }

    return topic;
  }

  async getHttpSubscriptionInfo(subscription: {
    arn: string;
    protocol: string;
    endpoint: string;
  }): Promise<Topic['subscriptions'][number]> {
    const subscriptionName = subscription.endpoint.split('/').pop();

    if (subscription.arn === 'PendingConfirmation') {
      return {
        arn: subscription.arn,
        name: subscriptionName,
        endpoint: subscription.endpoint,
        protocol: subscription.protocol,
        isConfirmed: false,
        dlrQueueUrl: null,
        failedMessages: [],
      };
    }

    const subscriptionArn = subscription.arn;
    const subscriptionInfo =
      await this.sns.getSubscriptionInfo(subscriptionArn);

    // get dlr queue messages
    let failedMessages: { messageId: string }[] = [];
    if (subscriptionInfo.dlrQueueUrl) {
      const list = await this.sqs.getQueueMessages(
        subscriptionInfo.dlrQueueUrl,
        {
          maxNumberOfMessages: 10,
          waitTimeSeconds: 1,
          visibilityTimeout: 1,
        },
      );
      failedMessages = list.map((msg) => {
        const parsed = JSON.parse(msg.Body) as {
          MessageId: string;
          Message: string;
        };
        return {
          messageId: parsed.MessageId,
        };
      });
    }

    return {
      arn: subscription.arn,
      name: subscriptionName,
      endpoint: subscription.endpoint,
      protocol: subscription.protocol,
      isConfirmed: subscriptionInfo.status === 'confirmed',
      dlrQueueUrl: subscriptionInfo.dlrQueueUrl,
      failedMessages,
    };
  }

  async getAllMessagesFromQueue(topicName: string, queueUrl: string) {
    try {
      const cacheKey = `insights:topic-messages:${topicName}`;
      const cachedMessages = await this.cacheService.get<string>(cacheKey);

      let messages: Record<string, QueueMessage> = {};

      if (cachedMessages) {
        messages = JSON.parse(cachedMessages);
      }

      let responseLength = 0;
      do {
        const response = await this.sqs.getQueueMessages(queueUrl, {
          maxNumberOfMessages: 10,
          waitTimeSeconds: 0,
          visibilityTimeout: 10,
        });

        response.forEach((msg) => {
          const parsed = this.sqs.decodeMessageBody(msg.Body);
          if (messages[parsed.MessageId]) return;
          const payload = JSON.parse(parsed.Message) as BaseMessage<
            '',
            { event: string }
          >;
          messages[parsed.MessageId] = {
            messageId: parsed.MessageId,
            patientId: payload.metadata.patientId,
            topicName: topicName,
            event: payload.payload.event,
            body: parsed.Message,
            timestamp: parseInt(msg.Attributes.SentTimestamp, 10),
          };
        });
        responseLength = response.length;
      } while (responseLength > 0);

      // clear messages older than 14 days
      const sevenDaysAgo = Date.now() - 14 * 24 * 60 * 60 * 1000;
      messages = Object.fromEntries(
        Object.entries(messages).filter(
          ([, message]) => message.timestamp >= sevenDaysAgo,
        ),
      );

      // cache messages for 1 hour
      await this.cacheService.set(cacheKey, JSON.stringify(messages), 60 * 60);

      return messages;
    } catch (error) {
      console.error(`Error fetching messages from queue ${queueUrl}:`, error);
      return {};
    }
  }
}
