import { SnsService } from '@/modules/shared/aws/sns/sns.service';
import { SqsService } from '@/modules/shared/aws/sqs/sqs.service';
import { CacheService } from '@modules/cache/cache.service';
import { Injectable } from '@nestjs/common';

import { GetTopicInsightUseCase, Topic } from './get-topic-insight.use-case';

@Injectable()
export class AllQueuesInsightsUseCase {
  constructor(
    private readonly sns: SnsService,
    private readonly sqs: SqsService,
    private readonly getTopicInsightUseCase: GetTopicInsightUseCase,
    private readonly cacheService: CacheService,
  ) {}

  async execute({ forceRefresh }: { forceRefresh?: boolean }) {
    return null;

    // TODO: find organize sqs queues
    // const sqsQueuesUrls = await this.sqs.listQueuesUrls({
    //   prefix: this.sqs.getQueuePrefix(),
    // });

    if (forceRefresh) await this.cacheService.del('insights:topics-global');

    return this.cacheService.remember(
      'insights:topics-global',
      5 * 60,
      async () => {
        const snsTopicsArns = await this.sns.listTopicsArns({
          prefix: this.sns.getTopicPrefix(),
        });

        const snsTopics: Topic[] = [];

        for (const topicArn of snsTopicsArns) {
          const topic = await this.getTopicInsightUseCase.execute({ topicArn });
          snsTopics.push(topic);
        }

        return {
          topics: snsTopics,
          date: new Date().toISOString(),
        };
      },
    );
  }
}
