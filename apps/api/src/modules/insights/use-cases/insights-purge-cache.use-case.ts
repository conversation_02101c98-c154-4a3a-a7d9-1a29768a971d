import { SnsService } from '@/modules/shared/aws/sns/sns.service';
import { CacheService } from '@modules/cache/cache.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class InsightsPurgeCacheUseCase {
  constructor(
    private readonly sns: SnsService,
    private readonly cacheService: CacheService,
  ) {}

  async execute() {
    const snsTopicsArns = await this.sns.listTopicsArns({
      prefix: this.sns.getTopicPrefix(),
    });

    const deletionPromises = snsTopicsArns.map((topicArn) => {
      const topicName = this.sns.getTopicNameFromArn(topicArn);
      return this.cacheService.del(`insights:topic-messages:${topicName}`);
    });

    await Promise.all(deletionPromises);

    await this.cacheService.del('insights:topics-global');
  }
}
