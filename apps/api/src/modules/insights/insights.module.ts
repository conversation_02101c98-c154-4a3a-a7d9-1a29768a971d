import { AuthModule } from '@modules/auth/auth.module';
import { AppCacheModule } from '@modules/cache/cache.module';
import { Module } from '@nestjs/common';

import { PrismaModule } from '../prisma/prisma.module';
import { AwsModule } from '../shared/aws/aws.module';
import { OutboxerModule } from '../shared/outboxer/outboxer.module';
import { InsightsController } from './insights.controller';
import { InsightsWorker } from './insights.worker';
import { InsightsUseCases } from './use-cases';

@Module({
  imports: [
    PrismaModule,
    AuthModule,
    AppCacheModule,
    OutboxerModule,
    AwsModule,
  ],
  controllers: [InsightsController],
  providers: [...InsightsUseCases, InsightsWorker],
  exports: [],
})
export class InsightsModule {}
