import { UserImpersonateDto } from '@modules/admin/dto/user-impersonate.dto';
import {
  AdminImpersonateUserUseCase,
  Impersonator,
} from '@modules/admin/use-cases/admin-impersonate-user.use-case';
import { AuthService } from '@modules/auth/auth.service';
import { RolesGuard } from '@modules/auth/guards/roles.guard';
import { Roles } from '@modules/auth/roles.decorator';
import { roles } from '@modules/auth/types/roles';
import { UserSignInDto } from '@modules/shared/dto/user-sign-in.dto';
import {
  Body,
  Controller,
  ForbiddenException,
  HttpCode,
  NotFoundException,
  Param,
  Post,
  Req,
  UnauthorizedException,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Throttle } from '@nestjs/throttler';
import { Request } from 'express';

import { UserRefreshTokenRequestDto } from '../shared/dto/user-refresh-token-request.dto';
import { AdminRefreshTokenUseCase } from './use-cases/admin-refresh-token-use.case';
import { AdminSignInUseCase } from './use-cases/admin-sign-in-use.case';

@Controller('admin')
export class AdminAuthController {
  constructor(
    private readonly adminSignInUseCaseService: AdminSignInUseCase,
    private readonly adminRefreshTokenUseCase: AdminRefreshTokenUseCase,
    private readonly adminImpersonateUserUseCase: AdminImpersonateUserUseCase,
    private readonly authService: AuthService,
  ) {}

  @Post('signIn')
  @HttpCode(200)
  @Throttle({ default: { limit: 10, ttl: 60000 } })
  async signIn(@Body() requestBody: UserSignInDto) {
    try {
      return await this.adminSignInUseCaseService.execute(requestBody);
    } catch (e) {
      if (e.name === 'NotAuthorizedException') {
        throw new ForbiddenException('Invalid credentials');
      } else {
        throw new Error(e.message);
      }
    }
  }

  @Post('refresh')
  @Throttle({ default: { limit: 10, ttl: 60000 } })
  async refreshToken(@Body() requestBody: UserRefreshTokenRequestDto) {
    try {
      return await this.adminRefreshTokenUseCase.execute(requestBody);
    } catch (e) {
      if (e.name === 'NotAuthorizedException') {
        throw new UnauthorizedException('Invalid Refresh Token');
      } else {
        throw new Error(e.message);
      }
    }
  }

  @Post('impersonate')
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles([roles.Admin, roles.Doctor])
  async impersonate(
    @Req() request: Request,
    @Body() requestBody: UserImpersonateDto,
  ) {
    try {
      const impersonator: Impersonator = {
        userId: request.user['userId'],
        role: request.user['role'] as 'Admin' | 'Doctor',
      };
      return await this.adminImpersonateUserUseCase.execute(
        requestBody,
        requestBody.impersonationToken,
        impersonator,
      );
    } catch (e) {
      throw new Error(e.message);
    }
  }

  @Post('impersonate/validate/:key')
  @Throttle({ default: { limit: 10, ttl: 60000 } })
  async validateImpersonation(@Param('key') key: string) {
    try {
      return await this.authService.validateImpersonationRequest(key);
    } catch (e) {
      if (e.name === 'NotFoundException') {
        throw new NotFoundException(e.message);
      }
      throw new Error(e.message);
    }
  }
}
