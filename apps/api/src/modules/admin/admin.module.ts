import { PatientPaymentMethodPersistence } from '@/adapters/persistence/database/patient-payment-method.persistence';
import { PatientPersistence } from '@/adapters/persistence/database/patient.persistence';
import { AdminUseCases } from '@modules/admin/use-cases';
import { AuthModule } from '@modules/auth/auth.module';
import { AppCacheModule } from '@modules/cache/cache.module';
import { ChatModule } from '@modules/chat/chat.module';
import { DoctorModule } from '@modules/doctor/doctor.module';
import { forwardRef, Module } from '@nestjs/common';

import { AuditLogModule } from '../audit-log/audit-log.module';
import { AuditService } from '../audit-log/audit-log.service';
import { ChatImageService } from '../chat/services/chat.image.service';
import { SendMessageUseCase } from '../chat/use-cases/send-message.use-case';
import { GetPatientPreSignedUrlUseCase } from '../doctor/use-cases/get-patient-pre-signed-url.use-case';
import { UpdatePatientPhotoUseCase } from '../doctor/use-cases/update-patient-photo.use-case';
import { DosespotService } from '../dosespot/dosespot.service';
import { PatientModule } from '../patient/patient.module';
import { PatientCancelSubscriptionUseCase } from '../patient/use-cases/patient-cancel-subscription.use-case';
import { PatientRestoreSubscriptionUseCase } from '../patient/use-cases/patient-restore-subscription-use-case';
import { PrismaModule } from '../prisma/prisma.module';
import { SesModule } from '../shared/aws/ses/ses.module';
import { SnsModule } from '../shared/aws/sns/sns.module';
import { OutboxerModule } from '../shared/outboxer/outboxer.module';
import { S3Service } from '../shared/services/s3.service';
import { UserForgotPasswordUseCase } from '../shared/use-cases/user-forgot-password-use.case';
import { StripeModule } from '../stripe/stripe.module';
import { TreatmentService } from '../treatment/services/treatment.service';
import { TreatmentCancelUseCase } from '../treatment/use-cases/treatment-cancel.use-case';
import { AdminAuthController } from './admin-auth.controller';
import { AdminController } from './admin.controller';
import { AdminService } from './admin.service';

@Module({
  imports: [
    forwardRef(() => ChatModule),
    PrismaModule,
    AuthModule,
    AuditLogModule,
    StripeModule,
    PatientModule,
    AppCacheModule,
    OutboxerModule,
    SnsModule,
    SesModule,
    DoctorModule,
  ],
  controllers: [AdminController, AdminAuthController],
  providers: [
    AdminService,
    PatientPaymentMethodPersistence,
    PatientCancelSubscriptionUseCase,
    PatientRestoreSubscriptionUseCase,
    PatientPersistence,
    SendMessageUseCase,
    TreatmentService,
    ChatImageService,
    S3Service,
    DosespotService,
    S3Service,
    TreatmentCancelUseCase,
    TreatmentService,
    AuditService,
    UserForgotPasswordUseCase,
    GetPatientPreSignedUrlUseCase,
    UpdatePatientPhotoUseCase,
    ...AdminUseCases,
  ],
  exports: [AdminService],
})
export class AdminModule {}
