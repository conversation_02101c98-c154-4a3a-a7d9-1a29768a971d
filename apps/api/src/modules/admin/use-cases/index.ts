import { AdminImpersonateUserUseCase } from '@modules/admin/use-cases/admin-impersonate-user.use-case';
import { AdminRefreshTokenUseCase } from '@modules/admin/use-cases/admin-refresh-token-use.case';
import { AdminSignInUseCase } from '@modules/admin/use-cases/admin-sign-in-use.case';
import { CreateAdminAccountUseCase } from '@modules/admin/use-cases/create-admin-account-use.case';

export const AdminUseCases = [
  AdminImpersonateUserUseCase,
  AdminRefreshTokenUseCase,
  AdminSignInUseCase,
  CreateAdminAccountUseCase,
];
