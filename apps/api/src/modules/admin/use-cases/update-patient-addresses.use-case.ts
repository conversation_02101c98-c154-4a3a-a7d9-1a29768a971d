import { PatientService } from '@/modules/patient/patient.service';
import { Injectable } from '@nestjs/common';

import { UpdatePatientAddressesDto } from '../dto/update-patient-addresses.dto';

@Injectable()
export class UpdatePatientAddressesUseCase {
  constructor(private readonly patientService: PatientService) {}

  async execute(
    {
      patientId,
      shippingAddress,
      billingAddress,
      isBillingIsSameAsShipping,
    }: { patientId: string } & UpdatePatientAddressesDto,
    { adminId }: { adminId: string },
  ) {
    const patient =
      await this.patientService.getPatientDataByPatientId(patientId);

    await this.patientService.updateShippingAddress(
      patient,
      {
        address: shippingAddress,
        shouldUpdateBillingAddress: isBillingIsSameAsShipping,
      },
      {
        updatedBy: { type: 'ADMIN', id: adminId },
      },
    );

    if (!isBillingIsSameAsShipping) {
      await this.patientService.updateBillingAddress(
        patient,
        {
          address: {
            ...billingAddress,
            state: patient.state.code,
          },
        },
        {
          updatedBy: { type: 'ADMIN', id: adminId },
        },
      );
    }

    return {
      success: true,
    };
  }
}
