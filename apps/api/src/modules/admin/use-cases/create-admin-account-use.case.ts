import { PrismaService } from '@/modules/prisma/prisma.service';
import { CognitoService } from '@modules/auth/cognito.service';
import { SesService } from '@modules/shared/aws/ses/ses.service';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AdminRole } from '@prisma/client';

@Injectable()
export class CreateAdminAccountUseCase {
  constructor(
    private readonly cognitoService: CognitoService,
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
    private readonly sesService: SesService,
  ) {}

  generateValidPassword(): string {
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '**********';
    const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';

    // Ensure we have at least one of each character type
    let password =
      lowercase[Math.floor(Math.random() * lowercase.length)] +
      uppercase[Math.floor(Math.random() * uppercase.length)] +
      numbers[Math.floor(Math.random() * numbers.length)] +
      symbols[Math.floor(Math.random() * symbols.length)];

    // Add more random characters to reach desired length
    const allChars = lowercase + uppercase + numbers + symbols;
    while (password.length < 12) {
      password += allChars[Math.floor(Math.random() * allChars.length)];
    }

    // Shuffle the password characters
    return password
      .split('')
      .sort(() => Math.random() - 0.5)
      .join('');
  }

  async execute(data: {
    email: string;
    firstName: string;
    lastName: string;
    role?: AdminRole;
  }) {
    const cognitoRole = 'Admin';
    const { email } = data;
    const adminRole = data.role || ('admin' as AdminRole);

    // Always generate a random password for new admins
    const userPassword = this.generateValidPassword();

    const userExists = await this.prisma.user.findFirst({
      where: {
        email,
        type: 'admin',
        deletedAt: null,
      },
    });

    if (userExists) {
      throw new Error(`Admin with email ${email} already exists`);
    }

    const cognitoUser = await this.cognitoService.signUp(
      email,
      userPassword,
      cognitoRole,
    );
    const cognitoUserId = cognitoUser['sub'];
    try {
      const admin = await this.prisma.user.create({
        data: {
          id: cognitoUserId,
          email,
          firstName: data.firstName,
          lastName: data.lastName,
          type: 'admin',
          admin: { create: { id: cognitoUserId, role: adminRole } },
        },
        include: { admin: { select: { role: true } } },
      });

      // Send welcome email with credentials
      try {
        const adminUrl = this.configService.get('ADMIN_URL');
        await this.sesService.sendEmail({
          to: email,
          bcc: [
            // '<EMAIL>',
            '<EMAIL>',
          ],
          subject: 'Your Willow Admin Credentials',
          textBody: `Hi ${data.firstName},

Here are your credentials for logging in to the admin dashboard in Willow:

url: ${adminUrl}
email: ${data.email}
password: ${userPassword}

Let me know if you have any questions

Thanks`,
        });
      } catch (emailError) {
        console.error('Failed to send welcome email:', emailError);
        // Don't throw here, as the admin was successfully created
      }

      return { ...admin, password: userPassword };
    } catch (e) {
      await this.cognitoService.deleteUser(cognitoUserId);
      throw e;
    }
  }
}
