import { UserRefreshTokenRequestDto } from '@/modules/shared/dto/user-refresh-token-request.dto';
import { BaseSignInOutput } from '@/modules/shared/types/user/user.types';
import { CognitoService } from '@modules/auth/cognito.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class AdminRefreshTokenUseCase {
  constructor(private readonly cognitoService: CognitoService) {}

  async execute(data: UserRefreshTokenRequestDto): Promise<BaseSignInOutput> {
    const { refreshToken } = data;
    const result = await this.cognitoService.refresh(refreshToken, 'Admin');
    return {
      accessToken: result.getAccessToken().getJwtToken(),
      refreshToken: result.getRefreshToken().getToken(),
      role: 'Admin',
    };
  }
}
