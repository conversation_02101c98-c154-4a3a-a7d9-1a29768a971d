import { PrismaService } from '@/modules/prisma/prisma.service';
import { CognitoService } from '@modules/auth/cognito.service';
import { Injectable } from '@nestjs/common';

import { UserSetPasswortDto } from '../dto/user-set-password.dto';

@Injectable()
export class UserSetPasswordUseCase {
  constructor(
    private readonly cognitoService: CognitoService,
    private readonly prismaService: PrismaService,
  ) {}
  async execute(data: UserSetPasswortDto): Promise<void> {
    const { email, password } = data;

    const user = await this.prismaService.user.findUnique({
      where: {
        email,
      },
    });
    if (!user) {
      throw new Error(`User with email ${email} not found`);
    }

    return await this.cognitoService.overridePassword(email, password);
  }
}
