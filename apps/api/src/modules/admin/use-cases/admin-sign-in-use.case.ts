import { PrismaService } from '@/modules/prisma/prisma.service';
import { CognitoService } from '@modules/auth/cognito.service';
import {
  AdminSignInOutput,
  UserSignInInput,
} from '@modules/shared/types/user/user.types';
import { Injectable } from '@nestjs/common';

@Injectable()
export class AdminSignInUseCase {
  constructor(
    private readonly cognitoService: CognitoService,
    private readonly prisma: PrismaService,
  ) {}

  async execute(data: UserSignInInput): Promise<AdminSignInOutput> {
    const { email, password } = data;
    const role = 'Admin';

    const tokens = await this.cognitoService.signIn(email, password, role);

    const roles = tokens.getAccessToken().payload['cognito:groups'];
    if (!roles.includes(role)) {
      throw new Error(`Invalid role ${role} for user ${email}`);
    }

    const user = await this.prisma.user.findFirst({
      where: { email, type: 'admin', deletedAt: null },
      include: { admin: true },
    });

    if (!user) {
      const err = new Error(`Admin with email ${email} not found`);
      err.name = 'NotAuthorizedException';
      throw err;
    }

    return {
      accessToken: tokens.getAccessToken().getJwtToken(),
      refreshToken: tokens.getRefreshToken().getToken(),
      role,
      ...user,
    };
  }
}
