import { randomBytes } from 'crypto';
import { CacheService } from '@modules/cache/cache.service';
import { PrismaService } from '@modules/prisma/prisma.service';
import { Injectable, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { UserImpersonateDto } from '../dto/user-impersonate.dto';

export type Impersonator = {
  userId: string;
  role: 'Admin' | 'Doctor';
};
@Injectable()
export class AdminImpersonateUserUseCase {
  constructor(
    private readonly configService: ConfigService,
    private readonly cacheService: CacheService,
    private readonly prisma: PrismaService,
  ) {}

  async execute(
    dto: UserImpersonateDto,
    accessToken: string,
    impersonator: Impersonator,
  ) {
    // Find user and determine role
    const user = await this.prisma.user.findUniqueOrThrow({
      where: { id: dto.userId },
      select: { id: true, type: true },
    });

    if (impersonator.role === 'Admin' && user.type !== 'patient')
      throw new NotFoundException('Can only impersonate patients');
    if (impersonator.role === 'Doctor' && user.type !== 'doctor')
      throw new NotFoundException('Can only impersonate doctors');

    if (impersonator.role === 'Doctor') {
      await this.prisma.user.findUniqueOrThrow({
        where: { id: impersonator.userId, doctor: { role: 'superDoctor' } },
      });
    }

    // Generate random key for the impersonation link
    const key = [
      randomBytes(32).toString('hex'),
      Date.now().toString(36),
      randomBytes(16)
        .toString('base64')
        .replace(/[^a-zA-Z0-9]/g, ''),
      randomBytes(8).toString('hex'),
    ].join('-');

    // Store the impersonation data in Redis
    const impersonationData = {
      accessToken,
      userId: user.id,
      role: user.type,
      impersonated: true,
    };

    // Store for 60 minutes
    await this.cacheService.set(
      `impersonateRequest:${key}`,
      impersonationData,
      3600,
    );

    // Generate the impersonation URL based on role
    const baseUrl =
      user.type === 'patient'
        ? `${this.configService.get('PATIENTS_URL')}/account/impersonate`
        : `${this.configService.get('DOCTORS_URL')}/impersonate`;

    return {
      url: `${baseUrl}/${key}`,
    };
  }
}
