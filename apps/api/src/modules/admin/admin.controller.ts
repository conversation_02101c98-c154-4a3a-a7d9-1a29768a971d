import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  ForbiddenException,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Request } from 'express';

import { AdminRole, Capability } from '@willow/auth';

import { AuditService } from '../audit-log/audit-log.service';
import { RequireCapabilities } from '../auth/decorators/require-capabilities.decorator';
import { CapabilityGuard } from '../auth/guards/capability.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { roles } from '../auth/types/roles';
import { AdminService } from './admin.service';
import { CreateAdminDto } from './dto/create-admin.dto';
import { UpdateAdminDto } from './dto/update-admin.dto';

@Controller('admin')
@UseGuards(AuthGuard('jwt'), RolesGuard, CapabilityGuard)
@Roles(roles.Admin)
@RequireCapabilities(Capability.MANAGE_ADMINS)
export class AdminController {
  constructor(
    private readonly adminService: AdminService,
    private readonly auditService: AuditService,
  ) {}

  @Get('profile')
  @RequireCapabilities() // Override controller-level check
  async getCurrentAdminProfile(@Req() request: Request) {
    try {
      const userId: string = request.user['userId'];
      return await this.adminService.getProfile(userId);
    } catch (e) {
      if (e.name === 'NotAuthorizedException') {
        throw new ForbiddenException('Invalid credentials');
      }
      throw new BadRequestException(e.message);
    }
  }

  // Admin management endpoints
  @Get('admins')
  // Uses controller-level MANAGE_ADMINS capability
  findAll(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('search') search?: string,
    @Query('sortBy') sortBy?: string,
    @Query('direction') direction?: 'asc' | 'desc',
    @Query('showInactive') showInactive?: boolean,
  ) {
    return this.adminService.findAll(
      page,
      limit,
      search,
      sortBy,
      direction,
      showInactive,
    );
  }

  @Get('admins/:id')
  // Uses controller-level MANAGE_ADMINS capability
  findOne(@Param('id') id: string) {
    return this.adminService.findOne(id);
  }

  @Post('admins')
  // Explicitly require more granular capability for creating admins
  @RequireCapabilities(Capability.CREATE_ADMINS)
  async create(
    @Body() createAdminDto: CreateAdminDto,
    @Req() request: Request,
  ) {
    // Get the current user's role
    const adminUser = await this.adminService.findOneByUserId(
      request.user['userId'],
    );

    const result = await this.adminService.create(
      createAdminDto,
      adminUser?.admin?.role as AdminRole,
    );

    // @todo Log the admin creation in audit logs

    return result;
  }

  @Patch('admins/:id')
  // Explicitly require more granular capability for editing admins
  @RequireCapabilities(Capability.EDIT_ADMINS)
  async update(
    @Param('id') id: string,
    @Body() updateAdminDto: UpdateAdminDto,
    @Req() request: Request,
  ) {
    // Get the current user's role
    const currentUser = await this.adminService.findOneByUserId(
      request.user['userId'],
    );

    const userId: string = request.user['userId'];
    return this.adminService.updateAdmin(
      id,
      updateAdminDto,
      currentUser?.admin?.role as AdminRole,
      userId,
    );
  }

  @Delete('admins/:id/deactivate')
  // Explicitly require more granular capability for deactivating admins
  @RequireCapabilities(Capability.DEACTIVATE_ADMINS)
  async deactivate(@Param('id') id: string, @Req() request: Request) {
    // Get the current user's role
    const currentUser = await this.adminService.findOneByUserId(
      request.user['userId'],
    );

    const userId: string = request.user['userId'];
    return this.adminService.deactivate(
      id,
      currentUser?.admin?.role as AdminRole,
      userId,
    );
  }

  @Delete('admins/:id')
  // Explicitly require more granular capability for deleting admins
  @RequireCapabilities(Capability.DELETE_ADMINS)
  async delete(@Param('id') id: string, @Req() request: Request) {
    // Get the current user's role
    const currentUser = await this.adminService.findOneByUserId(
      request.user['userId'],
    );

    const userId: string = request.user['userId'];
    return this.adminService.delete(
      id,
      userId,
      currentUser?.admin?.role as AdminRole,
    );
  }

  @Patch('admins/:id/reactivate')
  // Explicitly require more granular capability for reactivating admins
  @RequireCapabilities(Capability.REACTIVATE_ADMINS)
  async reactivate(@Param('id') id: string, @Req() request: Request) {
    // Get the current user's role
    const currentUser = await this.adminService.findOneByUserId(
      request.user['userId'],
    );

    return this.adminService.reactivate(
      id,
      currentUser?.admin?.role as AdminRole,
    );
  }
}
