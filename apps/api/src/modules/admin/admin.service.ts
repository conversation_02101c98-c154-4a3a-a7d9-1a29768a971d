import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { Prisma, User } from '@prisma/client';

import { AdminRole, Capability, hasCapability } from '@willow/auth';

import { CognitoService } from '../auth/cognito.service';
import { PrismaService } from '../prisma/prisma.service';
import { ValidateInputError } from '../shared/errors/validate-input.error';
import { UpdateAdminDto } from './dto/update-admin.dto';
import { CreateAdminAccountUseCase } from './use-cases/create-admin-account-use.case';

@Injectable()
export class AdminService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly createAdminAccountUseCase: CreateAdminAccountUseCase,
    private readonly cognitoService: CognitoService,
  ) {}

  async getProfile(
    userId: string,
  ): Promise<User & { admin: { role: string } }> {
    const admin = await this.prisma.user.findFirstOrThrow({
      where: { id: userId, type: 'admin' },
      include: { admin: { select: { role: true } } },
    });

    if (!admin) throw new Error(`Admin  not found`);
    if (admin.deletedAt) throw new Error(`Admin is deactivated`);

    return admin;
  }

  async create(
    info: {
      email: string;
      firstName: string;
      lastName: string;
      role?: AdminRole;
    },
    currentUserRole?: AdminRole,
  ) {
    // If creating a superAdmin, check if the current user has the MANAGE_SUPER_ADMINS capability
    if (info.role === 'superAdmin' && currentUserRole) {
      if (!hasCapability(currentUserRole, Capability.MANAGE_SUPER_ADMINS)) {
        throw new ForbiddenException(
          'You do not have permission to create superAdmin users',
        );
      }
    }

    return this.createAdminAccountUseCase.execute(info);
  }

  async findAll(
    page = 1,
    limit = 10,
    search?: string,
    sortBy = 'createdAt',
    direction: 'asc' | 'desc' = 'desc',
    showInactive = false,
  ) {
    const skip = (page - 1) * limit;
    const take = +limit;

    const where: Prisma.UserWhereInput = {
      type: 'admin',
      deletedAt: showInactive ? undefined : null,
      OR: search
        ? [
            { firstName: { contains: search, mode: 'insensitive' } },
            { lastName: { contains: search, mode: 'insensitive' } },
            { email: { contains: search, mode: 'insensitive' } },
          ]
        : undefined,
    };

    const [admins, total] = await Promise.all([
      this.prisma.user.findMany({
        where,
        include: { admin: { select: { role: true } } },
        skip,
        take,
        orderBy: {
          [sortBy]: direction,
        },
      }),
      this.prisma.user.count({ where }),
    ]);

    return {
      data: admins,
      meta: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string) {
    const admin = await this.prisma.user.findFirst({
      where: { id, type: 'admin' },
      include: { admin: { select: { role: true } } },
    });

    if (!admin) {
      throw new NotFoundException(`Admin with ID ${id} not found`);
    }

    return admin;
  }

  async findOneByUserId(userId: string) {
    const admin = await this.prisma.user.findFirst({
      where: { id: userId, type: 'admin' },
      include: { admin: { select: { role: true } } },
    });

    return admin;
  }

  async updateAdmin(
    id: string,
    updateAdminDto: UpdateAdminDto,
    currentUserRole?: AdminRole,
    currentUserId?: string,
  ) {
    const admin = await this.findOne(id);

    // Prevent editing your own role
    if (id === currentUserId && updateAdminDto.role) {
      throw new BadRequestException('You cannot change your own role');
    }

    // Check if we're editing a superAdmin or changing to/from superAdmin role
    if (
      admin.admin.role === 'superAdmin' ||
      updateAdminDto.role === 'superAdmin'
    ) {
      // Check if the current user has the MANAGE_SUPER_ADMINS capability
      if (
        currentUserRole &&
        !hasCapability(currentUserRole, Capability.MANAGE_SUPER_ADMINS)
      ) {
        throw new ForbiddenException(
          'You do not have permission to manage superAdmin users',
        );
      }
    }

    // Check if we're changing from admin to superAdmin or vice versa
    if (updateAdminDto.role && admin.admin.role !== updateAdminDto.role) {
      // Count superAdmins to prevent last superAdmin from being downgraded
      if (
        admin.admin.role === 'superAdmin' &&
        updateAdminDto.role === 'admin'
      ) {
        const superAdminCount = await this.prisma.admin.count({
          where: { role: 'superAdmin', user: { deletedAt: null } },
        });

        if (superAdminCount <= 1) {
          throw new BadRequestException('Cannot downgrade the last superAdmin');
        }
      }
    }

    // Extract fields we want to handle differently
    const { email, password } = updateAdminDto;

    // Handle password update if provided
    if (password) {
      // Update password in Cognito
      await this.cognitoService.overridePassword(admin.email, password);
    }

    // Handle email update if provided
    if (email && email !== admin.email) {
      // Check if email is already in use by another user
      const existingUser = await this.cognitoService.getUser(email);
      if (existingUser && existingUser['sub'] !== admin.id) {
        throw new ValidateInputError('Email is already in use by another user');
      }

      // Update email in Cognito
      await this.cognitoService.updateUserEmail(admin.email, email);
    }

    return this.prisma.user.update({
      where: { id },
      data: {
        firstName: updateAdminDto.firstName,
        lastName: updateAdminDto.lastName,
        email: updateAdminDto.email,
        admin: updateAdminDto.role
          ? { update: { role: updateAdminDto.role } }
          : undefined,
      },
      include: { admin: { select: { role: true } } },
    });
  }

  async deactivate(
    id: string,
    currentUserRole?: AdminRole,
    currentUserId?: string,
  ) {
    // Prevent deactivating yourself
    if (id === currentUserId) {
      throw new BadRequestException('You cannot deactivate your own account');
    }

    const admin = await this.findOne(id);

    // Check if deactivating a superAdmin
    if (admin.admin.role === 'superAdmin') {
      // Check if the current user has the MANAGE_SUPER_ADMINS capability
      if (
        currentUserRole &&
        !hasCapability(currentUserRole, Capability.MANAGE_SUPER_ADMINS)
      ) {
        throw new ForbiddenException(
          'You do not have permission to deactivate superAdmin users',
        );
      }

      // Check if this is the last superAdmin
      const superAdminCount = await this.prisma.admin.count({
        where: { role: 'superAdmin', user: { deletedAt: null } },
      });

      if (superAdminCount <= 1) {
        throw new BadRequestException('Cannot deactivate the last superAdmin');
      }
    }

    // Update the user with deletedAt timestamp
    const result = await this.prisma.user.update({
      where: { id },
      data: { deletedAt: new Date() },
      include: { admin: { select: { role: true } } },
    });

    try {
      // Invalidate all tokens for this user
      if (admin.email) {
        await this.cognitoService.signOutGlobally(admin.email);
      }
    } catch (error) {
      console.error(`Failed to sign out admin ${id} globally:`, error);
      // We don't throw here because the deactivation was successful
      // The token invalidation is a secondary action
    }

    return result;
  }

  async reactivate(id: string, currentUserRole?: AdminRole) {
    // Check if admin exists but is deleted
    const admin = await this.prisma.user.findFirst({
      where: { id, type: 'admin', deletedAt: { not: null } },
      include: { admin: { select: { role: true } } },
    });

    if (!admin) {
      throw new NotFoundException(
        `Admin with ID ${id} not found or is not deactivated`,
      );
    }

    // Check if reactivating a superAdmin
    if (admin.admin?.role === 'superAdmin') {
      // Check if the current user has the MANAGE_SUPER_ADMINS capability
      if (
        currentUserRole &&
        !hasCapability(currentUserRole, Capability.MANAGE_SUPER_ADMINS)
      ) {
        throw new ForbiddenException(
          'You do not have permission to reactivate superAdmin users',
        );
      }
    }

    // Check if email is now used by another active user
    const existingUser = await this.prisma.user.findFirst({
      where: { email: admin.email, id: { not: id }, deletedAt: null },
    });

    if (existingUser) {
      throw new ConflictException(
        `Cannot reactivate admin because email ${admin.email} is now used by another user`,
      );
    }

    return this.prisma.user.update({
      where: { id },
      data: { deletedAt: null },
      include: { admin: { select: { role: true } } },
    });
  }

  async delete(id: string, currentUserId: string, currentUserRole?: AdminRole) {
    // Prevent deleting yourself
    if (id === currentUserId) {
      throw new BadRequestException('You cannot delete your own account');
    }

    const admin = await this.findOne(id);

    // Check if deleting a superAdmin
    if (admin.admin.role === 'superAdmin') {
      // Check if the current user has the MANAGE_SUPER_ADMINS capability
      if (
        currentUserRole &&
        !hasCapability(currentUserRole, Capability.MANAGE_SUPER_ADMINS)
      ) {
        throw new ForbiddenException(
          'You do not have permission to delete superAdmin users',
        );
      }

      // Check if this is the last superAdmin
      const superAdminCount = await this.prisma.admin.count({
        where: { role: 'superAdmin', user: { deletedAt: null } },
      });

      if (superAdminCount <= 1) {
        throw new BadRequestException('Cannot delete the last superAdmin');
      }
    }

    try {
      // Permanently delete the admin and related data
      await this.prisma.$transaction([
        // Delete admin role record
        this.prisma.admin.delete({
          where: { userId: id },
        }),
        // Delete user record
        this.prisma.user.delete({
          where: { id },
        }),
      ]);

      // Delete the Cognito user
      await this.cognitoService.deleteUser(admin.email);

      return { success: true, message: 'Administrator permanently deleted' };
    } catch (error) {
      console.error(`Error deleting admin ${id}:`, error);
      throw new BadRequestException(
        `Failed to delete administrator: ${error.message}`,
      );
    }
  }
}
