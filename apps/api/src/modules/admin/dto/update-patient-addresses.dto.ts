import { specialCharsRegex } from '@/modules/patient/dto/patient-sign-up.dto';
import { Type } from 'class-transformer';
import {
  IsBoolean,
  IsDefined,
  IsNotEmpty,
  IsNotEmptyObject,
  IsObject,
  IsOptional,
  Matches,
  MaxLength,
  ValidateIf,
  ValidateNested,
} from 'class-validator';

class AddressDto {
  @IsNotEmpty()
  @Matches(specialCharsRegex, {
    message: 'Address 1 contains invalid characters',
  })
  @MaxLength(35)
  address1: string;

  @IsOptional()
  @Matches(specialCharsRegex, {
    message: 'Address 2 contains invalid characters',
  })
  @MaxLength(35)
  address2?: string;

  @IsNotEmpty()
  @Matches(specialCharsRegex, { message: 'City contains invalid characters' })
  @MaxLength(35)
  city: string;

  @IsNotEmpty()
  zip: string;
}

export class UpdatePatientAddressesDto {
  @IsDefined()
  @IsNotEmptyObject()
  @IsObject()
  @ValidateNested()
  @Type(() => AddressDto)
  shippingAddress!: AddressDto;

  @ValidateIf((o) => o.isBillingIsSameAsShipping === false)
  @IsDefined()
  @IsNotEmptyObject()
  @IsObject()
  @ValidateNested()
  @Type(() => AddressDto)
  billingAddress: AddressDto;

  @IsBoolean()
  isBillingIsSameAsShipping!: boolean;
}
