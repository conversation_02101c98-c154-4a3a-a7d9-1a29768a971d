import { CacheService } from '@modules/cache/cache.service';
import { PrismaService } from '@modules/prisma/prisma.service';
import { Injectable, NotFoundException } from '@nestjs/common';

interface ImpersonationData {
  userId: string;
  role: string;
  impersonated: boolean;
}

@Injectable()
export class AuthService {
  private localCache = new Map<string, ImpersonationData | null>();

  constructor(
    private readonly cacheService: CacheService,
    private readonly prismaService: PrismaService,
  ) {}

  async validateImpersonationRequest(key: string) {
    const data = await this.cacheService.get<{
      accessToken: string;
      userId: string;
      role: string;
      impersonated: true;
    }>(`impersonateRequest:${key}`);

    if (!data) {
      throw new NotFoundException('Invalid or expired impersonation token');
    }
    const userId = data.userId;

    const role = data.role[0].toUpperCase() + data.role.slice(1);

    await this.cacheService.set(
      `impersonate:${data.accessToken}`,
      {
        userId: userId,
        role: role,
        impersonated: true,
      },
      3600,
    );

    this.localCache.set(data.accessToken, {
      userId,
      role,
      impersonated: true,
    });

    const payload = {
      accessToken: data.accessToken,
      refreshToken: data.accessToken,
      impersonated: true,
    };
    if (role === 'Patient') {
      const { status } = await this.prismaService.patient.findFirstOrThrow({
        where: { userId },
        select: { status: true },
      });
      payload['status'] = status;
    }
    return payload;
  }

  async getImpersonationData(token: string): Promise<ImpersonationData | null> {
    // Check local cache first
    if (this.localCache.has(token)) {
      return this.localCache.get(token) ?? null;
    }

    // If not in local cache, check Redis
    const data = await this.cacheService.get<ImpersonationData>(
      `impersonate:${token}`,
    );

    // Update local cache (even if null)
    this.localCache.set(token, data ?? null);

    return data ?? null;
  }
}
