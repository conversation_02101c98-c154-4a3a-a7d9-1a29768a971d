import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as jwt from 'jsonwebtoken';
import * as jwksClient from 'jwks-rsa';

@Injectable()
export class JwtService {
  private jwksClient: jwksClient.JwksClient;
  constructor(configService: ConfigService) {
    const region = configService.get('AWS_REGION');
    const userPoolId = configService.get('COGNITO_USER_POOL_ID');
    this.jwksClient = jwksClient({
      jwksUri: `https://cognito-idp.${region}.amazonaws.com/${userPoolId}/.well-known/jwks.json`,
    });
  }
  async verifyToken(token: string): Promise<any> {
    // Decode the token to get the kid
    const decoded = jwt.decode(token, { complete: true });
    if (!decoded || typeof decoded === 'string') {
      throw new Error('Invalid token');
    }

    const key = await this.jwksClient.getSigningKey(decoded.header.kid);
    const signingKey = key.getPublicKey();

    // Verify the token
    return new Promise((resolve, reject) => {
      jwt.verify(
        token,
        signingKey,
        { algorithms: ['RS256'] },
        (err, decoded) => {
          if (err) {
            reject(err);
          } else {
            resolve(decoded);
          }
        },
      );
    });
  }
}
