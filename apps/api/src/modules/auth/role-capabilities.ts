import { AdminRole } from '@prisma/client';

import {
  AdminRole as AuthAdminRole,
  Capability,
  hasAllCapabilities,
  hasAnyCapability,
  hasCapability,
  ROLE_CAPABILITIES,
} from '@willow/auth';

/**
 * This file re-exports the role-based capabilities functionality from the auth package.
 *
 * IMPORTANT: The AdminRole from Prisma should always match the AdminRole enum in the auth package.
 * This ensures that the capabilities system works correctly across the entire application.
 *
 * If the Prisma schema's AdminRole enum is updated, the corresponding enum in the auth package
 * must also be updated to maintain consistency.
 */

// Verify that Prisma's AdminRole values match the auth package's AdminRole values
// This is a compile-time check to ensure the enums remain in sync
type PrismaAdminRoleKeys = keyof typeof AdminRole;
type AuthAdminRoleKeys = keyof typeof AuthAdminRole;

// This type assertion ensures that all Prisma admin roles exist in the auth package
// If this fails, it means the auth package's AdminRole enum needs to be updated
// to match the Prisma schema
type _EnsureAllPrismaRolesExistInAuth = {
  [K in PrismaAdminRoleKeys]: K extends AuthAdminRoleKeys ? K : never;
};

// This type assertion ensures that all auth package roles exist in Prisma
// If this fails, it means the Prisma schema's AdminRole enum needs to be updated
// to match the auth package
type _EnsureAllAuthRolesExistInPrisma = {
  [K in AuthAdminRoleKeys]: K extends PrismaAdminRoleKeys ? K : never;
};

// Re-export capabilities and functions from the auth package
export {
  Capability,
  ROLE_CAPABILITIES,
  hasCapability,
  hasAnyCapability,
  hasAllCapabilities,
};
