import { AuthService } from '@modules/auth/auth.service';
import { JwtService } from '@modules/auth/jwt.service';
import { Socket } from 'socket.io';

export type SocketIOMiddleware = {
  (client: Socket, next: (err?: Error) => void);
};

export const WebsocketAuthMiddleware = (
  jwtService: JwtService,
  authService: AuthService,
): SocketIOMiddleware => {
  return async (client, next) => {
    try {
      const token =
        client.handshake.headers?.authorization?.split(' ')?.[1] ??
        client.handshake.auth?.token;

      if (!token) {
        throw new Error('No token provided');
      }

      // Verify the JWT token
      const payload = await jwtService.verifyToken(token);

      if (!payload['cognito:groups']) {
        throw new Error('Invalid token: no groups found');
      }

      // Check for impersonation
      const impersonationData = await authService.getImpersonationData(token);

      if (
        impersonationData &&
        ['Patient', 'Doctor'].includes(impersonationData.role)
      ) {
        client['user'] = {
          userId: impersonationData.userId,
          impersonatedBy: payload.sub,
          role: impersonationData.role,
        };
      } else {
        client['user'] = {
          userId: payload.sub,
          role: payload['cognito:groups'][0],
        };
      }

      next();
    } catch (e) {
      next(e);
    }
  };
};
