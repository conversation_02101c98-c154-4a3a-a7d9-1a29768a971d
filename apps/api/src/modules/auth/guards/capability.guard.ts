import { PrismaService } from '@modules/prisma/prisma.service';
import {
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Injectable,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';

import { hasAnyCapability } from '@willow/auth';

export const REQUIRED_CAPABILITIES_KEY = 'requiredCapabilities';

@Injectable()
export class CapabilityGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private prisma: PrismaService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredCapabilities = this.reflector.getAllAndOverride<any[]>(
      REQUIRED_CAPABILITIES_KEY,
      [context.getHandler(), context.getClass()],
    );

    // If no capabilities are required, allow access
    if (!requiredCapabilities || requiredCapabilities.length === 0) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    // Since this guard runs after the AuthGuard, user should be populated
    if (!user) {
      console.warn('CapabilityGuard: User not found in request');
      return false;
    }

    // First check if user has Admin role
    const hasAdminRole = user.role === 'Admin';
    if (!hasAdminRole) {
      throw new ForbiddenException('Admin role required');
    }

    try {
      // Query the database to get the admin's role
      const adminUser = await this.prisma.user.findFirstOrThrow({
        where: { id: user.userId, type: 'admin' },
        include: { admin: { select: { role: true } } },
      });

      const adminRole = adminUser.admin?.role;

      // Check if the admin has any of the required capabilities
      const hasRequiredCapability = hasAnyCapability(
        adminRole as any,
        requiredCapabilities,
      );

      if (!hasRequiredCapability) {
        throw new ForbiddenException('Insufficient privileges');
      }

      return true;
    } catch (error) {
      console.error('CapabilityGuard: Error fetching admin info:', error);
      throw new ForbiddenException('Insufficient privileges');
    }
  }
}
