import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';

import { Roles } from '../types/roles';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const methodRole = this.reflector.get<Roles[]>(
      'roles',
      context.getHandler(),
    );
    const controllerRole = this.reflector.get<Roles[]>(
      'roles',
      context.getClass(),
    );
    const roles = methodRole ?? controllerRole;

    const requireSuperAdmin = this.reflector.getAllAndOverride<boolean>(
      'requireSuperAdmin',
      [context.getHandler(), context.getClass()],
    );

    if (!roles && !requireSuperAdmin) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user; // Assuming user is attached to the request after successful Passport authentication

    // Check admin role first
    if (roles && user.role && !roles.includes(user.role)) {
      throw new UnauthorizedException('Insufficient privileges');
    }

    // If super admin is required, check if user is a super admin
    if (requireSuperAdmin) {
      const isSuperAdmin = user.admin?.role === 'superAdmin';
      if (!isSuperAdmin) {
        throw new UnauthorizedException('Super admin privileges required');
      }
    }

    return true;
  }
}
