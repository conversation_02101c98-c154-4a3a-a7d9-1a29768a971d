import {
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Injectable,
} from '@nestjs/common';
import { Request } from 'express';

@Injectable()
export class PreventImpersonatedEditGuard implements CanActivate {
  constructor() {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest<Request>();
    const user = request.user as { impersonatedBy?: string };

    // If not impersonated, allow all actions
    if (!user?.impersonatedBy) {
      return true;
    }

    const method = request.method.toUpperCase();
    if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(method)) {
      throw new ForbiddenException(
        'Impersonated users cannot perform write operations',
      );
    }
    return true;
  }
}
