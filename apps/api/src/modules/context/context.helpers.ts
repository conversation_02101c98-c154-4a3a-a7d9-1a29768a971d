export function getClientIp(req: any): string {
  // Get X-Forwarded-For header
  const forwardedFor = req.headers['x-forwarded-for'];

  if (forwardedFor) {
    // X-Forwarded-For can contain multiple IPs in a comma-separated list
    // The leftmost IP is typically the original client IP
    const ips = forwardedFor.split(',').map((ip) => ip.trim());
    return ips[0];
  }

  // cloudflare headers
  const cfIp = req.headers['cf-connecting-ip'];
  if (cfIp) return cfIp;

  // Fallback to other methods if X-Forwarded-For is not available
  return req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
}
