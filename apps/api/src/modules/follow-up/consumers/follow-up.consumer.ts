import { PrismaService } from '@/modules/prisma/prisma.service';
import { SnsConsume } from '@/modules/shared/aws/sns/sns.decorator';
import { FollowUpUpdatedQueueEvent } from '@/modules/shared/events/follow-up-topic.definition';
import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class FollowUpConsumer {
  private logger = new Logger(FollowUpConsumer.name);

  constructor(private readonly prisma: PrismaService) {}

  @SnsConsume({
    topic: 'follow-up-updated',
    consumerGroup: 'follow-up-treatment-autoprescribe',
    filter: ['completed-by-patient'],
  })
  async handleFollowUpCompletion({
    payload: { followUp },
  }: FollowUpUpdatedQueueEvent) {
    try {
      // const autoPrescribe =
      //   await this.followUpService.calculateAutoPrescribe(state);
      const autoPrescribe = 'no';

      await this.prisma.patientFollowUp.update({
        where: { id: followUp.id },
        data: { autoPrescribe },
      });
    } catch (error) {
      console.error('Error processing follow-up completion:', error);
    }
  }
}
