import { segmentTrackEvents } from '@/modules/shared/events';
import { SegmentTrack } from '@/modules/shared/types/events';
import { PatientFollowUpProfile } from '@adapters/persistence/database/patient.persistence';
import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';

import { HandleFollowUpNpsSurveyEventDto } from '../dto/handle-follow-up-nps-survey-event.dto';

@Injectable()
export class HandleFollowUpNpsSurveyEventUseCase {
  constructor(private readonly eventEmitter: EventEmitter2) {}

  async execute(
    patient: PatientFollowUpProfile,
    data: HandleFollowUpNpsSurveyEventDto,
  ) {
    this.eventEmitter.emit(segmentTrackEvents.followUpNpsSurveyResponse.event, {
      event: segmentTrackEvents.followUpNpsSurveyResponse.name,
      userId: patient.userId,
      properties: {
        value: data.rating,
        source: 'Follow Up',
      },
    } satisfies SegmentTrack);

    return true;
  }
}
