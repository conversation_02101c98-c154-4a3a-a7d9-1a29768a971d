import { Injectable, Logger } from '@nestjs/common';
import { <PERSON><PERSON> } from '@nestjs/schedule';

import { OrchestrationService } from '../shared/orchestration/orchestration.service';
import { SendFollowUpNotificationsJob } from './jobs/send-follow-up-notifications.job';

@Injectable()
export class FollowUpWorker {
  private readonly logger = new Logger(FollowUpWorker.name);
  private readonly disabled: boolean;

  constructor(
    private readonly sendFollowUpNotificationsJob: SendFollowUpNotificationsJob,
    private readonly orchestrationService: OrchestrationService,
  ) {
    // Check IS_CLI first, if true, disable this queue
    this.disabled =
      process.env.IS_CLI === 'true' ||
      (process.env.ENVIRONMENT !== 'local' &&
        !!process.env.NEXT_PUBLIC_ENVIRONMENT);
  }

  @Cron('0 8-18 * * *', { timeZone: 'America/New_York' }) // every hour between 8am and 6pm EST
  async sendNotifications(): Promise<void> {
    if (this.disabled) return;

    try {
      await this.orchestrationService.runWithLock(
        {
          lockKey: 'sendFollowUpNotifications-cron',
          ttl: 1000 * 60 * 10, // 10 minutes
          thisInstanceMustBePrimary: true,
        },
        async () => {
          await this.sendFollowUpNotificationsJob.run();
        },
      );
    } catch (error) {
      this.logger.error('Error in sendNotifications cron job', error);
    }
  }
}
