import os from 'os';
import {
  PrismaService,
  PrismaTransactionalClient,
} from '@modules/prisma/prisma.service';
import { Injectable, Logger } from '@nestjs/common';

import { AuditLogInsert } from '@willow/utils/audit-log';
import { ActorExtraDetails } from '@willow/utils/src/audit-log/types';

@Injectable()
export class AuditService {
  private readonly logger = new Logger('AuditService');

  constructor(private readonly prismaService: PrismaService) {}

  async append(
    data: AuditLogInsert,
    options: { prisma?: PrismaTransactionalClient } = {},
  ) {
    try {
      const db = options.prisma ?? this.prismaService;
      await db.auditLog.create({
        data: {
          patientId: data.patientId,
          action: data.action,
          actorType: data.actorType,
          actorId: data.actorId,
          actorExtraDetails:
            (await this.getActorDetails(data.actorType, data.actorId)) ?? {},
          resourceType: data.resourceType,
          resourceId: data.resourceId,
          details: data.details,
        },
      });
    } catch (error) {
      this.logger.error('Failed to log action', error, data);
      if (options.prisma) {
        throw error;
      }
    }
  }

  async getActorDetails(
    actorType: AuditLogInsert['actorType'],
    actorId: string,
  ): Promise<ActorExtraDetails | null> {
    try {
      switch (actorType) {
        case 'ADMIN':
        case 'PATIENT':
        case 'DOCTOR':
          return this.prismaService.user
            .findFirst({
              where: {
                ...(actorType === 'PATIENT' && { patient: { id: actorId } }),
                ...(actorType === 'DOCTOR' && { doctor: { id: actorId } }),
                ...(actorType === 'ADMIN' && { admin: { id: actorId } }),
              },
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            })
            .then((user) => {
              if (!user) {
                return null;
              }

              return {
                userId: user.id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
              };
            });
        case 'COMMAND':
          return {
            computer_username: os.userInfo().username,
          };
        default:
          return null;
      }
    } catch (error) {
      this.logger.error('Failed to get actor details', error);
      return null;
    }
  }
}
