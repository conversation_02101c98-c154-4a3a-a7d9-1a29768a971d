/**
 * Base class for pharmacy-related errors
 * @property isRetryable - Indicates whether the error should trigger a retry via SNS/SQS
 * @property pharmacyName - Name of the pharmacy that caused the error
 */
export class PharmacyError extends Error {
  constructor(
    message: string,
    public readonly isRetryable: boolean,
    public readonly pharmacyName: string,
  ) {
    super(message);
    this.name = 'PharmacyError';
  }
}

export class PharmacyTemporaryError extends PharmacyError {
  constructor(message: string, pharmacyName: string) {
    super(message, true, pharmacyName);
    this.name = 'PharmacyTemporaryError';
  }
}

export class PharmacyPermanentError extends PharmacyError {
  constructor(message: string, pharmacyName: string) {
    super(message, false, pharmacyName);
    this.name = 'PharmacyPermanentError';
  }
}
