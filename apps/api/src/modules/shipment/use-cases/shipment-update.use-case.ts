import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable, Logger } from '@nestjs/common';
import { subDays } from 'date-fns';
import { camelCase } from 'lodash';

import { ShipmentUpdateDto } from '../dto/shipment-update.dto';

@Injectable()
export class ShipmentUpdateUseCase {
  private readonly logger = new Logger(ShipmentUpdateUseCase.name);

  // Map incoming pharmacy names to database slugs
  private readonly pharmacyMap: Record<string, string> = {
    Empower: 'empower',
    'Red Rock': 'redRock',
    RedRock: 'redRock',
    Epiq: 'epiqScripts',
    EpiqScripts: 'epiqScripts',
    Strive: 'strive',
    StriveRX: 'strive',
    Boothwyn: 'boothwyn',
    Partell: 'partell',
  };

  constructor(private readonly prisma: PrismaService) {}

  async execute(data: ShipmentUpdateDto) {
    // Transform webhook payload to expected format
    const patientId = data.userId;
    const trackingNumber = data.properties.trackingNumber;
    const trackingLink = data.properties.trackingLink;
    const status = camelCase(data.properties.orderStatus.replace(/^LS-/, ''));
    const incomingPharmacyName = data.properties.pharmacy;

    // Verify patient exists
    const patient = await this.prisma.patient.findUnique({
      where: { id: patientId },
    });

    if (!patient) {
      this.logger.warn(
        `Patient not found with ID: ${patientId}. Skipping shipment update.`,
      );
      return {
        success: false,
        message: `Patient not found with ID: ${patientId}`,
      };
    }

    // Map the incoming pharmacy name to our database slug
    const pharmacySlug = this.pharmacyMap[incomingPharmacyName];
    let pharmacyId: string | null = null;

    if (pharmacySlug) {
      // Find the pharmacy by slug
      const pharmacy = await this.prisma.pharmacy.findUnique({
        where: { slug: pharmacySlug },
      });

      if (pharmacy) {
        pharmacyId = pharmacy.id;
      } else {
        this.logger.warn(`Pharmacy not found with slug: ${pharmacySlug}`);
      }
    } else {
      this.logger.warn(
        `Unknown pharmacy name in webhook: ${incomingPharmacyName}`,
      );
    }

    const existingRecord = await this.prisma.shipmentUpdate.findFirst({
      where: { patientId, trackingNumber },
    });

    if (existingRecord) {
      return await this.prisma.shipmentUpdate.update({
        where: { id: existingRecord.id },
        data: {
          status,
          trackingLink,
          pharmacyId,
          updatedAt: new Date(),
        },
      });
    } else {
      return await this.prisma.shipmentUpdate.create({
        data: {
          status,
          patientId,
          trackingNumber,
          trackingLink,
          pharmacyId,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });
    }
  }

  async getShipmentStatus(patientId: string) {
    return await this.prisma.shipmentUpdate.findMany({
      where: { patientId, updatedAt: { gte: subDays(new Date(), 10) } },
      orderBy: { updatedAt: 'desc' },
      take: 1,
    });
  }
}
