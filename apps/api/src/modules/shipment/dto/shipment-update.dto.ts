export interface ShipmentUpdateDto {
  type: string;
  context: {
    library: {
      name: string;
      version: string;
    };
  };
  userId: string;
  integrations: Record<string, any>;
  properties: {
    orderNo: string;
    trackingNumber: string;
    pharmacy: string;
    trackingLink: string;
    orderStatus: string;
  };
  projectId: string;
  timestamp: string;
  version: number;
  receivedAt: string;
  event: string;
  messageId: string;
  channel: string;
}
