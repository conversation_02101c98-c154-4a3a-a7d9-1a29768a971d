import {
  boolean,
  date,
  doublePrecision,
  integer,
  json,
  jsonb,
  pgEnum,
  pgTable,
  primaryKey,
  text,
  timestamp,
  uniqueIndex,
  varchar,
} from 'drizzle-orm/pg-core';

export const userType = pgEnum('UserType', ['patient', 'doctor', 'admin']);
export const patientGender = pgEnum('patientGender', ['female', 'male']);
export const patientStatus = pgEnum('patientStatus', [
  'onboardingPending',
  'onboardingRejected',
  'onboardingCompleted',
  'pendingApprovalFromDoctor',
  'pendingUploadPhotos',
  'pendingPrescription',
  'activePrescription',
  'nonActivePrescription',
  'cancelled',
  'banned',
]);
export const prescriptionStatus = pgEnum('prescriptionStatus', [
  'incomplete',
  'incomplete_expired',
  'trialing',
  'active',
  'past_due',
  'canceled',
  'unpaid',
]);
export const conversationStatus = pgEnum('ConversationStatus', [
  'active',
  'needsReply',
  'followup',
]);
export const conversationMessageType = pgEnum('ConversationMessageType', [
  'message',
  'system',
]);
export const conversationMessageContentType = pgEnum(
  'ConversationMessageContentType',
  ['text', 'image', 'file'],
);
export const adminRole = pgEnum('AdminRole', ['superAdmin', 'admin']);

export const state = pgTable('State', {
  id: text('id').primaryKey().notNull(),
  name: text('name'),
  code: varchar('code', { length: 2 }),
  enabled: boolean('enabled').default(false).notNull(),
});

export const patient = pgTable(
  'Patient',
  {
    id: text('id').primaryKey().notNull(),
    userId: text('userId')
      .notNull()
      .references(() => user.id, { onDelete: 'restrict', onUpdate: 'cascade' }),
    stateId: text('stateId')
      .notNull()
      .references(() => state.id, {
        onDelete: 'restrict',
        onUpdate: 'cascade',
      }),
    questionnaireId: text('questionnaireId')
      .notNull()
      .references(() => questionnaire.id, {
        onDelete: 'restrict',
        onUpdate: 'cascade',
      }),
    doctorId: text('doctorId').references(() => doctor.id, {
      onDelete: 'set null',
      onUpdate: 'cascade',
    }),
    stripeCustomerId: text('stripeCustomerId'),
    doseSpotPatientId: text('doseSpotPatientId'),
    birthDate: date('birthDate'),
    gender: patientGender('gender'),
    height: doublePrecision('height'),
    weight: doublePrecision('weight'),
    idPhoto: text('idPhoto'),
    facePhoto: text('facePhoto'),
    questionnaireAnswers: jsonb('questionnaireAnswers').default({}).notNull(),
    status: patientStatus('status').default('onboardingPending'),
    onboardingStatus: varchar('onboardingStatus', { length: 255 }),
    rejectedReason: text('rejectedReason'),
    getPromotionsSms: boolean('getPromotionsSMS').default(false).notNull(),
    createdAt: timestamp('createdAt', { precision: 3, mode: 'string' })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp('updatedAt', { precision: 3, mode: 'string' }),
    acceptedAt: timestamp('acceptedAt', { precision: 3, mode: 'string' }),
  },
  (table) => {
    return {
      userIdKey: uniqueIndex('Patient_userId_key').on(table.userId),
      stripeCustomerIdKey: uniqueIndex('Patient_stripeCustomerId_key').on(
        table.stripeCustomerId,
      ),
      doseSpotPatientIdKey: uniqueIndex('Patient_doseSpotPatientId_key').on(
        table.doseSpotPatientId,
      ),
    };
  },
);

export const questionnaire = pgTable(
  'Questionnaire',
  {
    id: text('id').primaryKey().notNull(),
    version: integer('version').default(1).notNull(),
    name: text('name').notNull(),
    description: text('description'),
    config: json('config').notNull(),
    createdAt: timestamp('createdAt', { precision: 3, mode: 'string' })
      .defaultNow()
      .notNull(),
  },
  (table) => {
    return {
      versionKey: uniqueIndex('Questionnaire_version_key').on(table.version),
    };
  },
);

export const patientShippingAddress = pgTable('PatientShippingAddress', {
  id: text('id').primaryKey().notNull(),
  patientId: text('patientId')
    .notNull()
    .references(() => patient.id, {
      onDelete: 'restrict',
      onUpdate: 'cascade',
    }),
  address1: text('address1').notNull(),
  address2: text('address2'),
  city: text('city').notNull(),
  stateId: text('stateId').notNull(),
  zip: text('zip').notNull(),
  default: boolean('default').default(false),
  createdAt: timestamp('createdAt', { precision: 3, mode: 'string' })
    .defaultNow()
    .notNull(),
});

export const patientPaymentMethod = pgTable(
  'PatientPaymentMethod',
  {
    id: text('id').primaryKey().notNull(),
    stripeId: text('stripeId').notNull(),
    patientId: text('patientId')
      .notNull()
      .references(() => patient.id, {
        onDelete: 'restrict',
        onUpdate: 'cascade',
      }),
    type: text('type').notNull(),
    brand: text('brand').notNull(),
    last4: text('last4').notNull(),
    expMonth: integer('expMonth').notNull(),
    expYear: integer('expYear').notNull(),
    default: boolean('default').default(false).notNull(),
    createdAt: timestamp('createdAt', { precision: 3, mode: 'string' })
      .defaultNow()
      .notNull(),
  },
  (table) => {
    return {
      stripeIdKey: uniqueIndex('PatientPaymentMethod_stripeId_key').on(
        table.stripeId,
      ),
    };
  },
);

export const user = pgTable(
  'User',
  {
    id: text('id').primaryKey().notNull(),
    type: userType('type').default('patient').notNull(),
    firstName: text('firstName').notNull(),
    lastName: text('lastName').notNull(),
    email: text('email').notNull(),
    phone: text('phone'),
    createdAt: timestamp('createdAt', { precision: 3, mode: 'string' })
      .defaultNow()
      .notNull(),
  },
  (table) => {
    return {
      emailKey: uniqueIndex('User_email_key').on(table.email),
    };
  },
);

export const doctorInviteCode = pgTable(
  'DoctorInviteCode',
  {
    id: text('id').primaryKey().notNull(),
    doctorId: text('doctorId').references(() => doctor.id, {
      onDelete: 'set null',
      onUpdate: 'cascade',
    }),
    code: varchar('code', { length: 8 }).notNull(),
    generatedByAdmin: text('generatedByAdmin')
      .notNull()
      .references(() => admin.id, {
        onDelete: 'restrict',
        onUpdate: 'cascade',
      }),
    createdAt: timestamp('createdAt', { precision: 3, mode: 'string' })
      .defaultNow()
      .notNull(),
    usedAt: timestamp('usedAt', { precision: 3, mode: 'string' }),
  },
  (table) => {
    return {
      doctorIdKey: uniqueIndex('DoctorInviteCode_doctorId_key').on(
        table.doctorId,
      ),
      codeKey: uniqueIndex('DoctorInviteCode_code_key').on(table.code),
    };
  },
);

export const conversation = pgTable(
  'Conversation',
  {
    id: text('id').primaryKey().notNull(),
    userId: text('userId')
      .notNull()
      .references(() => user.id, { onDelete: 'restrict', onUpdate: 'cascade' }),
    status: conversationStatus('status').default('active').notNull(),
    lastMessageText: text('lastMessageText').default('').notNull(),
    createdAt: timestamp('createdAt', { precision: 3, mode: 'string' })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp('updatedAt', { precision: 3, mode: 'string' }),
  },
  (table) => {
    return {
      userIdKey: uniqueIndex('Conversation_userId_key').on(table.userId),
    };
  },
);

export const product = pgTable(
  'Product',
  {
    id: text('id').primaryKey().notNull(),
    stripeId: text('stripeId').notNull(),
    name: text('name').notNull(),
    image: text('image'),
    description: text('description'),
    stripePayload: jsonb('stripePayload'),
    createdAt: timestamp('createdAt', { precision: 3, mode: 'string' })
      .defaultNow()
      .notNull(),
  },
  (table) => {
    return {
      stripeIdKey: uniqueIndex('Product_stripeId_key').on(table.stripeId),
    };
  },
);

export const prescription = pgTable(
  'Prescription',
  {
    id: text('id').primaryKey().notNull(),
    doctorId: text('doctorId')
      .notNull()
      .references(() => doctor.id, {
        onDelete: 'restrict',
        onUpdate: 'cascade',
      }),
    patientId: text('patientId')
      .notNull()
      .references(() => patient.id, {
        onDelete: 'restrict',
        onUpdate: 'cascade',
      }),
    productId: text('productId')
      .notNull()
      .references(() => product.id, {
        onDelete: 'restrict',
        onUpdate: 'cascade',
      }),
    doseSpotPrescriptionId: text('doseSpotPrescriptionId'),
    stripeSubscriptionId: text('stripeSubscriptionId'),
    status: prescriptionStatus('status').default('incomplete'),
    stripePayload: jsonb('stripePayload'),
    createdAt: timestamp('createdAt', { precision: 3, mode: 'string' })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp('updatedAt', { precision: 3, mode: 'string' }),
  },
  (table) => {
    return {
      doseSpotPrescriptionIdKey: uniqueIndex(
        'Prescription_doseSpotPrescriptionId_key',
      ).on(table.doseSpotPrescriptionId),
      stripeSubscriptionIdKey: uniqueIndex(
        'Prescription_stripeSubscriptionId_key',
      ).on(table.stripeSubscriptionId),
    };
  },
);

export const conversationWatcher = pgTable(
  'ConversationWatcher',
  {
    id: text('id').primaryKey().notNull(),
    conversationId: text('conversationId')
      .notNull()
      .references(() => conversation.id, {
        onDelete: 'restrict',
        onUpdate: 'cascade',
      }),
    userId: text('userId')
      .notNull()
      .references(() => user.id, { onDelete: 'restrict', onUpdate: 'cascade' }),
    unreadMessages: integer('unreadMessages').default(0).notNull(),
    updatedAt: timestamp('updatedAt', { precision: 3, mode: 'string' })
      .defaultNow()
      .notNull(),
  },
  (table) => {
    return {
      conversationIdUserIdKey: uniqueIndex(
        'ConversationWatcher_conversationId_userId_key',
      ).on(table.conversationId, table.userId),
    };
  },
);

export const conversationMessage = pgTable('ConversationMessage', {
  id: text('id').primaryKey().notNull(),
  conversationId: text('conversationId')
    .notNull()
    .references(() => conversation.id, {
      onDelete: 'restrict',
      onUpdate: 'cascade',
    }),
  userId: text('userId').references(() => user.id, {
    onDelete: 'set null',
    onUpdate: 'cascade',
  }),
  type: conversationMessageType('type').default('message').notNull(),
  contentType: conversationMessageContentType('contentType')
    .default('text')
    .notNull(),
  content: text('content').notNull(),
  createdAt: timestamp('createdAt', { precision: 3, mode: 'string' })
    .defaultNow()
    .notNull(),
});

export const patientWaitingList = pgTable(
  'PatientWaitingList',
  {
    id: text('id').primaryKey().notNull(),
    email: text('email'),
    createdAt: timestamp('createdAt', { precision: 3, mode: 'string' })
      .defaultNow()
      .notNull(),
  },
  (table) => {
    return {
      emailKey: uniqueIndex('PatientWaitingList_email_key').on(table.email),
    };
  },
);

export const doctor = pgTable(
  'Doctor',
  {
    id: text('id').primaryKey().notNull(),
    userId: text('userId')
      .notNull()
      .references(() => user.id, { onDelete: 'restrict', onUpdate: 'cascade' }),
    doseSpotClinicianId: text('doseSpotClinicianId'),
    address1: text('address1').notNull(),
    address2: text('address2'),
    city: text('city').notNull(),
    stateId: text('stateId')
      .notNull()
      .references(() => state.id, {
        onDelete: 'restrict',
        onUpdate: 'cascade',
      }),
    zip: text('zip').notNull(),
    createdAt: timestamp('createdAt', { precision: 3, mode: 'string' })
      .defaultNow()
      .notNull(),
  },
  (table) => {
    return {
      userIdKey: uniqueIndex('Doctor_userId_key').on(table.userId),
      doseSpotClinicianIdKey: uniqueIndex('Doctor_doseSpotClinicianId_key').on(
        table.doseSpotClinicianId,
      ),
    };
  },
);

export const admin = pgTable(
  'Admin',
  {
    id: text('id').primaryKey().notNull(),
    userId: text('userId')
      .notNull()
      .references(() => user.id, { onDelete: 'restrict', onUpdate: 'cascade' }),
    role: adminRole('role').default('admin').notNull(),
    createdAt: timestamp('createdAt', {
      precision: 3,
      mode: 'string',
    }).defaultNow(),
  },
  (table) => {
    return {
      userIdKey: uniqueIndex('Admin_userId_key').on(table.userId),
    };
  },
);

export const patientDesiredTreatment = pgTable(
  'PatientDesiredTreatment',
  {
    patientId: text('patientId')
      .notNull()
      .references(() => patient.id, {
        onDelete: 'restrict',
        onUpdate: 'cascade',
      }),
    productId: text('productId')
      .notNull()
      .references(() => product.id, {
        onDelete: 'restrict',
        onUpdate: 'cascade',
      }),
  },
  (table) => {
    return {
      patientDesiredTreatmentPkey: primaryKey({
        columns: [table.patientId, table.productId],
        name: 'PatientDesiredTreatment_pkey',
      }),
    };
  },
);

export const doctorsOnState = pgTable(
  'DoctorsOnState',
  {
    doctorId: text('doctorId')
      .notNull()
      .references(() => doctor.id, {
        onDelete: 'restrict',
        onUpdate: 'cascade',
      }),
    stateId: text('stateId')
      .notNull()
      .references(() => state.id, {
        onDelete: 'restrict',
        onUpdate: 'cascade',
      }),
    limit: integer('limit'),
  },
  (table) => {
    return {
      doctorsOnStatePkey: primaryKey({
        columns: [table.doctorId, table.stateId],
        name: 'DoctorsOnState_pkey',
      }),
    };
  },
);

export const doctorAssignment = pgTable(
  'DoctorAssignment',
  {
    doctorId: text('doctorId')
      .notNull()
      .references(() => doctor.id, {
        onDelete: 'restrict',
        onUpdate: 'cascade',
      }),
    patientId: text('patientId')
      .notNull()
      .references(() => patient.id, {
        onDelete: 'restrict',
        onUpdate: 'cascade',
      }),
    reasonForReassignment: text('reasonForReassignment'),
    createdAt: timestamp('createdAt', { precision: 3, mode: 'string' })
      .defaultNow()
      .notNull(),
  },
  (table) => {
    return {
      doctorAssignmentPkey: primaryKey({
        columns: [table.doctorId, table.patientId],
        name: 'DoctorAssignment_pkey',
      }),
    };
  },
);
