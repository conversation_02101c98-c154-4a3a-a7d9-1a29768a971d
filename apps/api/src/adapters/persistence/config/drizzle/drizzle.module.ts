import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { drizzle } from 'drizzle-orm/node-postgres';
import { Client } from 'pg';

import * as schema from './schema';

export const PG_CONNECTION = 'PG_CONNECTION';
@Module({
  providers: [
    {
      provide: PG_CONNECTION,
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        const user = configService.get<string>('POSTGRES_USER');
        const password = configService.get<string>('POSTGRES_PASSWORD');
        const port = configService.get<string>('POSTGRES_PORT');
        const host = configService.get<string>('POSTGRES_HOST');
        const database = configService.get<string>('POSTGRES_DB');
        const client = new Client({
          host,
          user,
          password,
          database,
          port: parseInt(port),
        });
        await client.connect();
        return drizzle(client, { schema });
      },
    },
  ],
  exports: [PG_CONNECTION],
})
export class DrizzleModule {}
