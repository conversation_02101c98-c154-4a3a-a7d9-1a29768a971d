import { ExecutionContext } from '@/helpers/transaction';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';

@Injectable()
export class PatientPaymentMethodPersistence {
  constructor(private readonly prisma: PrismaService) {}

  async create(
    data: Prisma.PatientPaymentMethodCreateInput,
    ctx: ExecutionContext = {
      prisma: this.prisma,
    },
  ) {
    return ctx.prisma.patientPaymentMethod.upsert({
      where: {
        stripeId: data.stripeId,
      },
      update: {
        ...data,
      },
      create: {
        ...data,
      },
    });
  }

  async update(
    id: string,
    data: Prisma.PatientPaymentMethodUpdateInput,
    ctx: ExecutionContext = {
      prisma: this.prisma,
    },
  ) {
    return ctx.prisma.patientPaymentMethod.update({
      where: { id },
      data,
    });
  }
  async getDefaultPaymentmethod(
    patientId: string,
    ctx: ExecutionContext = {
      prisma: this.prisma,
    },
  ) {
    return ctx.prisma.patientPaymentMethod.findFirst({
      where: {
        patientId: patientId,
        default: true,
      },
    });
  }
}
