import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { questionnaireType } from '@prisma/client';

export type Questionnaire = Awaited<
  ReturnType<typeof QuestionnairePersistence.prototype.getLastQuestionnaire>
>;

@Injectable()
export class QuestionnairePersistence {
  constructor(private readonly prisma: PrismaService) {}

  async getLastQuestionnaire(type: questionnaireType) {
    return this.prisma.questionnaire.findFirstOrThrow({
      where: { type },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async getQuestionnaireByVersion(type: questionnaireType, version: number) {
    return this.prisma.questionnaire.findFirstOrThrow({
      where: { type, version },
    });
  }
}
