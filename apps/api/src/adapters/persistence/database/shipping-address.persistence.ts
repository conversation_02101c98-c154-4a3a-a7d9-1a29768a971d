import {
  PrismaService,
  PrismaTransactionalClient,
} from '@/modules/prisma/prisma.service';
import { ShippingInfo } from '@modules/onboarding/types/shipping-info.type';
import { Injectable } from '@nestjs/common';

@Injectable()
export class ShippingAddressPersistence {
  constructor(private readonly prisma: PrismaService) {}

  async create(
    patientId: string,
    shippingInfo: ShippingInfo,
    client?: PrismaTransactionalClient,
  ) {
    await (client || this.prisma).patientShippingAddress.create({
      data: {
        patientId,
        ...shippingInfo,
        default: true,
      },
    });
  }
}
