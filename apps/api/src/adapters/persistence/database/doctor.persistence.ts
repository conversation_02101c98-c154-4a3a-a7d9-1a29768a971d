import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';

export type DoctorProfile = Awaited<
  ReturnType<typeof DoctorPersistence.prototype.getProfile>
>;

@Injectable()
export class DoctorPersistence {
  constructor(private readonly prisma: PrismaService) {}

  async getProfile(userId: string) {
    return this.prisma.doctor.findFirstOrThrow({
      where: {
        user: {
          id: userId,
          type: 'doctor',
        },
      },
      include: {
        user: true,
        prescribesIn: true,
      },
    });
  }

  async getLastMessages(userId: string) {
    const conversations = await this.prisma.conversationWatcher.findMany({
      where: {
        userId: userId,
        unreadMessages: { gt: 0 },
        conversation: {
          OR: [{ lastMessageFrom: null }, { lastMessageFrom: { not: userId } }],
          patient: { doctor: { userId: userId } },
        },
      },
      include: {
        conversation: {
          include: {
            messages: {
              select: {
                id: true,
                content: true,
                type: true,
                contentType: true,
                userId: true,
              },
              where: { type: { in: ['message', 'doctorNote'] } },
              orderBy: { createdAt: 'desc' },
              take: 4,
            },
            lastMessageUser: true,
            patient: {
              include: {
                user: true,
              },
            },
          },
        },
      },
      orderBy: { updatedAt: 'asc' },
    });

    return conversations
      .map((c) => {
        const unreadMessages = c.conversation.messages
          .slice(0, c.unreadMessages)
          .reverse();

        const updated = {
          ...c,
          conversation: {
            ...c.conversation,
            unreadMessages,
            // Override lastMessageUser with patient info for system/doctorNote messages
            lastMessageUser:
              c.conversation.lastMessageUser || c.conversation.patient.user,
          },
        };

        delete updated.conversation.messages;
        delete updated.conversation.patient;

        return updated;
      })
      .sort((a, b) => {
        // priority: (conversation) doctorAdmin > (message) doctorNote > others
        if (
          a.conversation.type === 'doctorAdmin' &&
          b.conversation.type !== 'doctorAdmin'
        )
          return -1;
        if (
          a.conversation.type !== 'doctorAdmin' &&
          b.conversation.type === 'doctorAdmin'
        )
          return 1;
        const aLast = a.conversation.unreadMessages.at(-1);
        const bLast = b.conversation.unreadMessages.at(-1);
        if (aLast?.type === 'doctorNote' && bLast?.type !== 'doctorNote')
          return -1;
        if (aLast?.type !== 'doctorNote' && bLast?.type === 'doctorNote')
          return 1;
        return 0;
      });
  }
}
