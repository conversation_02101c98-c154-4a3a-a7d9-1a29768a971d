import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class ProductPersistence {
  constructor(private readonly prisma: PrismaService) {}
  async getActiveWithDefaultPriceByIds(ids: string[]) {
    return this.prisma.product.findMany({
      where: {
        id: { in: ids },
        active: true,
      },
      include: {
        defaultPrice: true,
      },
    });
  }

  async getActiveWithDefaultPrice() {
    return this.prisma.product.findMany({
      where: {
        active: true,
      },
      include: {
        defaultPrice: true,
      },
    });
  }
}
