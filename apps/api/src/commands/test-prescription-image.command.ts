import { writeFileSync } from 'fs';
import { join } from 'path';
import type {
  PharmacyPrescriptionProduct,
  PrescriptionRequest,
} from '@modules/integrations/pharmacy';
import { PrescriptionImageGeneratorService } from '@modules/integrations/pharmacy/services/prescription-image-generator.service';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Command, CommandRunner } from 'nest-commander';

@Injectable()
@Command({
  name: 'test-prescription-image',
  description: 'Test prescription image generation with sample data',
})
export class TestPrescriptionImageCommand extends CommandRunner {
  private readonly logger = new Logger(TestPrescriptionImageCommand.name);
  private readonly prescriptionImageGenerator: PrescriptionImageGeneratorService;

  constructor(configService: ConfigService) {
    super();

    // Create a custom ConfigService with the resourcesPath
    const customConfigService = {
      get: (key: string) => {
        if (key === 'resourcesPath') {
          return process.env.ENVIRONMENT !== 'local'
            ? '/app/apps/api/dist/assets'
            : join(process.cwd(), 'resources', 'assets');
        }
        return configService.get(key);
      },
    } as ConfigService;

    // Instantiate the service with our custom config
    this.prescriptionImageGenerator = new PrescriptionImageGeneratorService(
      customConfigService,
    );
    this.logger.log(
      'Initialized prescription image generator with custom resourcesPath',
    );
  }

  async run(): Promise<void> {
    console.log('🏥 Testing Prescription Image Generator\n');

    // Random data generators
    const firstNames = [
      'John',
      'Sarah',
      'Michael',
      'Emily',
      'David',
      'Jennifer',
      'Robert',
      'Lisa',
    ];
    const lastNames = [
      'Smith',
      'Johnson',
      'Williams',
      'Brown',
      'Davis',
      'Miller',
      'Wilson',
      'Moore',
    ];
    const streets = [
      'Main',
      'Oak',
      'Maple',
      'Cedar',
      'Pine',
      'Elm',
      'Washington',
      'Lincoln',
    ];
    const cities = [
      'Phoenix',
      'Scottsdale',
      'Tempe',
      'Mesa',
      'Chandler',
      'Gilbert',
      'Glendale',
      'Peoria',
    ];
    const prescriberFirstNames = [
      'James',
      'Patricia',
      'Michael',
      'Linda',
      'William',
      'Barbara',
    ];
    const prescriberLastNames = [
      'Anderson',
      'Thompson',
      'Martinez',
      'Robinson',
      'Clark',
      'Rodriguez',
    ];

    // Random selection helpers
    const randomPick = <T>(arr: T[]): T =>
      arr[Math.floor(Math.random() * arr.length)];
    const randomInt = (min: number, max: number): number =>
      Math.floor(Math.random() * (max - min + 1)) + min;

    // Generate random data
    const randomFirstName = randomPick(firstNames);
    const randomLastName = randomPick(lastNames);
    const randomStreetNumber = randomInt(100, 9999);
    const randomStreet = randomPick(streets);
    const randomCity = randomPick(cities);
    const randomZip = randomInt(85001, 85099);
    const randomPhoneArea = randomInt(480, 602);
    const randomPhoneNumber = randomInt(1000000, 9999999);
    const randomBirthYear = randomInt(1950, 1995);
    const randomBirthMonth = randomInt(1, 12).toString().padStart(2, '0');
    const randomBirthDay = randomInt(1, 28).toString().padStart(2, '0');
    const randomPrescriberFirst = randomPick(prescriberFirstNames);
    const randomPrescriberLast = randomPick(prescriberLastNames);
    const randomNpi = randomInt(**********, **********);
    const randomQuantity = randomPick([1.5, 2.5, 3.5]);
    const randomDaysSupply = randomPick([28, 56, 84]);
    const randomRefills = randomPick([0, 1, 2, 3, 5]);
    const randomUnits = randomPick([40, 60, 79.4, 100, 120]);
    const randomMg = randomPick([2.5, 5, 7.5, 10, 12.5, 15]);
    const randomIssueMonth = randomInt(1, 12);
    const randomIssueDay = randomInt(1, 28);

    // Test data with randomized values
    const testProduct: PharmacyPrescriptionProduct = {
      id: 'product-test',
      name: `SEMAGLUTIDE ${randomMg} MG/ML (GLUTEN FREE)`,
      externalId: 'ext-product-test',
      drugDescription: `SEMAGLUTIDE ${randomMg} MG/ML`,
      quantity: randomQuantity,
      daysSupply: randomDaysSupply,
      sig: `INJECT ${randomUnits} UNITS (${(randomUnits * 0.17).toFixed(1)} MG) SUB-Q EVERY WEEK AS DIRECTED *THIS IS A COMPOUNDED MEDICATION. STORE IN REFRIGERATOR. DISCARD 28 DAYS AFTER FIRST VIAL PUNCTURE*`,
      refills: randomRefills,
    };

    const testRequest: PrescriptionRequest = {
      treatmentId: 'treatment-test',
      prescriptionId: 'prescription-test',
      patient: {
        id: 'patient-test',
        externalId: 'ext-patient-test',
        firstName: randomFirstName,
        lastName: randomLastName,
        dateOfBirth: `${randomBirthYear}-${randomBirthMonth}-${randomBirthDay}`,
        gender: randomPick(['male', 'female']),
        address: {
          street1: `${randomStreetNumber} ${randomStreet} Street`,
          city: randomCity,
          state: 'AZ',
          zipCode: randomZip.toString(),
        },
        phoneNumber: `${randomPhoneArea}-${randomPhoneNumber.toString().slice(0, 3)}-${randomPhoneNumber.toString().slice(3)}`,
      },
      prescriber: {
        id: 'prescriber-test',
        npi: randomNpi.toString(),
        firstName: randomPrescriberFirst,
        lastName: randomPrescriberLast,
        address: {
          street1: `${randomInt(100, 9999)} Medical Plaza Drive`,
          city: randomPick(cities),
          state: 'AZ',
          zipCode: randomInt(85001, 85099).toString(),
        },
        phoneNumber: `${randomInt(480, 602)}-${randomInt(100, 999)}-${randomInt(1000, 9999)}`,
      },
      products: [testProduct],
      prescriptionIssueDate: new Date(
        2025,
        randomIssueMonth - 1,
        randomIssueDay,
      ),
    };

    try {
      console.log('📋 Test Data:');
      console.log(
        `   Patient: ${testRequest.patient.firstName} ${testRequest.patient.lastName}`,
      );
      console.log(`   Product: ${testRequest.products[0].name}`);
      console.log(`   Directions: ${testRequest.products[0].sig}\n`);

      console.log('🖼️  Generating prescription image...');

      const imageBase64 =
        await this.prescriptionImageGenerator.generatePrescriptionImage(
          testRequest,
          testRequest.products[0],
        );

      // Convert base64 to buffer
      const imageBuffer = Buffer.from(imageBase64, 'base64');

      // Determine save path - fixed filename in project root
      const fileName = 'test-prescription.png';
      const filePath = join(process.cwd(), fileName);
      writeFileSync(filePath, imageBuffer);

      console.log(`\n✅ Test image saved to: ${filePath}`);
      console.log(`   File size: ${(imageBuffer.length / 1024).toFixed(2)} KB`);

      console.log('\n🎉 Image generation completed successfully!');
      console.log(
        '\n💡 Check the image to verify if the Creattion font issue is present.',
      );
    } catch (error) {
      console.error('\n❌ Error generating test image:', error);
      process.exit(1);
    }
  }
}
