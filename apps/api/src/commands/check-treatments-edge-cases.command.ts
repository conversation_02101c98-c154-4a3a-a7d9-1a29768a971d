import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { subDays } from 'date-fns';
import { Command, CommandRunner } from 'nest-commander';

@Injectable()
@Command({
  name: 'check-treatments-edge-cases',
  description: 'command to check treatments edge cases',
})
export class CheckTreatmentsEdgeCasesCommand extends CommandRunner {
  constructor(private readonly prisma: PrismaService) {
    super();
  }

  async run(): Promise<void> {
    const queuedPrescriptionsWithInvoices =
      await this.prisma.prescription.findMany({
        where: {
          status: 'queued',
          stripeInvoiceId: {
            not: null,
          },
        },
      });

    if (queuedPrescriptionsWithInvoices.length > 0) {
      console.log(
        `##### Found ${queuedPrescriptionsWithInvoices.length} queued prescriptions with Stripe invoices.`,
      );
      for (const treatment of queuedPrescriptionsWithInvoices) {
        console.log(
          `\t-TreatmentId: ${treatment.id} - prescriptionId: ${treatment.id}`,
        );
      }
    }

    const openPrescriptionsWithoutInvoices =
      await this.prisma.prescription.findMany({
        where: {
          status: 'open',
          stripeInvoiceId: null,
          updatedAt: {
            gte: subDays(new Date(), 1),
          },
        },
      });

    if (openPrescriptionsWithoutInvoices.length > 0) {
      console.log(
        `##### Found ${openPrescriptionsWithoutInvoices.length} open prescriptions without Stripe invoices.`,
      );
      for (const treatment of openPrescriptionsWithoutInvoices) {
        console.log(
          `\t-TreatmentId: ${treatment.id} - prescriptionId: ${treatment.id}`,
        );
      }
    }

    const openPrescriptionsWithInvoices =
      await this.prisma.prescription.findMany({
        where: {
          status: 'open',
          stripeInvoiceId: {
            not: null,
          },
          updatedAt: {
            gte: subDays(new Date(), 3),
          },
        },
      });

    if (openPrescriptionsWithInvoices.length > 0) {
      console.log(
        `##### Found ${openPrescriptionsWithInvoices.length} old open prescriptions with Stripe invoices.`,
      );
      for (const treatment of openPrescriptionsWithInvoices) {
        console.log(
          `\t-TreatmentId: ${treatment.id} - prescriptionId: ${treatment.id}`,
        );
      }
    }
  }
}
