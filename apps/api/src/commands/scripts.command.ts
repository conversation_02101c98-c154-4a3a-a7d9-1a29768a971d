import { PrismaService } from '@/modules/prisma/prisma.service';
import { DosespotService } from '@modules/dosespot/dosespot.service';
import { Injectable } from '@nestjs/common';
import { subDays } from 'date-fns';
import { Command, CommandRunner, Option } from 'nest-commander';

@Injectable()
@Command({
  name: 'scripts',
  description: 'Sync pharmacy assignments for recently created patients',
})
export class ScriptsCommand extends CommandRunner {
  constructor(
    private readonly prisma: PrismaService,
    private readonly dosespotService: DosespotService,
  ) {
    super();
  }

  @Option({
    flags: '-d, --dry-run',
    description: 'Simulate pharmacy sync without making changes',
  })
  parseDryRun(): boolean {
    return true;
  }

  async run(_: string[], options?: { dryRun?: boolean }): Promise<void> {
    console.log('Starting pharmacy sync process...');
    if (options?.dryRun) {
      console.log('DRY RUN MODE - No changes will be made');
    }

    const redRockPharmacy = await this.prisma.pharmacy.findFirst({
      where: { name: 'Red Rock' },
    });

    if (!redRockPharmacy) {
      console.error('Red Rock pharmacy not found in database');
      return;
    }

    const patients = await this.prisma.patient.findMany({
      where: {
        createdAt: { gte: subDays(new Date(), 20) },
        pharmacyId: redRockPharmacy.id,
        doseSpotPatientId: { not: null },
      },
      include: {
        doctor: true,
        pharmacy: true,
      },
    });

    console.log(`Found ${patients.length} patients to process`);

    for (const patient of patients) {
      try {
        console.log(`\nProcessing patient ${patient.id}`);

        const patientPharmacies =
          await this.dosespotService.getPatientPharmacies(
            patient.doseSpotPatientId,
            patient.doctor.doseSpotClinicianId,
          );

        if (!patientPharmacies.length) {
          console.log(`- No pharmacies found for patient ${patient.id}`);
          if (!options?.dryRun) {
            await this.dosespotService.addPatientToPharmacy(
              patient.doseSpotPatientId,
              redRockPharmacy.doseSpotPharmacyId,
              patient.doctor.doseSpotClinicianId,
            );
            console.log(
              `- Successfully added pharmacy for patient ${patient.id}`,
            );
          } else {
            console.log(
              `[DRY RUN] Would add pharmacy for patient ${patient.id}`,
            );
          }
          continue;
        }

        const defaultPharmacy = patientPharmacies.find((p) => p.IsDefault);

        if (!defaultPharmacy) {
          console.log(
            `- No pharmacy default pharmacy for patient ${patient.id} in DoseSpot`,
          );
          if (!options?.dryRun) {
            await this.dosespotService.addPatientToPharmacy(
              patient.doseSpotPatientId,
              redRockPharmacy.doseSpotPharmacyId,
              patient.doctor.doseSpotClinicianId,
            );
            console.log(
              `- Successfully added pharmacy for patient ${patient.id}`,
            );
          } else {
            console.log(
              `[DRY RUN] Would add pharmacy for patient ${patient.id}`,
            );
          }
        } else if (
          parseInt(redRockPharmacy.doseSpotPharmacyId) !==
          defaultPharmacy.PharmacyId
        ) {
          console.log(`- Pharmacy mismatch for patient ${patient.id}`);
          console.log(
            `  DB: ${redRockPharmacy.doseSpotPharmacyId}, DoseSpot: ${defaultPharmacy.PharmacyId}`,
          );
          if (!options?.dryRun) {
            await this.dosespotService.addPatientToPharmacy(
              patient.doseSpotPatientId,
              redRockPharmacy.doseSpotPharmacyId,
              patient.doctor.doseSpotClinicianId,
            );
            console.log(
              `- Successfully updated pharmacy for patient ${patient.id}`,
            );
          } else {
            console.log(
              `[DRY RUN] Would update pharmacy for patient ${patient.id}`,
            );
          }
        } else {
          console.log(`- Pharmacy already correct for patient ${patient.id}`);
        }
      } catch (error) {
        console.error(`Error processing patient ${patient.id}:`, error.message);
      }
    }

    console.log('\nPharmacy sync process completed');
  }
}
