import { Injectable, Logger } from '@nestjs/common';
import { Command, CommandRunner, Option } from 'nest-commander';
import { Client } from 'pg';

interface FixConversationWatchersOptions {
  sourceConnectionString: string;
  targetConnectionString: string;
}

@Injectable()
@Command({
  name: 'fix-conversation-watchers',
  description:
    'Fix ConversationWatcher unread counts by restoring from backup and applying correct increments',
})
export class FixConversationWatchersCommand extends CommandRunner {
  async run(
    _: string[],
    options: FixConversationWatchersOptions,
  ): Promise<void> {
    if (!options.sourceConnectionString || !options.targetConnectionString) {
      console.error('Both source and target connection strings are required');
      return;
    }

    const sourceClient = new Client({
      connectionString: options.sourceConnectionString,
    });

    const targetClient = new Client({
      connectionString: options.targetConnectionString,
    });

    try {
      await sourceClient.connect();
      await targetClient.connect();

      console.log('Connected to both databases');

      // Step 1: Copy original ConversationWatcher data to dryrun table
      await this.copyOriginalData(sourceClient, targetClient);

      // Step 2: Apply correct increments based on ConversationRouter
      await this.applyCorrectIncrements(targetClient);

      // Step 3: Show comparison
      await this.showComparison(targetClient);

      console.log('✅ Fix completed successfully');
    } catch (error) {
      console.error('Error during fix:', error);
      throw error;
    } finally {
      await sourceClient.end();
      await targetClient.end();
    }
  }

  private async copyOriginalData(
    sourceClient: Client,
    targetClient: Client,
  ): Promise<void> {
    console.log('📋 Step 1: Copying original ConversationWatcher data...');

    // Get original data from source (pre-deployment)
    const sourceQuery = `
      SELECT 
        "id",
        "userId",
        "conversationId",
        "unreadMessages",
        "updatedAt"
      FROM "ConversationWatcher"
      WHERE "userId" IN (
        SELECT d."userId" FROM "Doctor" d
        JOIN "User" u ON d."userId" = u.id
        WHERE u."deletedAt" IS NULL
      )
    `;

    console.log('🔍 Fetching original data from source database...');
    const originalData = await sourceClient.query(sourceQuery);
    console.log(
      `📊 Found ${originalData.rows.length} original ConversationWatcher records`,
    );

    // Clear the dryrun table first
    console.log('🗑️  Clearing ConversationWatcher_dryrun table...');
    await targetClient.query('DELETE FROM "ConversationWatcher_dryrun"');

    // Insert original data into dryrun table with progress tracking
    console.log(
      '💾 Inserting original data into ConversationWatcher_dryrun...',
    );
    const batchSize = 1000; // Larger batches for fewer round trips
    const totalRows = originalData.rows.length;

    for (let i = 0; i < totalRows; i += batchSize) {
      const batch = originalData.rows.slice(i, i + batchSize);

      // Build bulk insert query
      const values = batch
        .map((_, index) => {
          const offset = index * 5;
          return `($${offset + 1}, $${offset + 2}, $${offset + 3}, $${offset + 4}, $${offset + 5})`;
        })
        .join(', ');

      const bulkInsertQuery = `
        INSERT INTO "ConversationWatcher_dryrun" 
        ("id", "userId", "conversationId", "unreadMessages", "updatedAt")
        VALUES ${values}
      `;

      // Flatten all parameters
      const params = batch.flatMap((row) => [
        row.id,
        row.userId,
        row.conversationId,
        row.unreadMessages,
        row.updatedAt,
      ]);

      await targetClient.query(bulkInsertQuery, params);

      const progress = Math.min(i + batchSize, totalRows);
      const percentage = ((progress / totalRows) * 100).toFixed(1);
      process.stdout.write(
        `\r📈 Progress: ${progress}/${totalRows} (${percentage}%)`,
      );
    }

    console.log(); // Add newline after progress is complete

    console.log('✅ Original data copied to ConversationWatcher_dryrun');
  }

  private async applyCorrectIncrements(targetClient: Client): Promise<void> {
    console.log('🔧 Step 2: Applying correct increments...');

    // Get ConversationRouter records that were processed during bug window
    const routerQuery = `
      WITH correct_increments AS (SELECT cr."conversationId",
                                         d."userId" as doctor_user_id,
                                         SUM(
                                           CASE
                                             WHEN cr.messages IS NOT NULL AND cr.messages::text != 'null'
                                               THEN json_array_length(cr.messages::json)
                                             ELSE 0
                                             END
                                         )          as increment_amount
                                  FROM "ConversationRouter" cr
                                         JOIN "Conversation" c ON cr."conversationId" = c.id
                                         JOIN "Patient" p ON c."patientId" = p.id
                                         JOIN "Doctor" d ON p."doctorId" = d.id
                                  WHERE cr."relevantForDoctor" = true
                                    AND cr.status IN ('processed', 'closed')
                                    AND cr."createdAt" >= '2025-06-03 13:00:00'
                                    AND cr."createdAt" < '2025-06-03 15:52:16.779'
                                  GROUP BY cr."conversationId", d."userId")
      SELECT "conversationId",
             doctor_user_id,
             increment_amount
      FROM correct_increments
      WHERE increment_amount > 0
      ORDER BY doctor_user_id, "conversationId"
    `;

    console.log('🔍 Calculating correct increments...');
    const increments = await targetClient.query(routerQuery);
    console.log(
      `📊 Found ${increments.rows.length} conversations to increment`,
    );

    // Apply increments to the dryrun table with progress tracking
    console.log('⬆️  Applying increments to ConversationWatcher_dryrun...');

    if (increments.rows.length === 0) {
      console.log('ℹ️  No increments to apply');
    } else {
      // Use a single bulk update with CASE statements for better performance
      const whenClauses = increments.rows
        .map(
          (increment) =>
            `WHEN ("conversationId" = '${increment.conversationId}' AND "userId" = '${increment.doctor_user_id}') THEN "unreadMessages" + ${increment.increment_amount}`,
        )
        .join(' ');

      const conversationUserPairs = increments.rows
        .map(
          (increment) =>
            `('${increment.conversationId}', '${increment.doctor_user_id}')`,
        )
        .join(', ');

      const bulkUpdateQuery = `
        UPDATE "ConversationWatcher_dryrun"
        SET "unreadMessages" = CASE
          ${whenClauses}
                                 ELSE "unreadMessages"
          END,
            "updatedAt"      = NOW()
        WHERE ("conversationId", "userId") IN (VALUES ${conversationUserPairs})
      `;

      console.log(
        `🔄 Executing bulk update for ${increments.rows.length} increments...`,
      );
      const result = await targetClient.query(bulkUpdateQuery);

      const updatedCount = result.rowCount || 0;
      const notFoundCount = increments.rows.length - updatedCount;

      console.log(`✅ Updated ${updatedCount} conversation watchers`);
      if (notFoundCount > 0) {
        console.warn(
          `⚠️  ${notFoundCount} ConversationWatchers not found (likely deleted)`,
        );
      }
    }

    // Set unread count to 0 for all deleted doctors
    console.log('🗑️  Resetting counts for deleted doctors...');
    const resetDeletedDoctorsQuery = `
      UPDATE "ConversationWatcher_dryrun"
      SET "unreadMessages" = 0,
          "updatedAt"      = NOW()
      WHERE "userId" IN (SELECT d."userId"
                         FROM "Doctor" d
                                JOIN "User" u ON d."userId" = u.id
                         WHERE u."deletedAt" IS NOT NULL)
    `;

    const resetResult = await targetClient.query(resetDeletedDoctorsQuery);
    console.log(
      `🔄 Reset ${resetResult.rowCount} conversation watchers for deleted doctors to 0`,
    );

    console.log('✅ Correct increments applied to ConversationWatcher_dryrun');
  }

  private async showComparison(targetClient: Client): Promise<void> {
    console.log('📊 Step 3: Showing comparison...');

    // Get current production data
    const currentQuery = `
      SELECT 
        u."email" as doctor_email,
        COUNT(*) as conversations,
        SUM(cw."unreadMessages") as total_unread
      FROM "ConversationWatcher" cw
      JOIN "User" u ON cw."userId" = u.id
      WHERE u.type = 'doctor'
      GROUP BY u."email"
      ORDER BY u."email"
    `;

    // Get fixed dryrun data
    const dryrunQuery = `
      SELECT 
        u."email" as doctor_email,
        COUNT(*) as conversations,
        SUM(cwd."unreadMessages") as total_unread
      FROM "ConversationWatcher_dryrun" cwd
      JOIN "User" u ON cwd."userId" = u.id
      WHERE u.type = 'doctor'
      GROUP BY u."email"
      ORDER BY u."email"
    `;

    const [currentResult, dryrunResult] = await Promise.all([
      targetClient.query(currentQuery),
      targetClient.query(dryrunQuery),
    ]);

    console.log('Comparison Results:');
    console.log('==================');

    // Create maps for easier comparison
    const currentData = new Map();
    const dryrunData = new Map();

    currentResult.rows.forEach((row) => {
      currentData.set(row.doctor_email, {
        conversations: parseInt(row.conversations),
        total_unread: parseInt(row.total_unread),
      });
    });

    dryrunResult.rows.forEach((row) => {
      dryrunData.set(row.doctor_email, {
        conversations: parseInt(row.conversations),
        total_unread: parseInt(row.total_unread),
      });
    });

    // Get all unique doctor emails
    const allEmails = new Set([...currentData.keys(), ...dryrunData.keys()]);

    let totalCurrentUnread = 0;
    let totalFixedUnread = 0;

    allEmails.forEach((email) => {
      const current = currentData.get(email) || {
        conversations: 0,
        total_unread: 0,
      };
      const fixed = dryrunData.get(email) || {
        conversations: 0,
        total_unread: 0,
      };
      const difference = current.total_unread - fixed.total_unread;

      totalCurrentUnread += current.total_unread;
      totalFixedUnread += fixed.total_unread;

      if (difference !== 0) {
        console.log(
          `${email}: Current=${current.total_unread}, Fixed=${fixed.total_unread}, Difference=${difference}`,
        );
      }
    });

    console.log('==================');
    console.log(`Total Current Unread: ${totalCurrentUnread}`);
    console.log(`Total Fixed Unread: ${totalFixedUnread}`);
    console.log(`Total Difference: ${totalCurrentUnread - totalFixedUnread}`);
  }

  @Option({
    flags: '--source-connection-string <string>',
    description:
      'Connection string for source database (pre-deployment backup)',
  })
  parseSourceConnectionString(val: string): string {
    return val;
  }

  @Option({
    flags: '--target-connection-string <string>',
    description: 'Connection string for target database (production)',
  })
  parseTargetConnectionString(val: string): string {
    return val;
  }
}
