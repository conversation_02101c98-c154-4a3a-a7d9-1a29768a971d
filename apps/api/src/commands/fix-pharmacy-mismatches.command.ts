import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { Command, CommandRunner, Option } from 'nest-commander';

interface PatientMismatch {
  patient_id: string;
  patient_pharmacy_id: string;
  patient_pharmacy_name: string;
  patient_state: string;
  desired_product_id: string;
  product_pharmacy_id: string;
  product_pharmacy_name: string;
}

@Injectable()
@Command({
  name: 'fix-pharmacy-mismatches',
  description:
    'Fix pharmacy mismatches for unprescribed patients with incorrect desired treatments',
})
export class FixPharmacyMismatchesCommand extends CommandRunner {
  constructor(private readonly prisma: PrismaService) {
    super();
  }

  @Option({
    flags: '-d, --dry-run',
    description: 'Simulate fixes without making changes',
  })
  parseDryRun(): boolean {
    return true;
  }

  @Option({
    flags: '-l, --limit <number>',
    description: 'Limit number of patients to process',
  })
  parseLimit(value: string): number {
    return parseInt(value, 10);
  }

  async run(
    _: string[],
    options?: { dryRun?: boolean; limit?: number },
  ): Promise<void> {
    console.log('🔍 Finding patients with pharmacy mismatches...');
    if (options?.dryRun) {
      console.log('DRY RUN MODE - No changes will be made');
    }

    // Find all patients who have never been prescribed but have desired treatments from different pharmacies
    const affectedPatients = await this.prisma.$queryRaw<PatientMismatch[]>`
      WITH patient_details AS (SELECT p.id           as patient_id,
                                      p."pharmacyId" as patient_pharmacy_id,
                                      ph.name        as patient_pharmacy_name,
                                      s.code         as patient_state
                               FROM "Patient" p
                                      JOIN "Pharmacy" ph ON ph.id = p."pharmacyId"
                                      JOIN "State" s ON s.id = p."stateId"),
           patient_treatments AS (SELECT DISTINCT "patientId"
                                  FROM "Treatment"),
           desired_treatment_details AS (SELECT pdt."patientId",
                                                pdt."productId"   as desired_product_id,
                                                prod."pharmacyId" as product_pharmacy_id,
                                                ph.name           as product_pharmacy_name
                                         FROM "PatientDesiredTreatment" pdt
                                                JOIN "Product" prod ON prod.id = pdt."productId"
                                                JOIN "Pharmacy" ph ON ph.id = prod."pharmacyId")
      SELECT pd.patient_id,
             pd.patient_pharmacy_id,
             pd.patient_pharmacy_name,
             pd.patient_state,
             dt.desired_product_id,
             dt.product_pharmacy_id,
             dt.product_pharmacy_name
      FROM patient_details pd
             JOIN desired_treatment_details dt ON dt."patientId" = pd.patient_id
             LEFT JOIN patient_treatments pt ON pt."patientId" = pd.patient_id
      WHERE pt."patientId" IS NULL -- Never been prescribed
        AND pd.patient_pharmacy_id != dt.product_pharmacy_id -- Pharmacy mismatch
        ${options?.limit ? Prisma.sql`LIMIT ${options.limit}` : Prisma.empty}
    `;

    console.log(`Found ${affectedPatients.length} mismatched records`);

    if (affectedPatients.length === 0) {
      console.log('✅ No patients to fix');
      return;
    }

    // Group by patient to handle multiple desired treatments
    const patientMap = new Map<string, PatientMismatch[]>();
    for (const record of affectedPatients) {
      const existing = patientMap.get(record.patient_id) || [];
      existing.push(record);
      patientMap.set(record.patient_id, existing);
    }

    console.log(`Processing ${patientMap.size} unique patients...`);

    // Get all pharmacies with their priorities
    const pharmacies = await this.prisma.pharmacy.findMany({
      where: { enabled: true },
      include: {
        PharmacyOnState: {
          include: {
            state: true,
          },
        },
      },
    });

    // Create lookup maps for pharmacy priorities by state
    const regularPriorities = new Map<
      string,
      Array<{ pharmacyId: string; priority: number; pharmacyName: string }>
    >();
    const glp1Priorities = new Map<
      string,
      Array<{ pharmacyId: string; priority: number; pharmacyName: string }>
    >();

    // Build priority maps based on pharmacy availability per state
    for (const pharmacy of pharmacies) {
      for (const pos of pharmacy.PharmacyOnState) {
        const stateCode = pos.state.code;

        // Regular priorities
        const regular = regularPriorities.get(stateCode) || [];
        regular.push({
          pharmacyId: pharmacy.id,
          priority: pharmacy.regularPriority,
          pharmacyName: pharmacy.name,
        });
        regularPriorities.set(stateCode, regular);

        // GLP-1 priorities
        const glp1 = glp1Priorities.get(stateCode) || [];
        glp1.push({
          pharmacyId: pharmacy.id,
          priority: pharmacy.usingGLP1Priority,
          pharmacyName: pharmacy.name,
        });
        glp1Priorities.set(stateCode, glp1);
      }
    }

    // Sort priorities
    for (const [, priorities] of regularPriorities) {
      priorities.sort((a, b) => a.priority - b.priority);
    }
    for (const [, priorities] of glp1Priorities) {
      priorities.sort((a, b) => a.priority - b.priority);
    }

    let fixedCount = 0;
    let errorCount = 0;

    for (const [patientId, records] of patientMap) {
      try {
        const patientState = records[0].patient_state;
        const currentPharmacyId = records[0].patient_pharmacy_id;
        const desiredProductIds = records.map((r) => r.desired_product_id);

        console.log(`\n👤 Patient ${patientId}:`);
        console.log(`  State: ${patientState}`);
        console.log(`  Current pharmacy: ${records[0].patient_pharmacy_name}`);
        console.log(
          `  Mismatched desired treatments from: ${[
            ...new Set(records.map((r) => r.product_pharmacy_name)),
          ].join(', ')}`,
        );

        // Determine if this is a GLP-1 patient based on desired treatments
        const hasGlp1Treatment =
          (await this.prisma.product.count({
            where: {
              id: { in: desiredProductIds },
              metadata: {
                path: ['type'],
                equals: 'core',
              },
            },
          })) > 0;

        // Get appropriate pharmacy based on priorities
        const priorities =
          hasGlp1Treatment && glp1Priorities.has(patientState)
            ? glp1Priorities.get(patientState)!
            : regularPriorities.get(patientState) || [];

        let newPharmacyId = currentPharmacyId;
        let newPharmacyName = records[0].patient_pharmacy_name;

        if (priorities.length > 0) {
          newPharmacyId = priorities[0].pharmacyId;
          newPharmacyName = priorities[0].pharmacyName;
        }

        console.log(
          `  Will assign to: ${newPharmacyName} (${hasGlp1Treatment ? 'GLP-1' : 'Regular'} priority)`,
        );

        if (!options?.dryRun) {
          // Start transaction
          await this.prisma.$transaction(async (tx) => {
            // 1. Remove mismatched desired treatments
            const deleted = await tx.patientDesiredTreatment.deleteMany({
              where: {
                patientId: patientId,
                productId: { in: desiredProductIds },
              },
            });
            console.log(
              `  ✅ Removed ${deleted.count} mismatched desired treatments`,
            );

            // 2. Update patient pharmacy if needed
            if (newPharmacyId !== currentPharmacyId) {
              await tx.patient.update({
                where: { id: patientId },
                data: { pharmacyId: newPharmacyId },
              });
              console.log(
                `  ✅ Updated pharmacy from ${records[0].patient_pharmacy_name} to ${newPharmacyName}`,
              );
            }
          });

          fixedCount++;
        } else {
          console.log('  🔸 DRY RUN - No changes made');
        }
      } catch (error) {
        console.error(
          `  ❌ Error processing patient ${patientId}:`,
          error.message,
        );
        errorCount++;
      }
    }

    console.log('\n📊 Summary:');
    console.log(`  Total patients processed: ${patientMap.size}`);
    if (!options?.dryRun) {
      console.log(`  Successfully fixed: ${fixedCount}`);
      console.log(`  Errors: ${errorCount}`);
    } else {
      console.log(
        '\n⚠️  This was a DRY RUN. Run without --dry-run to apply changes',
      );
    }
  }
}
