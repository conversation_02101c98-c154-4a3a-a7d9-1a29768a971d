import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import {
  eachMonthOfInterval,
  endOfMonth,
  format,
  parseISO,
  setHours,
  setMilliseconds,
  setMinutes,
  setSeconds,
  startOfMonth,
  subMonths,
} from 'date-fns';
import { formatInTimeZone } from 'date-fns-tz';
import { Command, CommandRunner, Option } from 'nest-commander';

@Injectable()
@Command({
  name: 'count-doctor-interactions',
  description: 'Count monthly doctor interactions with patients',
})
export class CountDoctorInteractionsCommand extends CommandRunner {
  private readonly TIMEZONE = 'America/Puerto_Rico';

  constructor(private readonly prisma: PrismaService) {
    super();
  }

  @Option({
    flags: '--from [date]',
    description: 'Start date (YYYY-MM)',
  })
  parseFrom(val: string): string {
    if (val && !/^\d{4}-\d{2}$/.test(val)) {
      throw new Error('From date must be in YYYY-MM format');
    }
    return val;
  }

  @Option({
    flags: '--to [date]',
    description: 'End date (YYYY-MM)',
  })
  parseTo(val: string): string {
    if (val && !/^\d{4}-\d{2}$/.test(val)) {
      throw new Error('To date must be in YYYY-MM format');
    }
    return val;
  }

  private setStartOfDay(date: Date): Date {
    return setMilliseconds(setSeconds(setMinutes(setHours(date, 0), 0), 0), 0);
  }

  private setEndOfDay(date: Date): Date {
    return setMilliseconds(
      setSeconds(setMinutes(setHours(date, 23), 59), 59),
      999,
    );
  }

  async run(
    _: string[],
    options?: { from?: string; to?: string },
  ): Promise<void> {
    // Set date range
    const endDate = options?.to
      ? endOfMonth(parseISO(`${options.to}-01`))
      : endOfMonth(new Date());

    const startDate = options?.from
      ? startOfMonth(parseISO(`${options.from}-01`))
      : startOfMonth(subMonths(endDate, 11));

    // Get all doctors
    const doctors = await this.prisma.user.findMany({
      where: { type: 'doctor' },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
      },
      orderBy: { email: 'asc' },
    });

    if (doctors.length === 0) {
      console.log('No doctors found');
      return;
    }

    const messages = await this.prisma.conversationMessage.findMany({
      where: {
        userId: { in: doctors.map((d) => d.id) },
        createdAt: {
          gte: this.setStartOfDay(startDate),
          lte: this.setEndOfDay(endDate),
        },
        // Exclude system messages from Empower
        NOT: {
          AND: [
            { type: 'system' },
            {
              treatment: {
                initialProductPrice: {
                  product: {
                    pharmacy: {
                      name: 'Empower',
                    },
                  },
                },
              },
            },
          ],
        },
      },
      select: {
        userId: true,
        createdAt: true,
        type: true,
        treatmentId: true,
        conversation: {
          select: { patientId: true },
        },
        treatment: {
          select: {
            initialProductPrice: {
              select: {
                product: {
                  select: {
                    pharmacy: {
                      select: { name: true },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    // Generate months range
    const months = eachMonthOfInterval({ start: startDate, end: endDate });

    // Create interaction map
    const interactionMap = new Map<string, Map<string, Set<string>>>();
    months.forEach((month) => {
      const monthKey = format(month, 'yyyy-MM');
      interactionMap.set(monthKey, new Map());
      doctors.forEach((doctor) => {
        interactionMap.get(monthKey).set(doctor.id, new Set());
      });
    });

    // Process messages
    messages.forEach((message) => {
      if (!message.userId || !message.conversation?.patientId) return;

      const monthKey = formatInTimeZone(
        message.createdAt,
        this.TIMEZONE,
        'yyyy-MM',
      );

      const monthMap = interactionMap.get(monthKey);
      if (monthMap) {
        const doctorSet = monthMap.get(message.userId);
        if (doctorSet) {
          doctorSet.add(message.conversation.patientId);
        }
      }
    });

    // Print CSV header
    const monthKeys = months.map((month) => format(month, 'yyyy-MM'));
    console.log(['Doctor', ...monthKeys].join('\t'));

    // Prepare data rows for each doctor
    const doctorRows = doctors.map((doctor) => {
      const doctorInteractions = monthKeys.map(
        (month) => interactionMap.get(month)?.get(doctor.id)?.size || 0,
      );
      return {
        doctor,
        interactions: doctorInteractions,
        lastMonthCount: doctorInteractions[doctorInteractions.length - 1],
      };
    });

    // Sort by last month count in descending order, then alphabetically by last name
    doctorRows.sort((a, b) => {
      if (b.lastMonthCount !== a.lastMonthCount) {
        return b.lastMonthCount - a.lastMonthCount;
      }
      return a.doctor.lastName.localeCompare(b.doctor.lastName);
    });

    // Print sorted data rows
    doctorRows.forEach(({ doctor, interactions }) => {
      console.log(
        [`Dr ${doctor.firstName} ${doctor.lastName}`, ...interactions].join(
          '\t',
        ),
      );
    });

    // Calculate and print totals
    const monthlyTotals = monthKeys.map((month) => {
      let total = 0;
      doctors.forEach((doctor) => {
        total += interactionMap.get(month)?.get(doctor.id)?.size || 0;
      });
      return total;
    });

    console.log(['TOTAL', ...monthlyTotals].join('\t'));
  }
}
