import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { parse } from 'csv-parse/sync';
import { Command, CommandRunner, Option } from 'nest-commander';

interface CsvRow {
  pharmacy: string;
  form: string;
  product: string;
  phase: string;
  compoundName: string;
  patientDirections: string;
}

@Injectable()
@Command({
  name: 'update-product-prices-csv',
  description:
    'Update product prices compound name and patient directions from Google Sheets',
})
export class UpdateProductPricesCsvCommand extends CommandRunner {
  constructor(private readonly prisma: PrismaService) {
    super();
  }

  @Option({
    flags: '--url [url]',
    description: 'Google Sheets URL to fetch data from',
  })
  parseUrl(val: string): string {
    return val;
  }

  @Option({
    flags: '--dry-run',
    description: 'Simulate updates without making changes',
  })
  parseDryRun(): boolean {
    return true;
  }

  async run(
    _: string[],
    options?: { url?: string; dryRun?: boolean },
  ): Promise<void> {
    if (options?.dryRun) {
      console.log('DRY RUN MODE - No changes will be made');
    }

    try {
      const input = await this.getInput(options?.url);
      const csvData = await this.parseCsvData(input);
      await this.processCsvData(csvData, options?.dryRun ?? false);
    } catch (error) {
      console.error('Error processing data:', error.message);
    }
  }

  private async getInput(url?: string): Promise<string> {
    // Check if there's data in stdin
    const stdinData = await this.checkStdin();
    if (stdinData) {
      return stdinData;
    }

    // If no stdin data and no URL, throw error
    if (!url) {
      throw new Error(
        'No input provided. Either pipe data to stdin or provide --url parameter',
      );
    }

    // Extract sheet ID from URL
    const sheetId = url.match(/\/d\/(.*?)(\/|$)/)?.[1];
    if (!sheetId) {
      throw new Error('Invalid Google Sheets URL');
    }

    // Fetch CSV export
    const csvUrl = `https://docs.google.com/spreadsheets/d/${sheetId}/export?format=csv`;
    const response = await fetch(csvUrl);
    if (!response.ok) {
      throw new Error(`Failed to fetch sheet: ${response.statusText}`);
    }

    return await response.text();
  }

  private async checkStdin(): Promise<string | null> {
    // Check if there's data in stdin
    const stdinBuffer = await new Promise<Buffer>((resolve) => {
      const chunks: Buffer[] = [];
      if (process.stdin.isTTY) {
        resolve(Buffer.concat([]));
        return;
      }

      process.stdin.on('data', (chunk) => chunks.push(chunk));
      process.stdin.on('end', () => resolve(Buffer.concat(chunks)));
    });

    return stdinBuffer.length > 0 ? stdinBuffer.toString('utf8') : null;
  }

  private async parseCsvData(input: string): Promise<CsvRow[]> {
    // Parse the CSV data
    const rows = parse(input, {
      skip_empty_lines: true,
      trim: true,
    });

    if (rows.length < 2) {
      throw new Error('CSV data must have at least a header and one data row');
    }

    // Expected header: Pharmacy,Form,Product,Phase,Compound Name,Patient Directions
    const csvData: CsvRow[] = [];

    for (let i = 1; i < rows.length; i++) {
      const values = rows[i];

      csvData.push({
        pharmacy: values[1] || '',
        form: values[3] || '',
        product: values[5] || '',
        phase: values[11] || '',
        compoundName: values[17] || '',
        patientDirections: values[18] || '',
      });
    }

    return csvData;
  }

  private async processCsvData(
    csvData: CsvRow[],
    dryRun: boolean,
  ): Promise<void> {
    // Filter out invalid rows
    const validRows = csvData.filter((row) => this.isValidRow(row));
    const invalidRows = csvData.filter((row) => !this.isValidRow(row));

    console.log(`Total rows: ${csvData.length}`);
    console.log(`Valid rows: ${validRows.length}`);
    console.log(`Invalid rows (skipped): ${invalidRows.length}`);

    // Get all our available product prices for comparison
    const availableProducts = await this.getAvailableProductPrices();
    console.log(
      `\nAvailable product prices in database: ${availableProducts.length}`,
    );

    let successCount = 0;
    let notFoundCount = 0;
    let errorCount = 0;
    const notFoundProducts: string[] = [];
    const updatedProducts: string[] = [];

    for (const row of validRows) {
      try {
        const result = await this.updateProductPrice(row, dryRun);
        if (result) {
          successCount++;
          updatedProducts.push(
            `${row.pharmacy} | ${row.form} | ${row.product} | Phase ${row.phase}`,
          );
        } else {
          notFoundCount++;
          notFoundProducts.push(
            `${row.pharmacy} | ${row.form} | ${row.product} | Phase ${row.phase}`,
          );
        }
      } catch (error) {
        console.error(`Error processing row:`, row, error.message);
        errorCount++;
      }
    }

    this.printSummary(
      successCount,
      notFoundCount,
      errorCount,
      validRows.length,
      updatedProducts,
      notFoundProducts,
    );
  }

  private isValidRow(row: CsvRow): boolean {
    return !!(
      row.pharmacy &&
      row.form &&
      row.product &&
      row.compoundName &&
      row.patientDirections
    );
  }

  private async getAvailableProductPrices() {
    return await this.prisma.productPrice.findMany({
      select: {
        id: true,
        name: true,
        phase: true,
        compoundName: true,
        patientDirections: true,
        product: {
          select: {
            name: true,
            form: true,
            pharmacy: {
              select: {
                name: true,
              },
            },
          },
        },
      },
      orderBy: [
        { product: { pharmacy: { name: 'asc' } } },
        { product: { name: 'asc' } },
        { phase: 'asc' },
      ],
    });
  }

  private printSummary(
    successCount: number,
    notFoundCount: number,
    errorCount: number,
    totalValid: number,
    updatedProducts: string[],
    notFoundProducts: string[],
  ) {
    console.log('\n=== PROCESSING SUMMARY ===');
    console.log(`Successfully processed: ${successCount}`);
    console.log(`Not in database (skipped): ${notFoundCount}`);
    console.log(`Errors: ${errorCount}`);
    console.log(`Total valid rows: ${totalValid}`);

    if (updatedProducts.length > 0) {
      console.log('\n=== SUCCESSFULLY UPDATED ===');
      updatedProducts.forEach((product) => console.log(`✅ ${product}`));
    }

    if (notFoundProducts.length > 0) {
      console.log('\n=== NOT FOUND IN DATABASE ===');
      notFoundProducts.forEach((product) => console.log(`⚠️  ${product}`));
    }
  }

  private async updateProductPrice(
    row: CsvRow,
    dryRun: boolean,
  ): Promise<boolean> {
    const { pharmacy, form, product, phase, compoundName, patientDirections } =
      row;

    // Skip if any required field is empty
    if (!pharmacy || !form || !product || !compoundName || !patientDirections) {
      return false;
    }

    // Find the product price by pharmacy name, form, product name, and phase
    const whereClause: any = {
      product: {
        label: product,
        form: form.toLowerCase() as any,
        pharmacy: {
          name: pharmacy,
        },
      },
    };

    // Only add phase filter if it's not Ondansetron and phase is provided
    if (product !== 'Ondansetron' && phase && !isNaN(parseInt(phase))) {
      whereClause.phase = parseInt(phase);
    }

    const productPrice = await this.prisma.productPrice.findFirst({
      where: whereClause,
      include: {
        product: {
          include: {
            pharmacy: true,
          },
        },
      },
    });

    if (!productPrice) {
      console.log(
        `⚠️  Not in database: ${pharmacy} | ${form} | ${product} | Phase ${phase}`,
      );
      return false;
    }

    console.log(
      `✅ Found: ${productPrice.product.pharmacy.name} | ${productPrice.name} | ${productPrice.product.name} | Phase ${productPrice.phase}`,
    );

    if (!dryRun) {
      await this.prisma.productPrice.update({
        where: { id: productPrice.id },
        data: {
          compoundName,
          patientDirections,
        },
      });
      console.log(`   Updated compound name: "${compoundName}"`);
      console.log(`   Updated patient directions: "${patientDirections}"`);
    } else {
      console.log(`   [DRY RUN] Would update compound name: "${compoundName}"`);
      console.log(
        `   [DRY RUN] Would update patient directions: "${patientDirections}"`,
      );
    }

    return true;
  }
}
