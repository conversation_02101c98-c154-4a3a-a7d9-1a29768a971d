import * as fs from 'fs';
import { runInDbTransaction } from '@/helpers/transaction';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { parse } from 'csv-parse';
import { subDays } from 'date-fns';
import { camelCase, chunk } from 'lodash';
import { Command, CommandRunner, Option } from 'nest-commander';
import * as ora from 'ora';

interface ShipmentUpdateRecord {
  patientId: string;
  trackingNumber: string;
  trackingLink: string;
  status: string;
  pharmacyId: string | null;
  receivedAt: Date;
}

@Injectable()
@Command({
  name: 'import-shipment-updates',
  description: 'Import shipment updates from CSV file',
})
export class ImportShipmentUpdatesCommand extends CommandRunner {
  // Map incoming pharmacy names to database slugs
  private readonly pharmacyMap: Record<string, string> = {
    Empower: 'empower',
    'Red Rock': 'redRock',
    RedRock: 'redRock',
    Epiq: 'epiqScripts',
    EpiqScripts: 'epiqScripts',
    Strive: 'strive',
    StriveRX: 'strive',
    Boothwyn: 'boothwyn',
    Partell: 'partell',
  };

  // Configurable batch sizes for different operations
  private readonly PATIENT_BATCH_SIZE = 1000;
  private readonly SHIPMENT_UPDATE_BATCH_SIZE = 500;
  private readonly EXISTING_RECORDS_BATCH_SIZE = 2000;

  constructor(private readonly prisma: PrismaService) {
    super();
  }

  @Option({
    flags: '-f, --file <file>',
    description: 'CSV file path',
    required: true,
  })
  parseFile(val: string): string {
    return val;
  }

  @Option({
    flags: '-c, --chunk-size <size>',
    description: 'Chunk size for batch operations (default: 500)',
    defaultValue: '500',
  })
  parseChunkSize(val: string): number {
    const size = parseInt(val, 10);
    if (isNaN(size) || size < 1) {
      throw new Error('Chunk size must be a positive number');
    }
    return size;
  }

  /**
   * Prefetch all existing shipment updates for the given patient IDs
   * This eliminates individual database lookups during processing
   */
  private async prefetchExistingShipmentUpdates(
    patientIds: string[],
    spinner: ora.Ora,
  ): Promise<Set<string>> {
    const existingRecords = new Set<string>();
    const batches = chunk(patientIds, this.EXISTING_RECORDS_BATCH_SIZE);

    spinner.text = `Prefetching existing shipment updates (${batches.length} batches)...`;

    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      const records = await this.prisma.shipmentUpdate.findMany({
        where: { patientId: { in: batch } },
        select: { patientId: true, trackingNumber: true },
      });

      // Create composite keys for fast lookup
      records.forEach((record) => {
        existingRecords.add(`${record.patientId}:${record.trackingNumber}`);
      });

      spinner.text = `Prefetching existing shipment updates (${i + 1}/${batches.length} batches)...`;
    }

    return existingRecords;
  }

  /**
   * Validate patients in batches to reduce database round trips
   */
  private async validatePatientsInBatches(
    patientIds: string[],
    spinner: ora.Ora,
  ): Promise<Set<string>> {
    const validPatients = new Set<string>();
    const batches = chunk(patientIds, this.PATIENT_BATCH_SIZE);

    spinner.text = `Validating patients (${batches.length} batches)...`;

    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      const patients = await this.prisma.patient.findMany({
        where: { id: { in: batch } },
        select: { id: true },
      });

      patients.forEach((patient) => {
        validPatients.add(patient.id);
      });

      spinner.text = `Validating patients (${i + 1}/${batches.length} batches)...`;
    }

    return validPatients;
  }

  /**
   * Create shipment updates in batches using transactions for better performance
   */
  private async createShipmentUpdatesInBatches(
    records: ShipmentUpdateRecord[],
    chunkSize: number,
    spinner: ora.Ora,
  ): Promise<{ imported: number; errors: number }> {
    let imported = 0;
    let errors = 0;
    const batches = chunk(records, chunkSize);

    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];

      try {
        await runInDbTransaction(this.prisma, async (tx) => {
          await tx.shipmentUpdate.createMany({
            data: batch.map((record) => ({
              status: record.status,
              patientId: record.patientId,
              trackingNumber: record.trackingNumber,
              trackingLink: record.trackingLink,
              pharmacyId: record.pharmacyId,
              createdAt: record.receivedAt,
              updatedAt: record.receivedAt,
            })),
            skipDuplicates: true, // Skip any duplicates that might have been created since prefetch
          });
        });

        imported += batch.length;
      } catch (error) {
        console.error(`Error processing batch ${i + 1}:`, error);
        errors += batch.length;
      }

      // Update progress
      const percentage = Math.round(((i + 1) / batches.length) * 100);
      spinner.text = `Creating shipment updates: ${i + 1}/${batches.length} batches (${percentage}%) - Imported: ${imported.toLocaleString()}, Errors: ${errors.toLocaleString()}`;
    }

    return { imported, errors };
  }

  async run(
    _: string[],
    options: { file: string; chunkSize: number },
  ): Promise<void> {
    const cutoffDate = subDays(new Date(), 10);
    const latestByPatient = new Map<string, any>();
    const pharmacyCache = new Map<string, string>(); // slug -> pharmacyId
    const chunkSize = options.chunkSize || this.SHIPMENT_UPDATE_BATCH_SIZE;

    console.log(`Importing shipment updates from: ${options.file}`);
    console.log(
      `Only processing records from ${cutoffDate.toISOString()} onwards`,
    );
    console.log(`Using chunk size: ${chunkSize.toLocaleString()}`);

    const spinner = ora('Loading pharmacies...').start();

    // Pre-fetch all pharmacies and build cache
    const pharmacies = await this.prisma.pharmacy.findMany({
      where: { slug: { not: null } },
      select: { id: true, slug: true },
    });

    for (const pharmacy of pharmacies) {
      if (pharmacy.slug) {
        pharmacyCache.set(pharmacy.slug, pharmacy.id);
      }
    }

    spinner.succeed(`Loaded ${pharmacyCache.size} pharmacies into cache`);

    // Read and parse CSV
    const readSpinner = ora('Reading CSV file...').start();
    let totalRows = 0;

    await new Promise((resolve, reject) => {
      fs.createReadStream(options.file)
        .pipe(parse({ columns: true }))
        .on('data', (row) => {
          totalRows++;

          // Parse timestamp
          const timestamp = new Date(row.TIMESTAMP);
          if (timestamp < cutoffDate) return;

          const patientId = row.USER_ID;
          if (!patientId) return;

          // Check if we have a newer record for this patient
          const existing = latestByPatient.get(patientId);
          if (!existing || timestamp > new Date(existing.TIMESTAMP)) {
            latestByPatient.set(patientId, row);
          }

          // Update spinner with row count
          if (totalRows % 1000 === 0) {
            readSpinner.text = `Reading CSV file... (${totalRows.toLocaleString()} rows)`;
          }
        })
        .on('end', resolve)
        .on('error', reject);
    });

    readSpinner.succeed(
      `Read ${totalRows.toLocaleString()} rows, found ${latestByPatient.size.toLocaleString()} unique patients with recent updates`,
    );

    // Extract unique patient IDs for batch operations
    const uniquePatientIds = Array.from(latestByPatient.keys());
    const total = latestByPatient.size;

    const processSpinner = ora(
      `Processing ${total.toLocaleString()} patient records...`,
    ).start();

    // Step 1: Validate patients in batches
    const validPatients = await this.validatePatientsInBatches(
      uniquePatientIds,
      processSpinner,
    );

    // Step 2: Prefetch existing shipment updates
    const existingRecords = await this.prefetchExistingShipmentUpdates(
      uniquePatientIds,
      processSpinner,
    );

    processSpinner.succeed(
      `Prefetched data - Valid patients: ${validPatients.size.toLocaleString()}, Existing records: ${existingRecords.size.toLocaleString()}`,
    );

    // Step 3: Process and prepare records for batch creation
    const recordsToCreate: ShipmentUpdateRecord[] = [];
    let skipped = 0;

    const prepareSpinner = ora(
      'Preparing records for batch creation...',
    ).start();

    for (const [patientId, row] of latestByPatient) {
      try {
        // Skip if patient doesn't exist (using prefetched data)
        if (!validPatients.has(patientId)) {
          skipped++;
          continue;
        }

        // Transform data
        const trackingNumber = row.TRACKING_NUMBER;
        const trackingLink = row.TRACKING_LINK;
        const status = row.ORDER_STATUS
          ? camelCase(row.ORDER_STATUS.replace(/^LS-/, ''))
          : null;
        const pharmacyName = row.PHARMACY;
        const receivedAt = row.RECEIVED_AT
          ? new Date(row.RECEIVED_AT)
          : new Date();

        if (!trackingNumber || !status) {
          skipped++;
          continue;
        }

        // Find pharmacy from cache
        let pharmacyId: string | null = null;
        const pharmacySlug = this.pharmacyMap[pharmacyName];
        if (pharmacySlug) {
          pharmacyId = pharmacyCache.get(pharmacySlug) || null;
        }

        // Check if record already exists (using prefetched data)
        const recordKey = `${patientId}:${trackingNumber}`;
        if (existingRecords.has(recordKey)) {
          // Skip - existing record from webhook is always newer
          skipped++;
          continue;
        }

        // Add to batch creation list
        recordsToCreate.push({
          patientId,
          trackingNumber,
          trackingLink,
          status,
          pharmacyId,
          receivedAt,
        });
      } catch (error) {
        skipped++;
        // Don't log individual errors during bulk import - too noisy
      }
    }

    prepareSpinner.succeed(
      `Prepared ${recordsToCreate.length.toLocaleString()} records for creation, skipped ${skipped.toLocaleString()}`,
    );

    // Step 4: Create shipment updates in batches
    let imported = 0;
    let errors = 0;

    if (recordsToCreate.length > 0) {
      const createSpinner = ora(
        'Creating shipment updates in batches...',
      ).start();

      const result = await this.createShipmentUpdatesInBatches(
        recordsToCreate,
        chunkSize,
        createSpinner,
      );

      imported = result.imported;
      errors = result.errors;

      if (imported > 0) {
        createSpinner.succeed(
          `Import completed successfully! Imported: ${imported.toLocaleString()}, Errors: ${errors.toLocaleString()}, Skipped: ${skipped.toLocaleString()}`,
        );
      } else {
        createSpinner.fail(
          `Import completed with no records imported. Errors: ${errors.toLocaleString()}, Skipped: ${skipped.toLocaleString()}`,
        );
      }
    } else {
      console.log(
        `Import completed with no new records to create. Skipped: ${skipped.toLocaleString()}`,
      );
    }

    // Final summary
    console.log('\n=== Import Summary ===');
    console.log(`Total CSV rows processed: ${totalRows.toLocaleString()}`);
    console.log(`Unique patients found: ${total.toLocaleString()}`);
    console.log(`Valid patients: ${validPatients.size.toLocaleString()}`);
    console.log(`Records imported: ${imported.toLocaleString()}`);
    console.log(`Records skipped: ${skipped.toLocaleString()}`);
    console.log(`Errors: ${errors.toLocaleString()}`);
    console.log(`Chunk size used: ${chunkSize.toLocaleString()}`);
  }
}
