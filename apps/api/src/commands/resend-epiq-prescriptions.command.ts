import { writeFileSync } from 'fs';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import { Command, CommandRunner, Option } from 'nest-commander';

@Injectable()
@Command({
  name: 'resend-epiq-prescriptions',
  description:
    'Find prescriptions sent to Epiq Scripts with "Take as directed" medication_sig for core medications',
})
export class ResendEpiqPrescriptionsCommand extends CommandRunner {
  private readonly EPIQ_SCRIPTS_PHARMACY_ID =
    'febdd81b-3cc4-406f-b311-eaed499d9db6';
  private readonly httpClient: AxiosInstance;

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {
    super();

    // Initialize HTTP client for direct Epiq API calls
    const baseUrl = this.configService.getOrThrow<string>('EPIQ_API_URL');
    const username = this.configService.getOrThrow<string>('EPIQ_API_USERNAME');
    const password = this.configService.getOrThrow<string>('EPIQ_API_PASSWORD');

    this.httpClient = axios.create({
      baseURL: baseUrl,
      auth: { username, password },
    });
  }

  @Option({
    flags: '--dry-run',
    description: 'Show what would be resent without actually resending',
  })
  parseDryRun(): boolean {
    return true;
  }

  @Option({
    flags: '--limit [number]',
    description:
      'Limit the number of prescriptions to process (useful for testing)',
  })
  parseLimit(val: string): number {
    const limit = parseInt(val, 10);
    if (isNaN(limit) || limit < 1) {
      throw new Error('Limit must be a positive number');
    }
    return limit;
  }

  @Option({
    flags: '--delay [number]',
    description: 'Delay in milliseconds between API requests (default: 1000ms)',
  })
  parseDelay(val: string): number {
    const delay = parseInt(val, 10);
    if (isNaN(delay) || delay < 0) {
      throw new Error('Delay must be a non-negative number');
    }
    return delay;
  }

  /**
   * Helper function to create a delay
   */
  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Fetches all core product mappings with their directions
   * Returns a Map where key is rx_id and value is the medication directions
   */
  private async fetchCoreProductMappings(): Promise<Map<string, string>> {
    const mappings = await this.prisma.productPriceMapping.findMany({
      where: {
        productPrice: {
          product: { pharmacyId: this.EPIQ_SCRIPTS_PHARMACY_ID },
        },
      },
      select: {
        externalId: true,
        metadata: true,
      },
    });

    const productsMap = new Map<string, string>();
    for (const mapping of mappings) {
      const metadata = mapping.metadata as any;
      if (metadata?.directions) {
        productsMap.set(mapping.externalId, metadata.directions);
      }
    }

    return productsMap;
  }

  async run(
    _: string[],
    options?: { dryRun?: boolean; limit?: number; delay?: number },
  ): Promise<void> {
    const isDryRun = options?.dryRun || false;
    const limit = options?.limit;
    const delay = options?.delay || 1000; // Default 1000ms delay
    const csvRecords: Array<{ originalOrderId: string; newOrderId: string }> =
      [];

    console.log(
      '🔍 Searching for prescriptions with "Take as directed" sent to Epiq Scripts...\n',
    );

    // Pre-fetch all core product mappings
    const coreProductMappings = await this.fetchCoreProductMappings();
    console.log(
      `📦 Loaded ${coreProductMappings.size} core product mappings\n`,
    );

    // Find all PharmacyIntegration records for Epiq Scripts that have rx_items with "Take as directed"
    const allRecords = await this.prisma.pharmacyIntegration.findMany({
      where: {
        pharmacyId: this.EPIQ_SCRIPTS_PHARMACY_ID,
        request: {
          path: ['rx_items'],
          array_contains: [
            {
              medication_sig: 'Take as directed',
            },
          ],
        },
        responseStatus: { not: 0 },
      },
      include: {
        prescription: {
          include: {
            product: true,
            patient: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true,
                  },
                },
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Filter records that have core medication rx_items with "Take as directed"
    const affectedRecords = [];
    for (const record of allRecords) {
      const request = record.request as any;
      const rxItems = request.rx_items as Array<{
        medication_name: string;
        medication_sig: string;
        rx_id: string;
      }>;

      let hasCoreWithTakeAsDirected = false;

      for (const rxItem of rxItems) {
        if (
          rxItem.medication_sig === 'Take as directed' &&
          coreProductMappings.has(rxItem.rx_id)
        ) {
          hasCoreWithTakeAsDirected = true;
          break;
        }
      }

      if (hasCoreWithTakeAsDirected) {
        affectedRecords.push(record);
      }
    }

    if (affectedRecords.length === 0) {
      console.log(
        '✅ No prescriptions found with "Take as directed" for core medications in Epiq Scripts.',
      );
      return;
    }

    console.log(
      `##### Found ${affectedRecords.length} prescriptions sent to Epiq Scripts with 'Take as directed' for core medications\n`,
    );

    if (limit && affectedRecords.length > limit) {
      console.log(
        `\n🔧 Limited to processing ${limit} prescriptions for this run.`,
      );
      affectedRecords.splice(limit);
    }

    console.log(
      isDryRun
        ? '\n🏃‍♂️ Dry run mode - showing corrections without sending to API...\n'
        : '\n🚀 Starting to resend prescriptions with corrected medication signatures...\n',
    );

    let successCount = 0;
    let errorCount = 0;

    for (const record of affectedRecords) {
      try {
        console.log(`\n📤 Processing prescription ${record.prescriptionId}...`);

        // Get the proper medication signature from ProductPriceMapping
        const correctedRequest = await this.getCorrectedRequest(
          record,
          coreProductMappings,
        );

        if (!correctedRequest) {
          console.log(
            `  ❌ Could not find proper medication signature for prescription ${record.prescriptionId}`,
          );
          errorCount++;
          continue;
        }

        if (isDryRun) {
          // In dry run mode, just show what would be sent
          console.log(
            `  ✅ Would resend prescription ${record.prescriptionId} with corrections`,
          );
          successCount++;
        } else {
          // Actually send the request
          const response = await this.httpClient.post(
            '/api/receivedPrescription',
            correctedRequest,
            {
              headers: { 'Content-Type': 'application/json' },
            },
          );

          if (response.status === 200 || response.status === 201) {
            console.log(
              `  ✅ Successfully resent prescription ${record.prescriptionId}`,
            );

            // Update original PharmacyIntegration record with responseStatus = 0
            await this.prisma.pharmacyIntegration.update({
              where: { id: record.id },
              data: { responseStatus: 0 },
            });

            // Log the new successful integration
            await this.logNewPharmacyIntegration(
              record,
              correctedRequest,
              response.data,
              response.status,
            );

            // Track successful resend for CSV
            csvRecords.push({
              originalOrderId: record.orderId,
              newOrderId: correctedRequest.order_id,
            });

            successCount++;

            // Add delay between requests (skip on last item)
            if (affectedRecords.indexOf(record) < affectedRecords.length - 1) {
              await this.sleep(delay);
            }
          } else {
            console.log(
              `  ❌ Unexpected response status ${response.status} for prescription ${record.prescriptionId}`,
            );
            errorCount++;
          }
        }
      } catch (error) {
        console.log(
          `  ❌ Failed to resend prescription ${record.prescriptionId}: ${error.message}`,
        );
        errorCount++;
      }
    }

    console.log(
      isDryRun ? '\n🎯 Dry run complete:' : '\n🎯 Resending complete:',
    );
    console.log(
      isDryRun
        ? `  ✅ Would resend: ${successCount} prescriptions`
        : `  ✅ Successfully resent: ${successCount} prescriptions`,
    );
    console.log(
      isDryRun
        ? `  ❌ Would fail: ${errorCount} prescriptions`
        : `  ❌ Failed to resend: ${errorCount} prescriptions`,
    );

    // Generate CSV file if there were successful resends and not in dry run mode
    if (!isDryRun && csvRecords.length > 0) {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const csvFilename = `epiq-resent-prescriptions-${timestamp}.csv`;

      // Create CSV content with headers
      const csvContent = [
        'Original Order ID,New Order ID',
        ...csvRecords.map(
          (record) => `${record.originalOrderId},${record.newOrderId}`,
        ),
      ].join('\n');

      // Write CSV file
      try {
        writeFileSync(csvFilename, csvContent);
        console.log(`\n📄 CSV file generated: ${csvFilename}`);
      } catch (error) {
        console.log(`\n❌ Failed to write CSV file: ${error.message}`);
      }
    }
  }

  private async getCorrectedRequest(
    record: any,
    coreProductMappings: Map<string, string>,
  ): Promise<any | null> {
    const originalRequest = record.request as any;

    // Clone the original request
    const correctedRequest = JSON.parse(JSON.stringify(originalRequest));
    let hasCorrections = false;

    // Update medication_sig only for core medication rx_items that have "Take as directed"
    correctedRequest.rx_items = correctedRequest.rx_items.map((rxItem: any) => {
      if (
        rxItem.medication_sig === 'Take as directed' &&
        coreProductMappings.has(rxItem.rx_id)
      ) {
        // This is a core medication that needs correction
        const properDirections = coreProductMappings.get(rxItem.rx_id)!;
        hasCorrections = true;
        return {
          ...rxItem,
          medication_sig: properDirections,
        };
      }
      return rxItem;
    });

    if (!hasCorrections) {
      console.warn(
        `  ⚠️  No core medications found that need correction for prescription ${record.prescriptionId}`,
      );
      return null;
    }

    // Update the order ID to append -resent
    correctedRequest.order_id = `${correctedRequest.order_id}-resent`;

    return correctedRequest;
  }

  private async logNewPharmacyIntegration(
    originalRecord: any,
    request: any,
    responseData: any,
    responseStatus: number,
  ): Promise<void> {
    try {
      await this.prisma.pharmacyIntegration.create({
        data: {
          pharmacyId: this.EPIQ_SCRIPTS_PHARMACY_ID,
          prescriptionId: originalRecord.prescriptionId,
          orderId: request.order_id,
          request,
          response: responseData || {},
          responseStatus,
        },
      });
    } catch (error) {
      console.log(
        `  ⚠️  Failed to log new pharmacy integration: ${error.message}`,
      );
    }
  }
}
