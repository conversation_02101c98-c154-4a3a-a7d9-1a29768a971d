// save this as scripts/restart-localtunnel.js
const { spawn, exec } = require('child_process');
const os = require('os');

// Get username for subdomain
const username =
  process.env.USER || process.env.USERNAME || os.userInfo().username;

// Get worktree name from environment if available
const worktreeName = process.env.WORKTREE_NAME;

// Construct subdomain based on whether we're in a worktree or main branch
const subdomain = worktreeName
  ? `willow-api-${username}-${worktreeName}`
  : `willow-api-${username}`;

const PORT = process.env.API_PORT;

// Log configuration details
console.log(`Localtunnel configuration:`);
console.log(`  User: ${username}`);
console.log(`  Worktree: ${worktreeName || '(default branch)'}`);
console.log(`  Port: ${PORT}`);
console.log(`  Subdomain: ${subdomain}`);

// Keep track of the current tunnel process
let currentProcess = null;
let isRestarting = false;
let retryCount = 0;
const MAX_RETRIES = 3;
const RETRY_DELAY = 5000; // 5 seconds
const KILL_TIMEOUT = 2000; // 2 seconds to kill process

// Set max listeners to avoid warning
process.setMaxListeners(20);

// Clean up any stray processes on startup
const cleanupStrayProcesses = () => {
  exec(
    `ps aux | grep "lt --port ${PORT}" | grep -v grep | awk '{print $2}'`,
    (error, stdout) => {
      if (stdout.trim()) {
        const pids = stdout.trim().split('\n');
        console.log(
          `Found ${pids.length} stray localtunnel processes. Cleaning up...`,
        );
        pids.forEach((pid) => {
          try {
            process.kill(parseInt(pid.trim()), 'SIGTERM');
          } catch (e) {
            // Ignore errors killing processes
          }
        });
      }
    },
  );
};

// Properly kill the process and all its children
const killProcess = (proc) => {
  return new Promise((resolve) => {
    if (!proc) return resolve();

    try {
      // Try gentle SIGTERM first
      proc.kill('SIGTERM');

      // Set timeout for forceful kill
      const forceKillTimeout = setTimeout(() => {
        try {
          proc.kill('SIGKILL');
        } catch (e) {
          // Process might already be dead
        }
        resolve();
      }, KILL_TIMEOUT);

      // If process exits cleanly, clear the timeout
      proc.on('exit', () => {
        clearTimeout(forceKillTimeout);
        resolve();
      });
    } catch (e) {
      // Process might already be dead
      resolve();
    }
  });
};

// Simplified error logging
const logError = (message) => {
  // Extract just the first line of error
  const firstLine = message.split('\n')[0];
  // Check if it's the common connection refused error
  if (firstLine.includes('Error: connection refused')) {
    console.log('Connection error: Tunnel server connection refused');
    return true;
  } else if (message.includes('Error:')) {
    // For other errors, just show a simplified message
    console.log('Localtunnel error detected');
    return true;
  }
  return false;
};

function startTunnel() {
  if (isRestarting) return;
  isRestarting = true;

  // Reset retry count on successful connection
  const resetRetryCount = () => {
    retryCount = 0;
  };

  console.log(`\nStarting localtunnel...`);

  // Kill previous process if it exists
  if (currentProcess) {
    killProcess(currentProcess).then(() => {
      currentProcess = null;
      spawnNewProcess();
    });
  } else {
    spawnNewProcess();
  }

  function spawnNewProcess() {
    // Add a small random delay to avoid port conflicts on rapid restarts
    const randomDelay = Math.floor(Math.random() * 500);
    setTimeout(() => {
      currentProcess = spawn(
        'lt',
        ['--port', PORT.toString(), '--subdomain', subdomain],
        {
          stdio: ['ignore', 'pipe', 'pipe'],
        },
      );

      let urlFound = false;
      let hasError = false;

      currentProcess.stdout.on('data', (data) => {
        const output = data.toString();

        // Only log the URL line
        if (output.includes('.loca.lt')) {
          console.log(output.trim());

          // Check if we got our desired URL
          if (output.includes(`https://${subdomain}.loca.lt`)) {
            urlFound = true;
            resetRetryCount(); // Reset retry count on successful connection
          } else if (!urlFound) {
            // If we got a different URL than requested, kill and retry
            console.log('Wrong subdomain assigned! Restarting to try again...');
            restartTunnel();
          }
        }
      });

      currentProcess.stderr.on('data', (data) => {
        const errorMsg = data.toString();

        if (logError(errorMsg)) {
          hasError = true;
          restartTunnel();
        }
      });

      currentProcess.on('close', (code) => {
        // Only restart on abnormal termination if not already restarting
        if (code !== 0 && code !== null && !hasError && !isRestarting) {
          console.log(`Localtunnel process exited unexpectedly`);
          restartTunnel();
        }

        isRestarting = false;
      });

      // Reset the restarting flag after a reasonable timeout
      setTimeout(() => {
        isRestarting = false;
      }, RETRY_DELAY * 2);
    }, randomDelay);
  }
}

function restartTunnel() {
  if (isRestarting) return;

  retryCount++;

  if (retryCount > MAX_RETRIES) {
    console.log(
      `Failed after ${MAX_RETRIES} retries. Waiting longer before next attempt...`,
    );
    setTimeout(() => {
      retryCount = 0;
      isRestarting = false;
      startTunnel();
    }, RETRY_DELAY * 4); // Longer backoff
    return;
  }

  console.log(`Restarting tunnel... (Attempt ${retryCount}/${MAX_RETRIES})`);
  isRestarting = true;

  setTimeout(() => {
    startTunnel();
  }, RETRY_DELAY);
}

// Handle process termination signals
process.on('SIGINT', async () => {
  console.log('Shutting down localtunnel...');
  if (currentProcess) {
    await killProcess(currentProcess);
  }
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Shutting down localtunnel...');
  if (currentProcess) {
    await killProcess(currentProcess);
  }
  process.exit(0);
});

// Clean up stray processes before starting
cleanupStrayProcesses();

// Start the tunnel for the first time
setTimeout(startTunnel, 1000);
