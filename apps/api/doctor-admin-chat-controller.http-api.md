# DoctorAdminChatController HTTP API Documentation

Base path: `/doctor-admin-chat`

## Authentication & Roles

- All endpoints require JWT authentication.
- Role-based access: Most endpoints require `Admin` or `Doctor` roles. Some are restricted to `Admin` only.

---

### 1. Create a Conversation

- **Endpoint:** `POST /doctor-admin-chat/conversations`
- **Roles:** Admin, Doctor
- **Body:**
  ```json
  {
    "patientId": "string",
    "doctorUserId": "string"
  }
  ```
- **Returns:**
  ```json
  {
    "conversationId": "string"
  }
  ```

---

### 2. Get Admin Conversations

- **Endpoint:** `GET /doctor-admin-chat/conversations`
- **Roles:** Admin
- **Query Parameters:**
  - `filter` (optional, default: `all`): `"myInbox" | "all" | "unassigned" | "closed"`
  - `page` (optional, default: `1`): integer (page number)
  - `limit` (optional, default: `20`): integer (1-100)
- **Returns:** List of conversations .

---

## GET /doctor-admin-chat/conversations

Returns a paginated list of doctor-admin conversations with detailed information.

### Response Structure

```json
{
  "conversation": [
    {
      "id": "string",
      "patientId": "string",
      "status": "open | closed",
      "assignedAdminId": "string | null",
      "lastMessageText": "string | null",
      "createdAt": "ISO8601 string",
      "updatedAt": "ISO8601 string",
      "closedAt": "ISO8601 string | null",
      "patient": {
        "id": "string",
        "user": {
          "id": "string",
          "firstName": "string",
          "lastName": "string",
          "email": "string"
        },
        "doctor": {
          "user": {
            "id": "string",
            "firstName": "string",
            "lastName": "string"
          }
        }
      },
      "assignedAdmin": {
        "id": "string",
        "firstName": "string",
        "lastName": "string"
      } | null,
      "lastMessage": {
        "id": "string",
        "content": "string",
        "createdAt": "ISO8601 string",
        "user": {
          "id": "string",
          "firstName": "string",
          "lastName": "string",
          "type": "string"
        },
        "conversationId": "string",
        "type": "string", // e.g. 'message', 'system', etc.
        "needsReply": false,
        "read": true,
        "updatedAt": "ISO8601 string"
      } | null,
      "unreadMessages": 0
    }
    // ...more conversations
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 2,
    "totalCount": 25,
    "hasNextPage": true,
    "hasPreviousPage": false
  }
}
```

#### Field Notes

- `status`: "open" or "closed".
- `assignedAdminId` and `assignedAdmin` can be `null` if unassigned.
- `lastMessageText` and `closedAt` can be `null`.
- `lastMessage` is the most recent message or `null` if no messages exist.
- `unreadMessages`: integer (number of unread messages for the admin).
- `pagination`: metadata for paginated results.

---

### 3. Assign a Conversation

- **Endpoint:** `PUT /doctor-admin-chat/conversations/:conversationId/assign`
- **Roles:** Admin
- **Params:**
  - `conversationId`: string
- **Body (optional):**
  ```json
  {
    "adminUserId": "string" // If omitted, uses the current user
  }
  ```
- **Returns:** Assignment result (structure depends on implementation).

---

### 4. Unassign a Conversation

- **Endpoint:** `PUT /doctor-admin-chat/conversations/:conversationId/unassign`
- **Roles:** Admin
- **Params:**
  - `conversationId`: string
- **Returns:** Unassignment result.

---

### 5. Close a Conversation

- **Endpoint:** `PUT /doctor-admin-chat/conversations/:conversationId/close`
- **Roles:** Admin
- **Params:**
  - `conversationId`: string
- **Returns:** Close result.

---

### 6. Reopen a Conversation

- **Endpoint:** `PUT /doctor-admin-chat/conversations/:conversationId/reopen`
- **Roles:** Admin
- **Params:**
  - `conversationId`: string
- **Returns:** Reopen result.

---

### 7. Send a Message

- **Endpoint:** `POST /doctor-admin-chat/:conversationId/send`
- **Roles:** Admin, Doctor
- **Params:**
  - `conversationId`: string
- **Body:**
  ```json
  {
    "contentType": "text | image | file", // required, type of the message content
    "content": "string", // required, the message content (text or URL)
    "needsReply": false // Ignored, always false for admin doctor convo, keep it for compatibility
  }
  ```
- **Returns:** Message send result.

---

### 8. Get Conversation Messages

- **Endpoint:** `GET /doctor-admin-chat/conversations/:conversationId`
- **Roles:** Admin, Doctor
- **Params:**
  - `conversationId`: string
- **Returns:**
  ```json
  {
    "id": "string",
    "type": "doctorAdmin",
    "status": "open | closed",
    "createdAt": "ISO8601 string",
    "updatedAt": "ISO8601 string",
    "closedAt": "ISO8601 string | null",
    "patient": {
      "id": "string",
      "user": {
        "id": "string",
        "firstName": "string",
        "lastName": "string",
        "email": "string"
      },
      "doctor": {
        "id": "string",
        "user": {
          "id": "string",
          "firstName": "string",
          "lastName": "string",
          "type": "doctor"
        }
      }
    },
    "assignedAdmin": {
      "id": "string",
      "firstName": "string",
      "lastName": "string"
    } | null,
    "messages": [
      {
        "id": "string",
        "content": "string",
        "contentType": "text | image | file",
        "createdAt": "ISO8601 string",
        "needsReply": false,
        "user": {
          "id": "string",
          "firstName": "string",
          "lastName": "string",
          "type": "admin | doctor"
        }
      }
      // ...more messages
    ],
    "participants": {
      "<userId>": {
        "id": "string", // doctor/admin id
        "user": {
          "id": "string",
          "firstName": "string",
          "lastName": "string",
          "type": "admin | doctor"
        },
        "imageUrl": "string" // profile image URL (doctor) or empty for admin
      }
      // ...more participants
    }
  }
  ```

---

### 9. Mark All Messages as Read

- **Endpoint:** `POST /doctor-admin-chat/:conversationId/read`
- **Roles:** Admin, Doctor
- **Params:**
  - `conversationId`: string
- **Returns:** Mark as read result.

---

## Notes

- All endpoints expect and return JSON.
- Error responses follow standard HTTP error codes and may include a message field.
- The actual structure of returned objects (conversations, messages) depends on the backend implementation.

Let me know if you need example requests or more details for any endpoint.
