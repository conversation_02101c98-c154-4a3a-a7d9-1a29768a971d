# Willow Project Overview

Willow is a healthcare web app for GLP-1 medications (like Semaglutide) with patient onboarding, doctor verification,
and prescription management. The platform follows domain-driven design with XState for state management.

## Architecture

- **Monorepo**: Turborepo with 3 Next.js frontends + Nest.js backend
- **Database**: Postgres with Prisma
- **UI**: Tai<PERSON>wind, Shadcn, Lucide icons
- **State**: React Query v5, XState v5, Joitai
- **Integrations**: Stripe (payments), DoseSpot (pharmacy), Segment (analytics)
- **Infrastructure**: Docker Compose (local), AWS (ECS Fargate for backend, Amplify for frontends)

## Key Commands

```bash
# Setup
pnpm i                                  # Install dependencies
pnpm add -F {app} {dependency}          # Add dependencies

# Development, check ports on `.env`: ADMIN_URL, DOCTORS_URL, PATIENTS_URL, API_PORT
pnpm dev:api                            # Start API with hot reloading for shared packages
pnpm -F patients dev                    # Patient frontend
pnpm -F doctors dev                     # Doctor frontend
pnpm -F admin dev                       # Admin frontend

# Database
docker compose up -d                    # Start database and Redis
pnpm -F db migrate --name {migration}   # DONT RUN MIGRATIONS! pause and output the cli line to run the migration myself
pnpm -F restore-database start --name=clean_slate  # Initialize database from clean slate

# Output from the API, _prepend_ with `command`!!
`command tmux capture-pane -t willow-glp90:api -p -S -{lines}`

# Code Quality, run this globally
pnpm format:fix                         # Fix formatting
pnpm lint                               # Lint code
pnpm typecheck                          # Check TypeScript types
```

## Code Conventions

- TypeScript with strict typing
- Import types with `import type { Type } from 'module'`
- Use interfaces for type definitions
- No unused variables (prefix with _ if needed)
- No direct process.env access (use environment modules)
- No non-null assertions (!)
- Explicit error handling with try/catch
- PascalCase for components, camelCase for utilities
- Organized imports (external first, then internal)

## Instructions

Check your current branch name for the pattern `PROD-###`, for example `PROD-1104`. and in
`resources/tickets/{custom-id}.md` if there's a ticket description to follow. If there is, read it, follow the
instructions, and keep it updated with your progress.

## Testing

### E2E Manual Testing

- use Puppeteer MCP
- have all 3 FE apps and api running
- `pnpm -F onboard start --name ramiro --number 1` to onboard a patient, capture email from stdout
- `pnpm -F backup-database start --env local --name ${name}` to backup the database for manual testing
- `pnpm -F restore-database start --name ${name}` to restore the database for manual testing

#### credentials

Make a POST request to the following endpoints with the following bodies, and store the accessToken in the JSON
response:

```
https://localhost:API_PORT/admin/signIn
{ email: "<EMAIL>", password: "somePassword1234$" }

https://localhost:API_PORT/doctor/signIn
{ email: "<EMAIL>", password: "somePassword1234$" }

https://localhost:API_PORT/patient/signIn
{ email: "<EMAIL>", password: "somePassword1234$" }
```

## Github

- Org: Willow-Health-Services
- Repo: willow-monorepo

## Datadog

- site: us5.datadoghq.com

## ClickUp

- team id: **********
- generate branch names like: `{custom-id}-{ticket-title-in-kebab-case}`
- add testing instructions to the bottom of the description, like:

```
### Testing

1. step 1
2. step 2
...
```

## Commit Format

Short, descriptive commits with scope:

- `feat(api): add endpoint to cancel patient`
- `fix(patients): repair cancel button functionality`
- `chore(doctors): resolve lint issues`
- `refactor(tools): improve utility functions`

## PR Template

Title: `{ticket-id} - {meaningful-title}`, for example: `PROD-1104 - Cancel patient button`

```markdown
# Context 📝

https://app.clickup.com/t/**********/{ticket-id}

# What Changed 🔧

- {Short bullet list of changes}

# Verification ✅

1. {Steps to verify changes}
```

Don't add any indication of Claude in the PR

# Using Gemini CLI for Large Codebase Analysis

When analyzing large codebases or multiple files that might exceed context limits, use the Gemini CLI with its massive
context window. Use `gemini -p` to leverage Google Gemini's large context capacity.

## File and Directory Inclusion Syntax

Use the `@` syntax to include files and directories in your Gemini prompts. The paths should be relative to WHERE you
run the
gemini command:

### Examples:

**Single file analysis:**
gemini -p "@src/main.py Explain this file's purpose and structure"

Multiple files:
gemini -p "@package.json @src/index.js Analyze the dependencies used in the code"

Entire directory:
gemini -p "@src/ Summarize the architecture of this codebase"

Multiple directories:
gemini -p "@src/ @tests/ Analyze test coverage for the source code"

Current directory and subdirectories:
gemini -p "@./ Give me an overview of this entire project"

#### Or use --all_files flag:

gemini --all_files -p "Analyze the project structure and dependencies"

### Implementation Verification Examples

Check if a feature is implemented:
gemini -p "@src/ @lib/ Has dark mode been implemented in this codebase? Show me the relevant files and functions"

Verify authentication implementation:
gemini -p "@src/ @middleware/ Is JWT authentication implemented? List all auth-related endpoints and middleware"

Check for specific patterns:
gemini -p "@src/ Are there any React hooks that handle WebSocket connections? List them with file paths"

Verify error handling:
gemini -p "@src/ @api/ Is proper error handling implemented for all API endpoints? Show examples of try-catch blocks"

Check for rate limiting:
gemini -p "@backend/ @middleware/ Is rate limiting implemented for the API? Show the implementation details"

Verify caching strategy:
gemini -p "@src/ @lib/ @services/ Is Redis caching implemented? List all cache-related functions and their usage"

Check for specific security measures:
gemini -p "@src/ @api/ Are SQL injection protections implemented? Show how user inputs are sanitized"

Verify test coverage for features:
gemini -p "@src/payment/ @tests/ Is the payment processing module fully tested? List all test cases"

### When to Use Gemini CLI

Use gemini -p when:

- Analyzing entire codebases or large directories
- Comparing multiple large files
- Need to understand project-wide patterns or architecture
- Current context window is insufficient for the task
- Working with files totaling more than 100KB
- Verifying if specific features, patterns, or security measures are implemented
- Checking for the presence of certain coding patterns across the entire codebase

### Important Notes

- Paths in @ syntax are relative to your current working directory when invoking gemini
- The CLI will include file contents directly in the context
- No need for --yolo flag for read-only analysis
- Gemini's context window can handle entire codebases that would overflow Claude's context
- When checking implementations, be specific about what you're looking for to get accurate results
