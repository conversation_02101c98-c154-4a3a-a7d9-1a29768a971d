{"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "eslint.rules.customizations": [{"rule": "*", "severity": "warn"}], "eslint.useFlatConfig": true, "eslint.workingDirectories": [{"pattern": "apps/*/"}, {"pattern": "packages/*/"}, {"pattern": "tooling/*/"}], "prettier.ignorePath": ".giti<PERSON>re", "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]], "tailwindCSS.experimental.configFile": "./config/tailwind/web.ts", "typescript.enablePromptUseWorkspaceTsdk": true, "typescript.preferences.autoImportFileExcludePatterns": ["next/router.d.ts", "next/dist/client/router.d.ts"], "typescript.tsdk": "node_modules/typescript/lib", "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[xml]": {"editor.defaultFormatter": "redhat.vscode-xml"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[prisma]": {"editor.defaultFormatter": "Prisma.prisma"}}